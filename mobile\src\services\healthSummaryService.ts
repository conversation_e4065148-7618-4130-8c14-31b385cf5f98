import { apiService } from './api'; export interface HealthMetric { id: number; name: string; value: string; unit: string; status: 'normal' | 'warning' | 'critical'; lastUpdated: string; trend: 'up' | 'down' | 'stable'; icon: string; reference_range?: string; notes?: string; } export interface VitalSign { id: number; type: string; value: string; unit: string; recorded_date: string; recorded_by: string; notes?: string; } export interface HealthAlert { id: number; title: string; message: string; priority: 'low' | 'medium' | 'high'; type: 'medication' | 'appointment' | 'test' | 'general'; due_date?: string; is_read: boolean; created_at: string; } export interface HealthGoal { id: number; title: string; description: string; target_value: string; current_value: string; unit: string; target_date: string; progress_percentage: number; status: 'active' | 'completed' | 'paused'; created_at: string; } export interface CreateHealthMetricData { name: string; value: string; unit: string; notes?: string; } export interface CreateVitalSignData { type: string; value: string; unit: string; notes?: string; } class HealthSummaryService { // Health Metrics async getHealthMetrics(): Promise<HealthMetric[]> { return apiService.get<HealthMetric[]>(`/health/metrics/`); } async createHealthMetric(metricData: CreateHealthMetricData): Promise<HealthMetric> { return apiService.post<HealthMetric>(`/health/metrics/`, metricData); } async updateHealthMetric(id: number, metricData: Partial<CreateHealthMetricData>): Promise<HealthMetric> { return apiService.patch<HealthMetric>(`/health/metrics/${id}/`, metricData); } async deleteHealthMetric(id: number): Promise<{ message: string }> { return apiService.delete<{ message: string }>(`/health/metrics/${id}/`); } // Vital Signs async getVitalSigns(): Promise<VitalSign[]> { return apiService.get<VitalSign[]>(`/health/vital-signs/`); } async createVitalSign(vitalData: CreateVitalSignData): Promise<VitalSign> { return apiService.post<VitalSign>(`/health/vital-signs/`, vitalData); } async getVitalSignsByType(type: string): Promise<VitalSign[]> { return apiService.get<VitalSign[]>(`/health/vital-signs/type/${type}/`); } async getVitalSignsByDateRange(startDate: string, endDate: string): Promise<VitalSign[]> { return apiService.get<VitalSign[]>(`/health/vital-signs/date-range/?start_date=${startDate}&end_date=${endDate}`); } // Health Alerts async getHealthAlerts(): Promise<HealthAlert[]> { return apiService.get<HealthAlert[]>(`/health/alerts/`); } async markAlertAsRead(id: number): Promise<HealthAlert> { return apiService.post<HealthAlert>(`/health/alerts/${id}/mark-read/`); } async dismissAlert(id: number): Promise<{ message: string }> { return apiService.delete<{ message: string }>(`/health/alerts/${id}/`); } // Health Goals async getHealthGoals(): Promise<HealthGoal[]> { return apiService.get<HealthGoal[]>(`/health/goals/`); } async createHealthGoal(goalData: any): Promise<HealthGoal> { return apiService.post<HealthGoal>(`/health/goals/`, goalData); } async updateHealthGoal(id: number, goalData: any): Promise<HealthGoal> { return apiService.patch<HealthGoal>(`/health/goals/${id}/`, goalData); } async deleteHealthGoal(id: number): Promise<{ message: string }> { return apiService.delete<{ message: string }>(`/health/goals/${id}/`); } // Health Summary and Analytics async getHealthSummary(): Promise<any> { return apiService.get<any>(`/health/summary/`); } async getHealthTrends(period: 'week' | 'month' | 'year'): Promise<any> { return apiService.get<any>(`/health/trends/?period=${period}`); } async exportHealthData(format: 'pdf' | 'csv'): Promise<Blob> { return apiService.get<Blob>(`/health/export/?format=${format}`, { responseType: 'blob' }); } } export const healthSummaryService = new HealthSummaryService(); 