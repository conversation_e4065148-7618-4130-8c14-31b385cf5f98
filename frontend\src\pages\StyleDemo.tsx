import React from 'react'; import { useDispatch } from 'react-redux'; import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'; import { Button } from '../components/ui/Button'; import ThemeToggle from '../components/ui/ThemeToggle'; import { useTheme } from '../hooks/useTheme'; import { setGlassmorphismLevel } from '../store/slices/themeSlice'; import type { GlassmorphismLevel } from '../store/slices/themeSlice'; import { Palette, Sun, Moon, Monitor, Sparkles, Zap, Heart, Star, Settings, User, Bell, Shield } from 'lucide-react'; const StyleDemo: React.FC = () => { const dispatch = useDispatch(); const { mode, effectiveTheme, glassmorphismLevel, isDark } = useTheme(); const handleGlassmorphismChange = (level: GlassmorphismLevel) => { dispatch(setGlassmorphismLevel(level)); }; return ( <div className="min-h-screen bg-background p-6"> <div className="max-w-7xl mx-auto space-y-8"> {/* Header */} <div className="text-center space-y-4"> <h1 className="text-4xl font-bold text-foreground"> HMS Styling System Demo </h1> <p className="text-lg text-muted-foreground"> Showcasing the unified macOS-style design system with glassmorphism effects </p> {/* Theme Controls */} <div className="flex justify-center items-center gap-4 flex-wrap"> <div className="flex items-center gap-2"> <span className="text-sm text-muted-foreground">Theme:</span> <ThemeToggle variant="dropdown" /> </div> <div className="flex items-center gap-2"> <span className="text-sm text-muted-foreground">Current:</span> <span className="text-sm font-medium text-foreground"> {mode} {mode === 'system' && `(${effectiveTheme})`} </span> </div> </div> </div> {/* Glassmorphism Controls */} <Card className="macos-card"> <CardHeader> <CardTitle className="flex items-center gap-2"> <Sparkles className="w-5 h-5 text-blue-500" /> Glassmorphism Level </CardTitle> </CardHeader> <CardContent> <div className="grid grid-cols-4 gap-4"> {(['none', 'subtle', 'medium', 'strong'] as GlassmorphismLevel[]).map((level) => ( <button key={level} onClick={() => handleGlassmorphismChange(level)} className={` p-4 rounded-lg text-sm font-medium transition-all duration-200 ${glassmorphismLevel === level ? 'bg-primary text-primary-foreground shadow-md scale-105' : 'bg-secondary text-secondary-foreground hover:bg-accent hover:scale-102' } `} > {level.charAt(0).toUpperCase() + level.slice(1)} </button> ))} </div> <p className="text-sm text-muted-foreground mt-4"> Current level: <span className="font-medium">{glassmorphismLevel}</span> </p> </CardContent> </Card> {/* Component Showcase */} <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> {/* Buttons */} <Card className="macos-card"> <CardHeader> <CardTitle>Button Variants</CardTitle> </CardHeader> <CardContent className="space-y-3"> <Button variant="default">Default Button</Button> <Button variant="secondary">Secondary Button</Button> <Button variant="outline">Outline Button</Button> <Button variant="ghost">Ghost Button</Button> <Button variant="glass">Glass Button</Button> <Button variant="glass-primary">Glass Primary</Button> </CardContent> </Card> {/* Cards */} <Card variant="glass"> <CardHeader> <CardTitle>Glass Card</CardTitle> </CardHeader> <CardContent> <p className="text-muted-foreground"> This card uses glassmorphism effects that adapt to your selected level. </p> </CardContent> </Card> <Card variant="elevated"> <CardHeader> <CardTitle>Elevated Card</CardTitle> </CardHeader> <CardContent> <p className="text-muted-foreground"> This card has enhanced shadows for depth. </p> </CardContent> </Card> {/* Icons Grid */} <Card className="macos-card"> <CardHeader> <CardTitle>Icon Colors</CardTitle> </CardHeader> <CardContent> <div className="grid grid-cols-4 gap-4"> <div className="flex flex-col items-center gap-2"> <Heart className="w-6 h-6 text-red-500" /> <span className="text-xs text-muted-foreground">Red</span> </div> <div className="flex flex-col items-center gap-2"> <Star className="w-6 h-6 text-yellow-500" /> <span className="text-xs text-muted-foreground">Yellow</span> </div> <div className="flex flex-col items-center gap-2"> <Zap className="w-6 h-6 text-blue-500" /> <span className="text-xs text-muted-foreground">Blue</span> </div> <div className="flex flex-col items-center gap-2"> <Sparkles className="w-6 h-6 text-purple-500" /> <span className="text-xs text-muted-foreground">Purple</span> </div> </div> </CardContent> </Card> {/* Status Badges */} <Card className="macos-card"> <CardHeader> <CardTitle>Status Badges</CardTitle> </CardHeader> <CardContent className="space-y-3"> <div className="flex flex-wrap gap-2"> <span className="badge-success">Active</span> <span className="badge-warning">Pending</span> <span className="badge-default">Inactive</span> <span className="status-completed">Completed</span> </div> </CardContent> </Card> {/* Theme Status */} <Card className="macos-card"> <CardHeader> <CardTitle>Theme Status</CardTitle> </CardHeader> <CardContent className="space-y-3"> <div className="flex items-center justify-between"> <span className="text-sm text-muted-foreground">Dark Mode:</span> <span className={`text-sm font-medium ${isDark ? 'text-green-500' : 'text-gray-500'}`}> {isDark ? 'Enabled' : 'Disabled'} </span> </div> <div className="flex items-center justify-between"> <span className="text-sm text-muted-foreground">Glassmorphism:</span> <span className="text-sm font-medium text-foreground capitalize"> {glassmorphismLevel} </span> </div> <div className="flex items-center justify-between"> <span className="text-sm text-muted-foreground">System Theme:</span> <span className="text-sm font-medium text-foreground"> {effectiveTheme} </span> </div> </CardContent> </Card> </div> {/* Footer */} <div className="text-center py-8"> <p className="text-muted-foreground"> HMS Styling System - macOS-inspired design with glassmorphism effects </p> </div> </div> </div> ); }; export default StyleDemo; 