import { configureStore } from '@reduxjs/toolkit'; import authSlice from './slices/authSlice'; import userSlice from './slices/userSlice'; import patientSlice from './slices/patientSlice'; import appointmentSlice from './slices/appointmentSlice'; import aiSlice from './slices/aiSlice'; import themeSlice from './slices/themeSlice'; export const store = configureStore({ reducer: { auth: authSlice, user: userSlice, patient: patientSlice, appointment: appointmentSlice, ai: aiSlice, theme: themeSlice, }, middleware: (getDefaultMiddleware) => getDefaultMiddleware({ serializableCheck: { ignoredActions: ['persist/PERSIST'], }, }), }); export type RootState = ReturnType<typeof store.getState>; export type AppDispatch = typeof store.dispatch; 