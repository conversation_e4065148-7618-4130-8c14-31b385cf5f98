import React from 'react';
import { useTheme } from '../../hooks/useTheme';
import { Button } from '../ui/button';

const SimpleLandingPage: React.FC = () => {
  const { mode, effectiveTheme, toggleTheme } = useTheme();

  return (
    <div className="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900">
      {/* Header */}
      <header className="glass border-b border-border/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">H</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">HMS AI</h1>
                <p className="text-sm text-muted-foreground">Healthcare Management</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-muted-foreground">
                Mode: <strong>{mode}</strong> | Theme: <strong>{effectiveTheme}</strong>
              </div>
              <Button onClick={toggleTheme} variant="outline" size="sm">
                {effectiveTheme === 'dark' ? '☀️ Light' : '🌙 Dark'}
              </Button>
              <Button variant="default">
                Login
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center space-y-8">
          <div className="space-y-4">
            <h1 className="text-5xl font-bold text-foreground">
              Transform Healthcare with
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"> AI</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Experience the future of healthcare management with our intelligent AI-powered platform
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="px-8">
              Get Started
            </Button>
            <Button variant="outline" size="lg" className="px-8">
              Learn More
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-24 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="glass p-8 rounded-2xl text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">🏥</span>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-4">Smart Management</h3>
            <p className="text-muted-foreground">
              Streamline hospital operations with AI-powered management tools
            </p>
          </div>

          <div className="glass p-8 rounded-2xl text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">👨‍⚕️</span>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-4">Patient Care</h3>
            <p className="text-muted-foreground">
              Enhanced patient care through intelligent monitoring and alerts
            </p>
          </div>

          <div className="glass p-8 rounded-2xl text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">📊</span>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-4">Analytics</h3>
            <p className="text-muted-foreground">
              Data-driven insights for better healthcare decisions
            </p>
          </div>
        </div>

        {/* Status Demo */}
        <div className="mt-24">
          <div className="glass p-8 rounded-2xl">
            <h2 className="text-2xl font-bold text-foreground mb-8 text-center">
              🎨 Theme System Demo
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <div className="status-success p-4 rounded-lg text-center">
                <div className="font-medium">Success</div>
                <div className="text-sm opacity-75">Appointments</div>
              </div>
              <div className="status-warning p-4 rounded-lg text-center">
                <div className="font-medium">Warning</div>
                <div className="text-sm opacity-75">Pending</div>
              </div>
              <div className="status-error p-4 rounded-lg text-center">
                <div className="font-medium">Error</div>
                <div className="text-sm opacity-75">Critical</div>
              </div>
              <div className="status-info p-4 rounded-lg text-center">
                <div className="font-medium">Info</div>
                <div className="text-sm opacity-75">Updates</div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="glass-light p-6 rounded-lg text-center">
                <h3 className="font-medium text-foreground">Light Glass</h3>
                <p className="text-sm text-muted-foreground">Subtle effect</p>
              </div>
              <div className="glass p-6 rounded-lg text-center">
                <h3 className="font-medium text-foreground">Medium Glass</h3>
                <p className="text-sm text-muted-foreground">Balanced effect</p>
              </div>
              <div className="glass-heavy p-6 rounded-lg text-center">
                <h3 className="font-medium text-foreground">Heavy Glass</h3>
                <p className="text-sm text-muted-foreground">Strong effect</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="glass border-t border-border/50 mt-24">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p className="text-muted-foreground">
              © 2024 HMS AI. All rights reserved. | 
              <span className="ml-2">
                {effectiveTheme === 'dark' ? '🌙 Dark Mode' : '☀️ Light Mode'} Active
              </span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SimpleLandingPage;
