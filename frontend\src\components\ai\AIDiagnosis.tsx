import React, { useState } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Steth<PERSON>cope, Plus, X, Brain, AlertCircle, CheckCircle, Loader2, FileText, TrendingUp } from 'lucide-react'; import type { RootState, AppDispatch } from '../../store'; import { generateDiagnosis } from '../../store/slices/aiSlice'; interface AIDiagnosisProps { patientId: number; onDiagnosisGenerated?: (diagnosis: any) => void; } const AIDiagnosis: React.FC<AIDiagnosisProps> = ({ patientId, onDiagnosisGenerated }) => { const dispatch = useDispatch<AppDispatch>(); const { isGeneratingDiagnosis, lastDiagnosis, diagnosisError } = useSelector((state: RootState) => state.ai); const [symptoms, setSymptoms] = useState<string[]>(['']); const [additionalInfo, setAdditionalInfo] = useState(''); const [showResults, setShowResults] = useState(false); const addSymptom = () => { setSymptoms([...symptoms, '']); }; const removeSymptom = (index: number) => { if (symptoms.length > 1) { setSymptoms(symptoms.filter((_, i) => i !== index)); } }; const updateSymptom = (index: number, value: string) => { const newSymptoms = [...symptoms]; newSymptoms[index] = value; setSymptoms(newSymptoms); }; const handleGenerateDiagnosis = async () => { const validSymptoms = symptoms.filter(s => s.trim()); if (validSymptoms.length === 0) { alert('Please enter at least one symptom'); return; } try { const result = await dispatch(generateDiagnosis({ patient_id: patientId, symptoms: validSymptoms, additional_info: additionalInfo })).unwrap(); setShowResults(true); onDiagnosisGenerated?.(result); } catch (error) { console.error('Failed to generate diagnosis:', error); } }; const getConfidenceColor = (score: number) => { if (score >= 0.8) return 'text-emerald-700 dark:text-emerald-400 bg-green-100'; if (score >= 0.6) return 'text-amber-700 dark:text-amber-400 bg-yellow-100'; return 'text-rose-700 dark:text-rose-400 bg-red-100'; }; const getConfidenceLabel = (score: number) => { if (score >= 0.8) return 'High Confidence'; if (score >= 0.6) return 'Moderate Confidence'; return 'Low Confidence'; }; return ( <div className="space-y-6"> {/* Header */} <div className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-6 text-white"> <div className="flex items-center gap-3"> <div className="p-2 bg-background/20 rounded-lg"> <Stethoscope className="w-6 h-6" /> </div> <div> <h2 className="text-xl font-bold">AI Diagnosis Assistant</h2> <p className="text-purple-100"> Get AI-powered diagnostic suggestions based on symptoms </p> </div> </div> </div> {/* Input Form */} <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <h3 className="text-lg font-semibold text-foreground dark:text-white mb-4"> Enter Patient Symptoms </h3> {/* Symptoms */} <div className="space-y-3 mb-6"> <label className="block text-sm font-medium text-foreground dark:text-gray-300"> Symptoms * </label> {symptoms.map((symptom, index) => ( <div key={index} className="flex gap-2"> <input type="text" value={symptom} onChange={(e) => updateSymptom(index, e.target.value)} placeholder={`Symptom ${index + 1}`} className="flex-1 px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> {symptoms.length > 1 && ( <button onClick={() => removeSymptom(index)} className="p-2 text-rose-700 dark:text-rose-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors" > <X className="w-4 h-4" /> </button> )} </div> ))} <button onClick={addSymptom} className="flex items-center gap-2 px-3 py-2 text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors" > <Plus className="w-4 h-4" /> Add Another Symptom </button> </div> {/* Additional Information */} <div className="mb-6"> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Additional Information </label> <textarea value={additionalInfo} onChange={(e) => setAdditionalInfo(e.target.value)} placeholder="Duration, severity, triggers, or any other relevant information..." rows={3} className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> {/* Generate Button */} <button onClick={handleGenerateDiagnosis} disabled={isGeneratingDiagnosis || symptoms.every(s => !s.trim())} className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors" > {isGeneratingDiagnosis ? ( <> <Loader2 className="w-5 h-5 animate-spin" /> Analyzing Symptoms... </> ) : ( <> <Brain className="w-5 h-5" /> Generate AI Diagnosis </> )} </button> {diagnosisError && ( <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border rounded-lg"> <div className="flex items-center gap-2"> <AlertCircle className="w-5 h-5 text-rose-700 dark:text-rose-400 dark:text-red-400" /> <p className="text-rose-700 dark:text-rose-400 dark:text-red-400 font-medium"> Error generating diagnosis </p> </div> <p className="text-rose-700 dark:text-rose-400 dark:text-red-400 text-sm mt-1"> {diagnosisError} </p> </div> )} </div> {/* Results */} {showResults && lastDiagnosis && lastDiagnosis.success && ( <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <div className="flex items-center gap-3 mb-6"> <CheckCircle className="w-6 h-6 text-emerald-700 dark:text-emerald-400" /> <h3 className="text-lg font-semibold text-foreground dark:text-white"> AI Diagnosis Results </h3> </div> {/* Primary Diagnosis */} <div className="mb-6"> <div className="flex items-center justify-between mb-3"> <h4 className="font-medium text-foreground dark:text-white"> Primary Diagnosis </h4> <div className={`px-3 py-1 rounded-full text-xs font-medium ${getConfidenceColor(lastDiagnosis.confidence_score)}`}> {getConfidenceLabel(lastDiagnosis.confidence_score)} ({(typeof lastDiagnosis.confidence_score === 'number' ? (lastDiagnosis.confidence_score * 100).toFixed(0) : '0')}%) </div> </div> <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border "> <p className="text-blue-900 dark:text-blue-100 font-medium"> {lastDiagnosis.primary_diagnosis} </p> </div> </div> {/* Differential Diagnoses */} {lastDiagnosis.differential_diagnoses && lastDiagnosis.differential_diagnoses.length > 0 && ( <div className="mb-6"> <h4 className="font-medium text-foreground dark:text-white mb-3"> Differential Diagnoses </h4> <div className="space-y-2"> {lastDiagnosis.differential_diagnoses.map((diagnosis: any, index: number) => ( <div key={index} className="flex items-center justify-between p-3 bg-muted dark:bg-gray-700 rounded-lg" > <span className="text-foreground dark:text-white"> {diagnosis.diagnosis || diagnosis} </span> {diagnosis.confidence && ( <span className="text-sm text-muted-foreground dark:text-gray-400"> {(diagnosis.confidence * 100).toFixed(0)}% </span> )} </div> ))} </div> </div> )} {/* Reasoning */} <div className="mb-6"> <h4 className="font-medium text-foreground dark:text-white mb-3 flex items-center gap-2"> <FileText className="w-4 h-4" /> AI Reasoning </h4> <div className="p-4 bg-muted dark:bg-gray-700 rounded-lg"> <p className="text-foreground dark:text-gray-300 whitespace-pre-wrap"> {lastDiagnosis.reasoning} </p> </div> </div> {/* Recommended Tests */} {lastDiagnosis.recommended_tests && lastDiagnosis.recommended_tests.length > 0 && ( <div> <h4 className="font-medium text-foreground dark:text-white mb-3 flex items-center gap-2"> <TrendingUp className="w-4 h-4" /> Recommended Tests </h4> <div className="grid grid-cols-1 md:grid-cols-2 gap-2"> {lastDiagnosis.recommended_tests.map((test: string, index: number) => ( <div key={index} className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border rounded-lg" > <p className="text-amber-700 dark:text-amber-400 dark:text-yellow-200 text-sm"> {test} </p> </div> ))} </div> </div> )} {/* Disclaimer */} <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg"> <div className="flex items-start gap-2"> <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" /> <div> <p className="text-amber-800 dark:text-amber-200 font-medium text-sm"> Medical Disclaimer </p> <p className="text-amber-700 dark:text-amber-300 text-sm mt-1"> This AI-generated diagnosis is for informational purposes only and should not replace professional medical advice. Always consult with a qualified healthcare provider for proper diagnosis and treatment. </p> </div> </div> </div> </div> )} </div> ); }; export default AIDiagnosis; 