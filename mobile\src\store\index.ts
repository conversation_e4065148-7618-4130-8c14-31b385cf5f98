import { configureStore } from '@reduxjs/toolkit'; import authSlice from './slices/authSlice'; import patientSlice from './slices/patientSlice'; import appointmentSlice from './slices/appointmentSlice'; import medicalRecordSlice from './slices/medicalRecordSlice'; import notificationSlice from './slices/notificationSlice'; import themeSlice from './slices/themeSlice'; import labResultSlice from './slices/labResultSlice'; import prescriptionSlice from './slices/prescriptionSlice'; import emergencyContactSlice from './slices/emergencyContactSlice'; import healthSummarySlice from './slices/healthSummarySlice'; import doctorSlice from './slices/doctorSlice'; export const store = configureStore({ reducer: { auth: authSlice, patient: patientSlice, appointment: appointmentSlice, medicalRecord: medicalRecordSlice, notification: notificationSlice, theme: themeSlice, labResult: labResultSlice, prescription: prescriptionSlice, emergencyContact: emergencyContactSlice, healthSummary: healthSummarySlice, doctor: doctorSlice, }, middleware: (getDefaultMiddleware) => getDefaultMiddleware({ serializableCheck: { ignoredActions: ['persist/PERSIST'], }, }), }); export type RootState = ReturnType<typeof store.getState>; export type AppDispatch = typeof store.dispatch; 