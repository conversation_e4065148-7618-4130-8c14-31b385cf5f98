import { User } from './auth'; export interface Patient { id: number; patient_id: string; user: User; // Medical Information blood_group?: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-'; allergies?: string; chronic_conditions?: string; current_medications?: string; // Insurance Information insurance_provider?: string; insurance_policy_number?: string; // Emergency Contact emergency_contact_name?: string; emergency_contact_phone?: string; emergency_contact_relationship?: string; // Timestamps created_at: string; updated_at: string; } export interface MedicalRecord { id: number; patient: Patient; doctor: User; // Record Information visit_date: string; chief_complaint: string; diagnosis: string; treatment_plan: string; prescription?: string; notes?: string; // Vital Signs temperature?: number; blood_pressure_systolic?: number; blood_pressure_diastolic?: number; heart_rate?: number; respiratory_rate?: number; weight?: number; height?: number; // Follow-up follow_up_required: boolean; follow_up_date?: string; created_at: string; updated_at: string; } export interface Prescription { id: number; prescription_id: string; medical_record: number; patient: Patient; doctor: User; // Prescription details medication_name: string; dosage: string; frequency: string; duration: string; quantity: number; instructions?: string; // Status status: 'active' | 'completed' | 'cancelled'; // Dates prescribed_date: string; start_date: string; end_date: string; created_at: string; updated_at: string; } export interface LabTest { id: number; test_id: string; patient: Patient; doctor: User; medical_record?: number; test_name: string; test_type: 'blood' | 'urine' | 'imaging' | 'biopsy' | 'other'; // Status and dates status: 'ordered' | 'in_progress' | 'completed' | 'cancelled'; ordered_date: string; sample_collected_date?: string; result_date?: string; // Results result_value?: string; result_unit?: string; reference_range?: string; result_status?: 'normal' | 'abnormal' | 'critical'; notes?: string; created_at: string; updated_at: string; } export interface MedicalDocument { id: number; patient: Patient; medical_record?: number; uploaded_by: User; document_type: 'lab_result' | 'imaging' | 'prescription' | 'discharge_summary' | 'insurance' | 'other'; title: string; description?: string; file: string; file_size?: number; created_at: string; updated_at: string; } // API Response Types export interface PatientListResponse { count: number; next?: string; previous?: string; results: Patient[]; } export interface PatientCreateRequest { user_id: number; blood_group?: string; allergies?: string; chronic_conditions?: string; current_medications?: string; insurance_provider?: string; insurance_policy_number?: string; emergency_contact_name?: string; emergency_contact_phone?: string; emergency_contact_relationship?: string; } export interface PatientUpdateRequest extends Partial<PatientCreateRequest> {} export interface MedicalRecordCreateRequest { patient_id: number; doctor_id: number; visit_date: string; chief_complaint: string; diagnosis: string; treatment_plan: string; prescription?: string; notes?: string; temperature?: number; blood_pressure_systolic?: number; blood_pressure_diastolic?: number; heart_rate?: number; respiratory_rate?: number; weight?: number; height?: number; follow_up_required: boolean; follow_up_date?: string; } export interface PrescriptionCreateRequest { medical_record_id: number; patient_id: number; doctor_id: number; medication_name: string; dosage: string; frequency: string; duration: string; quantity: number; instructions?: string; start_date: string; end_date: string; } export interface LabTestCreateRequest { patient_id: number; doctor_id: number; medical_record_id?: number; test_name: string; test_type: string; } export interface LabTestResultUpdateRequest { result_value: string; result_unit?: string; reference_range?: string; result_status: 'normal' | 'abnormal' | 'critical'; notes?: string; } 