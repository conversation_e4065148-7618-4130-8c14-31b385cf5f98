import React, { useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, Alert, Linking } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import Card from '../../components/ui/Card'; interface EmergencyContact { id: number; name: string; type: 'hospital' | 'ambulance' | 'police' | 'fire' | 'poison' | 'personal'; phone: string; address?: string; description: string; available24h: boolean; icon: keyof typeof Ionicons.glyphMap; } interface PersonalContact { id: number; name: string; relationship: string; phone: string; email?: string; isPrimary: boolean; } const EmergencyScreen: React.FC = () => { const navigation = useNavigation(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const [activeTab, setActiveTab] = useState<'emergency' | 'personal'>('emergency'); // Mock emergency contacts const emergencyContacts: EmergencyContact[] = [ { id: 1, name: 'Emergency Services', type: 'ambulance', phone: '999', description: 'General emergency services', available24h: true, icon: 'medical' }, { id: 2, name: 'City Hospital Emergency', type: 'hospital', phone: '+966-11-123-4567', address: 'King Fahd Road, Riyadh', description: 'Main hospital emergency department', available24h: true, icon: 'business' }, { id: 3, name: 'Ambulance Service', type: 'ambulance', phone: '997', description: 'Direct ambulance service', available24h: true, icon: 'car' }, { id: 4, name: 'Police', type: 'police', phone: '999', description: 'Police emergency services', available24h: true, icon: 'shield' }, { id: 5, name: 'Fire Department', type: 'fire', phone: '998', description: 'Fire and rescue services', available24h: true, icon: 'flame' }, { id: 6, name: 'Poison Control', type: 'poison', phone: '+966-11-234-5678', description: 'Poison control center', available24h: true, icon: 'warning' } ]; // Mock personal emergency contacts const personalContacts: PersonalContact[] = [ { id: 1, name: 'Ahmed Hassan', relationship: 'Spouse', phone: '+966-50-123-4567', email: '<EMAIL>', isPrimary: true }, { id: 2, name: 'Fatima Hassan', relationship: 'Sister', phone: '+966-55-987-6543', email: '<EMAIL>', isPrimary: false }, { id: 3, name: 'Dr. Sarah Ahmed', relationship: 'Primary Doctor', phone: '+966-11-345-6789', isPrimary: false } ]; const handleCall = (phone: string, name: string) => { Alert.alert( t('emergency.confirmCall'), t('emergency.callConfirm', { name, phone }), [ { text: t('common.cancel'), style: 'cancel' }, { text: t('emergency.call'), style: 'default', onPress: () => { Linking.openURL(`tel:${phone}`).catch(() => { Alert.alert(t('common.error'), t('emergency.callError')); }); } } ] ); }; const handleSMS = (phone: string, name: string) => { const message = t('emergency.smsMessage'); Linking.openURL(`sms:${phone}?body=${encodeURIComponent(message)}`).catch(() => { Alert.alert(t('common.error'), t('emergency.smsError')); }); }; const getContactTypeColor = (type: string) => { switch (type) { case 'ambulance': return '#EF4444'; case 'hospital': return '#3B82F6'; case 'police': return '#1F2937'; case 'fire': return '#F97316'; case 'poison': return '#8B5CF6'; default: return '#6B7280'; } }; const renderEmergencyContacts = () => ( <View className="space-y-4"> {emergencyContacts.map((contact) => ( <Card key={contact.id} className="p-4"> <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <View className="w-12 h-12 rounded-full justify-center items-center mr-4" style={{ backgroundColor: `${getContactTypeColor(contact.type)}20` }} > <Ionicons name={contact.icon} size={24} color={getContactTypeColor(contact.type)} /> </View> <View className="flex-1"> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {contact.name} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {contact.description} </Text> {contact.address && ( <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {contact.address} </Text> )} <View className={`flex-row items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`text-sm font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {contact.phone} </Text> {contact.available24h && ( <View className={`ml-2 px-2 py-1 rounded-full bg-green-100 ${isRTL ? 'ml-0 mr-2' : ''}`}> <Text className="text-emerald-700 dark:text-emerald-400 text-xs font-medium"> {t('emergency.available24h')} </Text> </View> )} </View> </View> <TouchableOpacity onPress={() => handleCall(contact.phone, contact.name)} className="bg-red-600 w-12 h-12 rounded-full justify-center items-center" > <Ionicons name="call" size={24} color="white" /> </TouchableOpacity> </View> </Card> ))} </View> ); const renderPersonalContacts = () => ( <View className="space-y-4"> {personalContacts.map((contact) => ( <Card key={contact.id} className="p-4"> <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <View className={`w-12 h-12 rounded-full justify-center items-center mr-4 ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name="person" size={24} color="#007AFF" /> </View> <View className="flex-1"> <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {contact.name} </Text> {contact.isPrimary && ( <View className={`ml-2 px-2 py-1 rounded-full bg-blue-100 ${isRTL ? 'ml-0 mr-2' : ''}`}> <Text className="text-sky-700 dark:text-sky-400 text-xs font-medium"> {t('emergency.primary')} </Text> </View> )} </View> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {contact.relationship} </Text> <Text className={`text-sm font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {contact.phone} </Text> {contact.email && ( <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {contact.email} </Text> )} </View> <View className="flex-row space-x-2"> <TouchableOpacity onPress={() => handleCall(contact.phone, contact.name)} className="bg-green-600 w-10 h-10 rounded-full justify-center items-center" > <Ionicons name="call" size={20} color="white" /> </TouchableOpacity> <TouchableOpacity onPress={() => handleSMS(contact.phone, contact.name)} className="bg-blue-600 w-10 h-10 rounded-full justify-center items-center" > <Ionicons name="chatbubble" size={20} color="white" /> </TouchableOpacity> </View> </View> </Card> ))} <TouchableOpacity onPress={() => navigation.navigate('AddEmergencyContact' as never)} className={`p-4 rounded-xl border-2 border-dashed ${ isDark ? 'border-dark-border' : 'border-border' } items-center`} > <Ionicons name="add-circle-outline" size={32} color={isDark ? '#8E8E93' : '#8E8E93'} /> <Text className={`text-sm mt-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('emergency.addContact')} </Text> </TouchableOpacity> </View> ); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('emergency.title')} </Text> <Text className={`text-sm mt-1 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('emergency.subtitle')} </Text> </View> {/* Emergency Alert */} <View className="px-6 mb-4"> <Card className="p-4 bg-red-50 "> <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Ionicons name="warning" size={24} color="#EF4444" style={{ marginRight: isRTL ? 0 : 12, marginLeft: isRTL ? 12 : 0 }} /> <View className="flex-1"> <Text className="text-rose-700 dark:text-rose-400 font-semibold"> {t('emergency.alertTitle')} </Text> <Text className="text-rose-700 dark:text-rose-400 text-sm"> {t('emergency.alertMessage')} </Text> </View> </View> </Card> </View> {/* Tab Navigation */} <View className="px-6 mb-4"> <View className={`flex-row ${isDark ? 'bg-dark-card' : 'bg-background'} rounded-xl p-1 ${isRTL ? 'flex-row-reverse' : ''}`}> <TouchableOpacity onPress={() => setActiveTab('emergency')} className={`flex-1 py-3 px-4 rounded-lg ${ activeTab === 'emergency' ? 'bg-primary-600' : '' }`} > <Text className={`text-center font-medium ${ activeTab === 'emergency' ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('emergency.emergencyServices')} </Text> </TouchableOpacity> <TouchableOpacity onPress={() => setActiveTab('personal')} className={`flex-1 py-3 px-4 rounded-lg ${ activeTab === 'personal' ? 'bg-primary-600' : '' }`} > <Text className={`text-center font-medium ${ activeTab === 'personal' ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('emergency.personalContacts')} </Text> </TouchableOpacity> </View> </View> {/* Content */} <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}> <View className="pb-8"> {activeTab === 'emergency' ? renderEmergencyContacts() : renderPersonalContacts()} </View> </ScrollView> </SafeAreaView> ); }; export default EmergencyScreen; 