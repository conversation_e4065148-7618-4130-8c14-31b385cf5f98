import React, { useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, Alert, TextInput, KeyboardAvoidingView, Platform } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { medicalRecordService, CreateMedicalRecordData } from '../../services/medicalRecordService'; import LoadingSpinner from '../../components/common/LoadingSpinner'; import Card from '../../components/ui/Card'; import { DateTimeUtils } from '../../utils/dateTime'; import { useTranslation } from '../../hooks/useTranslation'; const AddMedicalRecordScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const { isDark } = useTheme(); const [formData, setFormData] = useState<CreateMedicalRecordData>({ diagnosis: '', symptoms: '', treatment: '', prescription: '', notes: '', visit_date: DateTimeUtils.toISODate(new Date()), doctor_name: '', follow_up_date: '' }); const [errors, setErrors] = useState<Record<string, string>>({}); const [saving, setSaving] = useState(false); const validateForm = () => { const newErrors: Record<string, string> = {}; if (!formData.diagnosis.trim()) { newErrors.diagnosis = 'Diagnosis is required'; } if (!formData.symptoms.trim()) { newErrors.symptoms = 'Symptoms are required'; } if (!formData.treatment.trim()) { newErrors.treatment = 'Treatment is required'; } if (!formData.visit_date.trim()) { newErrors.visit_date = 'Visit date is required'; } setErrors(newErrors); return Object.keys(newErrors).length === 0; }; const handleSave = async () => { if (!validateForm()) { Alert.alert('Validation Error', 'Please fill in all required fields.'); return; } setSaving(true); try { await medicalRecordService.createMedicalRecord(formData); Alert.alert('Success', 'Medical record added successfully!', [ { text: 'OK', onPress: () => navigation.goBack() } ]); } catch (error: any) { Alert.alert('Error', error.message || 'Failed to add medical record'); } finally { setSaving(false); } }; const renderInput = ( label: string, key: keyof CreateMedicalRecordData, placeholder: string, required = false, multiline = false, keyboardType: any = 'default' ) => ( <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {label} {required && <Text className="text-red-500">*</Text>} </Text> <TextInput value={formData[key] || ''} onChangeText={(text) => setFormData(prev => ({ ...prev, [key]: text }))} placeholder={placeholder} placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'} multiline={multiline} numberOfLines={multiline ? 4 : 1} keyboardType={keyboardType} className={`border rounded-lg px-3 py-3 ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' } ${errors[key] ? 'border-red-500' : ''}`} /> {errors[key] && ( <Text className="text-red-500 text-xs mt-1">{errors[key]}</Text> )} </View> ); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1" > {/* Header */} <View className={`px-6 py-4 border-b ${ isDark ? 'border-dark-border' : 'border-border' }`}> <View className="flex-row items-center justify-between"> <TouchableOpacity onPress={() => navigation.goBack()}> <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} /> </TouchableOpacity> <Text className={`text-lg font-semibold ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Add Medical Record </Text> <TouchableOpacity onPress={handleSave} disabled={saving} className="bg-primary-600 px-4 py-2 rounded-lg" > {saving ? ( <LoadingSpinner size="small" /> ) : ( <Text className="text-white font-medium">Save</Text> )} </TouchableOpacity> </View> </View> <ScrollView className="flex-1 px-6 py-4"> {/* Visit Information */} <Card className="mb-6"> <Text className={`text-lg font-semibold mb-4 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Visit Information </Text> {renderInput('Visit Date', 'visit_date', 'YYYY-MM-DD', true)} {renderInput('Doctor Name', 'doctor_name', 'Enter doctor name')} </Card> {/* Medical Details */} <Card className="mb-6"> <Text className={`text-lg font-semibold mb-4 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Medical Details </Text> {renderInput('Diagnosis', 'diagnosis', 'Enter diagnosis', true, true)} {renderInput('Symptoms', 'symptoms', 'Describe symptoms experienced', true, true)} {renderInput('Treatment', 'treatment', 'Describe treatment provided', true, true)} </Card> {/* Prescription & Notes */} <Card className="mb-6"> <Text className={`text-lg font-semibold mb-4 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Additional Information </Text> {renderInput('Prescription', 'prescription', 'List prescribed medications', false, true)} {renderInput('Notes', 'notes', 'Additional notes or observations', false, true)} {renderInput('Follow-up Date', 'follow_up_date', 'YYYY-MM-DD (if applicable)')} </Card> {/* Instructions */} <View className={`p-4 rounded-lg mb-6 ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <View className="flex-row items-start"> <Ionicons name="information-circle" size={20} color={isDark ? '#60A5FA' : '#3B82F6'} className="mr-2 mt-1" /> <View className="flex-1"> <Text className={`text-sm ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> <Text className="font-medium">Note:</Text> This information will be added to your medical history. Make sure all details are accurate. You can edit or delete this record later if needed. </Text> </View> </View> </View> </ScrollView> </KeyboardAvoidingView> </SafeAreaView> ); }; export default AddMedicalRecordScreen; 