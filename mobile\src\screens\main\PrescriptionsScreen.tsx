import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl, Alert } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { RootState, AppDispatch } from '../../store'; import { fetchMyPrescriptions, fetchActivePrescriptions, requestRefill } from '../../store/slices/prescriptionSlice'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; interface Prescription { id: number; medication_name: string; dosage: string; frequency: string; duration: string; instructions: string; prescribed_date: string; start_date: string; end_date: string; doctor_name: string; status: 'active' | 'completed' | 'discontinued'; refills_remaining: number; total_refills: number; pharmacy_name?: string; side_effects?: string[]; notes?: string; } const PrescriptionsScreen: React.FC = () => { const navigation = useNavigation(); const dispatch = useDispatch<AppDispatch>(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const { prescriptions, activePrescriptions, isLoading, error } = useSelector((state: RootState) => state.prescription); const [refreshing, setRefreshing] = useState(false); const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all'); // Load prescriptions on component mount useEffect(() => { loadPrescriptions(); }, [dispatch]); // Load prescriptions based on current filter useEffect(() => { if (filter === 'active') { dispatch(fetchActivePrescriptions()); } else { dispatch(fetchMyPrescriptions({ page: 1, pageSize: 20 })); } }, [filter, dispatch]); const loadPrescriptions = () => { if (filter === 'active') { dispatch(fetchActivePrescriptions()); } else { dispatch(fetchMyPrescriptions({ page: 1, pageSize: 20 })); } }; const onRefresh = async () => { setRefreshing(true); loadPrescriptions(); setRefreshing(false); }; // Show error alert if there's an error useEffect(() => { if (error) { Alert.alert(t('common.error'), error); } }, [error, t]); const getFilteredPrescriptions = () => { const dataSource = filter === 'active' ? activePrescriptions : prescriptions; switch (filter) { case 'active': return dataSource.filter(p => p.status === 'active'); case 'completed': return dataSource.filter(p => p.status === 'completed' || p.status === 'discontinued'); default: return dataSource; } }; const getStatusColor = (status: string) => { switch (status) { case 'active': return 'success'; case 'completed': return 'secondary'; case 'discontinued': return 'destructive'; default: return 'secondary'; } }; const getStatusIcon = (status: string) => { switch (status) { case 'active': return 'medical'; case 'completed': return 'checkmark-circle'; case 'discontinued': return 'close-circle'; default: return 'help-circle'; } }; const handlePrescriptionPress = (prescription: Prescription) => { navigation.navigate('PrescriptionDetail' as never, { prescriptionId: prescription.id }); }; const handleRefillRequest = (prescription: Prescription) => { if (prescription.refills_remaining > 0) { Alert.alert( t('prescriptions.requestRefill'), t('prescriptions.refillConfirm', { medication: prescription.medication_name }), [ { text: t('common.cancel'), style: 'cancel' }, { text: t('prescriptions.request'), onPress: async () => { try { await dispatch(requestRefill({ prescription_id: prescription.id, notes: 'Refill requested via mobile app' })).unwrap(); Alert.alert(t('common.success'), t('prescriptions.refillRequested')); } catch (error: any) { Alert.alert(t('common.error'), error || t('prescriptions.refillFailed')); } }} ] ); } else { Alert.alert( t('prescriptions.noRefills'), t('prescriptions.contactDoctor') ); } }; const handleSetReminder = (prescription: Prescription) => { Alert.alert( t('prescriptions.setReminder'), t('prescriptions.reminderMessage', { medication: prescription.medication_name }), [ { text: t('common.cancel'), style: 'cancel' }, { text: t('prescriptions.setReminder'), onPress: () => { // TODO: Implement medication reminder Alert.alert(t('common.success'), t('prescriptions.reminderSet')); }} ] ); }; if (isLoading) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <LoadingSpinner /> </SafeAreaView> ); } const filteredPrescriptions = getFilteredPrescriptions(); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('prescriptions.title')} </Text> <Text className={`text-sm mt-1 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('prescriptions.subtitle')} </Text> </View> {/* Filter Tabs */} <View className="px-6 mb-4"> <ScrollView horizontal showsHorizontalScrollIndicator={false}> <View className={`flex-row space-x-3 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> {[ { key: 'all', label: t('common.all') }, { key: 'active', label: t('prescriptions.active') }, { key: 'completed', label: t('prescriptions.completed') } ].map((tab) => ( <TouchableOpacity key={tab.key} onPress={() => setFilter(tab.key as any)} className={`px-4 py-2 rounded-full ${ filter === tab.key ? 'bg-primary-600' : isDark ? 'bg-dark-card border border-dark-border' : 'bg-background border border-border' }`} > <Text className={`font-medium ${ filter === tab.key ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {tab.label} </Text> </TouchableOpacity> ))} </View> </ScrollView> </View> {/* Prescriptions List */} <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false} refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } > {filteredPrescriptions.length === 0 ? ( <View className="flex-1 justify-center items-center py-12"> <Ionicons name="medical-outline" size={64} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-xl font-bold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('prescriptions.noPrescriptions')} </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('prescriptions.noPrescriptionsMessage')} </Text> </View> ) : ( <View className="space-y-4 pb-8"> {filteredPrescriptions.map((prescription) => ( <Card key={prescription.id} className="p-4"> <TouchableOpacity onPress={() => handlePrescriptionPress(prescription)}> <View className={`flex-row items-start justify-between ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <View className="flex-1"> <View className={`flex-row items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Ionicons name={getStatusIcon(prescription.status)} size={20} color={prescription.status === 'active' ? '#10B981' : prescription.status === 'completed' ? '#6B7280' : '#EF4444'} style={{ marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }} /> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {prescription.medication_name} </Text> </View> <Text className={`text-sm mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {prescription.dosage} • {prescription.frequency} </Text> <Text className={`text-sm mb-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {prescription.instructions} </Text> <Text className={`text-sm mb-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('prescriptions.doctor')}: {prescription.doctor_name} </Text> {prescription.status === 'active' && ( <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('prescriptions.refillsRemaining')}: {prescription.refills_remaining} </Text> )} </View> <View className={`items-end ${isRTL ? 'items-start' : ''}`}> <Badge variant={getStatusColor(prescription.status)} className="mb-2" > {t(`prescriptions.status.${prescription.status}`)} </Badge> {prescription.status === 'active' && ( <View className="space-y-2"> <TouchableOpacity onPress={() => handleRefillRequest(prescription)} className="flex-row items-center" > <Ionicons name="refresh-outline" size={16} color="#007AFF" /> <Text className="text-primary-600 text-sm ml-1"> {t('prescriptions.refill')} </Text> </TouchableOpacity> <TouchableOpacity onPress={() => handleSetReminder(prescription)} className="flex-row items-center" > <Ionicons name="alarm-outline" size={16} color="#007AFF" /> <Text className="text-primary-600 text-sm ml-1"> {t('prescriptions.reminder')} </Text> </TouchableOpacity> </View> )} </View> </View> </TouchableOpacity> </Card> ))} </View> )} </ScrollView> </SafeAreaView> ); }; export default PrescriptionsScreen; 