import React, { useState } from 'react'; import { useTranslation } from 'react-i18next'; import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'; import { Button } from '../ui/Button'; import { Badge } from '../ui/badge'; import { Input } from '../ui/Input'; import { useTheme } from '../../hooks/useTheme'; import { Pill, Clock, User, AlertTriangle, CheckCircle, Calendar, Search, Filter, Plus, Eye, Edit } from 'lucide-react'; const MedicationAdministration: React.FC = () => { const { t } = useTranslation(); const { isDark } = useTheme(); const [activeTab, setActiveTab] = useState('due'); const [searchTerm, setSearchTerm] = useState(''); const medicationsDue = [ { id: 1, patient: '<PERSON>', room: '101', medication: 'Lisinopril 10mg', dosage: '1 tablet', route: 'Oral', time: '09:00 AM', frequency: 'Once daily', status: 'due', priority: 'normal', notes: 'Take with food' }, { id: 2, patient: '<PERSON>', room: '203', medication: 'Insulin Glargine', dosage: '20 units', route: 'Subcutaneous', time: '09:30 AM', frequency: 'Once daily', status: 'overdue', priority: 'high', notes: 'Check blood glucose before administration' }, { id: 3, patient: 'Emily Davis', room: '105', medication: 'Metformin 500mg', dosage: '2 tablets', route: 'Oral', time: '10:00 AM', frequency: 'Twice daily', status: 'due', priority: 'normal', notes: 'Monitor for GI side effects' } ]; const medicationsAdministered = [ { id: 4, patient: 'Robert Wilson', room: '150', medication: 'Atorvastatin 20mg', dosage: '1 tablet', route: 'Oral', time: '08:00 AM', administeredTime: '08:15 AM', administeredBy: 'Nurse Mary', status: 'completed', notes: 'Patient tolerated well' }, { id: 5, patient: 'Lisa Anderson', room: '220', medication: 'Warfarin 5mg', dosage: '1 tablet', route: 'Oral', time: '08:30 AM', administeredTime: '08:35 AM', administeredBy: 'Nurse Mary', status: 'completed', notes: 'INR checked - within range' } ]; const getStatusColor = (status: string) => { switch (status) { case 'due': return 'status-warning '; case 'overdue': return 'status-error '; case 'completed': return 'status-success '; case 'missed': return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300'; default: return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300'; } }; const getPriorityColor = (priority: string) => { switch (priority) { case 'high': return 'status-error '; case 'normal': return 'status-info '; case 'low': return 'status-success '; default: return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300'; } }; const handleAdminister = (medicationId: number) => { // TODO: Handle action // Implementation for medication administration }; const renderMedicationCard = (medication: any, showAdminActions = true) => ( <Card key={medication.id} className="glass border-0 shadow-lg hover:glass-hover macos-transition mb-4"> <CardContent className="p-6"> <div className="flex items-start justify-between mb-4"> <div className="flex-1"> <div className="flex items-center justify-between mb-3"> <div className="flex items-center gap-3"> <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-rose-600 rounded-xl flex items-center justify-center shadow-lg"> <Pill className="w-5 h-5 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary"> {medication.medication} </h3> </div> <div className="flex space-x-2"> <Badge className={`${getStatusColor(medication.status)} rounded-full px-3 py-1`}> {medication.status} </Badge> {medication.priority && ( <Badge className={`${getPriorityColor(medication.priority)} rounded-full px-3 py-1`}> {medication.priority} </Badge> )} </div> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"> <div className="space-y-3"> <div className="flex items-center macos-text-secondary"> <User className="w-4 h-4 mr-2 macos-text-tertiary" /> <span>{medication.patient} - Room <span className="macos-text-primary font-medium">{medication.room}</span></span> </div> <div className="flex items-center macos-text-secondary"> <Pill className="w-4 h-4 mr-2 macos-text-tertiary" /> <span>{medication.dosage} - <span className="macos-text-primary font-medium">{medication.route}</span></span> </div> <div className="flex items-center macos-text-secondary"> <Clock className="w-4 h-4 mr-2 macos-text-tertiary" /> <span> {medication.administeredTime ? `Administered: ${medication.administeredTime}` : `Due: ${medication.time}` } </span> </div> </div> <div className="space-y-3"> <div className="text-sm macos-text-secondary"> <strong className="macos-text-primary">Frequency:</strong> {medication.frequency} </div> {medication.administeredBy && ( <div className="text-sm macos-text-secondary"> <strong className="macos-text-primary">Administered by:</strong> {medication.administeredBy} </div> )} {medication.notes && ( <div className="glass-subtle border border-pink-200/50 dark:border-pink-800/50 rounded-xl p-3"> <div className="text-sm"> <strong className="macos-text-primary">Notes:</strong> <p className="text-pink-800 dark:text-pink-400 mt-1">{medication.notes}</p> </div> </div> )} </div> </div> </div> </div> {showAdminActions && medication.status !== 'completed' && ( <div className="flex justify-end space-x-2"> <Button variant="glass" size="sm" className="flex items-center gap-2"> <Eye className="w-4 h-4" /> View Details </Button> <Button variant="glass" size="sm" className="flex items-center gap-2"> <Edit className="w-4 h-4" /> Edit </Button> <Button variant="glass" size="sm" onClick={() => handleAdminister(medication.id)} className={`flex items-center gap-2 ${medication.status === 'overdue' ? 'bg-red-500/20 hover:bg-red-500/30 text-rose-700 dark:text-rose-400 dark:text-red-400' : ''}`} > <CheckCircle className="w-4 h-4" /> Administer </Button> </div> )} </CardContent> </Card> ); return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */} <Card className="glass border-0 shadow-xl"> <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-rose-600 rounded-xl flex items-center justify-center shadow-lg"> <Pill className="w-6 h-6 text-white" /> </div> <div> <CardTitle className="text-2xl font-bold macos-text-primary">{t('medication.title')}</CardTitle> <p className="macos-text-secondary">{t('medication.description')}</p> </div> </div> <Button variant="glass" className="flex items-center gap-2"> <Plus className="w-4 h-4" /> {t('medication.addMedication')} </Button> </div> </CardHeader> </Card> {/* Statistics */} <div className="grid grid-cols-1 md:grid-cols-4 gap-6"> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{t('medication.dueNow')}</p> <p className="text-2xl font-bold text-amber-700 dark:text-amber-400 dark:text-yellow-400"> {medicationsDue.filter(m => m.status === 'due').length} </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg"> <Clock className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{t('medication.overdue')}</p> <p className="text-2xl font-bold text-rose-700 dark:text-rose-400 dark:text-red-400"> {medicationsDue.filter(m => m.status === 'overdue').length} </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg"> <AlertTriangle className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{t('medication.completedToday')}</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400 dark:text-green-400">{medicationsAdministered.length}</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg"> <CheckCircle className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{t('medication.totalMedications')}</p> <p className="text-2xl font-bold text-sky-700 dark:text-sky-400 dark:text-blue-400"> {medicationsDue.length + medicationsAdministered.length} </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"> <Pill className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> </div> {/* Search and Filter */} <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center space-x-4"> <div className="flex-1 relative"> <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary" /> <Input type="text" placeholder="Search medications, patients, or rooms..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10" variant="glass" /> </div> <Button variant="glass" className="flex items-center gap-2"> <Filter className="w-4 h-4" /> Filter </Button> </div> </CardContent> </Card> {/* Tabs */} <Card className="glass border-0 shadow-lg"> <CardContent className="p-0"> <div className="border-b border-border/50 dark:border-gray-700/50"> <nav className="-mb-px flex space-x-8 px-6"> <button onClick={() => setActiveTab('due')} className={`py-4 px-1 border-b-2 font-medium text-sm macos-transition ${ activeTab === 'due' ? 'border-blue-500 text-sky-700 dark:text-sky-400 dark:text-blue-400' : 'border-transparent macos-text-secondary hover:macos-text-primary hover:border-border dark:hover:border-gray-600' }`} > Due Medications ({medicationsDue.length}) </button> <button onClick={() => setActiveTab('administered')} className={`py-4 px-1 border-b-2 font-medium text-sm macos-transition ${ activeTab === 'administered' ? 'border-blue-500 text-sky-700 dark:text-sky-400 dark:text-blue-400' : 'border-transparent macos-text-secondary hover:macos-text-primary hover:border-border dark:hover:border-gray-600' }`} > Administered ({medicationsAdministered.length}) </button> </nav> </div> </CardContent> </Card> {/* Content */} <div> {activeTab === 'due' && ( <div> {medicationsDue.length === 0 ? ( <Card className="glass border-0 shadow-lg"> <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg"> <Pill className="w-8 h-8 text-white" /> </div> <p className="macos-text-secondary">No medications due</p> </CardContent> </Card> ) : ( medicationsDue.map(medication => renderMedicationCard(medication, true)) )} </div> )} {activeTab === 'administered' && ( <div> {medicationsAdministered.length === 0 ? ( <Card className="glass border-0 shadow-lg"> <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg"> <CheckCircle className="w-8 h-8 text-white" /> </div> <p className="macos-text-secondary">No medications administered today</p> </CardContent> </Card> ) : ( medicationsAdministered.map(medication => renderMedicationCard(medication, false)) )} </div> )} </div> </div> </div> ); }; export default MedicationAdministration; 