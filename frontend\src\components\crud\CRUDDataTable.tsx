import React, { useState, useEffect } from 'react'; import { Button } from '../ui/Button'; import { Input } from '../ui/Input'; import { Modal } from '../ui/Modal'; import { DataTable } from '../ui/DataTable'; interface CRUDDataTableProps<T> { title: string; data: T[]; columns: Array<{ key: keyof T; label: string; render?: (value: any, item: T) => React.ReactNode; }>; onCreate?: (item: Partial<T>) => Promise<void>; onUpdate?: (id: string | number, item: Partial<T>) => Promise<void>; onDelete?: (id: string | number) => Promise<void>; onRefresh?: () => Promise<void>; createFields?: Array<{ key: keyof T; label: string; type: 'text' | 'email' | 'number' | 'select' | 'textarea' | 'date' | 'datetime-local'; required?: boolean; options?: Array<{ value: string; label: string }>; }>; searchable?: boolean; filterable?: boolean; loading?: boolean; } export function CRUDDataTable<T extends { id: string | number }>({ title, data, columns, onCreate, onUpdate, onDelete, onRefresh, createFields = [], searchable = true, filterable = false, loading = false }: CRUDDataTableProps<T>) { const [isCreateModalOpen, setIsCreateModalOpen] = useState(false); const [isEditModalOpen, setIsEditModalOpen] = useState(false); const [editingItem, setEditingItem] = useState<T | null>(null); const [formData, setFormData] = useState<Partial<T>>({}); const [searchTerm, setSearchTerm] = useState(''); const [filteredData, setFilteredData] = useState<T[]>(data); useEffect(() => { if (searchable && searchTerm) { const filtered = data.filter(item => Object.values(item).some(value => String(value).toLowerCase().includes(searchTerm.toLowerCase()) ) ); setFilteredData(filtered); } else { setFilteredData(data); } }, [data, searchTerm, searchable]); const handleCreate = async () => { if (onCreate) { try { await onCreate(formData); setIsCreateModalOpen(false); setFormData({}); if (onRefresh) await onRefresh(); } catch (error) { console.error('Error creating item:', error); } } }; const handleUpdate = async () => { if (onUpdate && editingItem) { try { await onUpdate(editingItem.id, formData); setIsEditModalOpen(false); setEditingItem(null); setFormData({}); if (onRefresh) await onRefresh(); } catch (error) { console.error('Error updating item:', error); } } }; const handleDelete = async (id: string | number) => { if (onDelete && window.confirm('Are you sure you want to delete this item?')) { try { await onDelete(id); if (onRefresh) await onRefresh(); } catch (error) { console.error('Error deleting item:', error); } } }; const openEditModal = (item: T) => { setEditingItem(item); setFormData({ ...item }); setIsEditModalOpen(true); }; const renderFormField = (field: any) => { const value = formData[field.key] || ''; switch (field.type) { case 'select': return ( <select value={value} onChange={(e) => setFormData({ ...formData, [field.key]: e.target.value })} className="w-full p-2 border border-border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent" required={field.required} > <option value="">Select {field.label}</option> {field.options?.map((option: any) => ( <option key={option.value} value={option.value}> {option.label} </option> ))} </select> ); case 'textarea': return ( <textarea value={value} onChange={(e) => setFormData({ ...formData, [field.key]: e.target.value })} className="w-full p-2 border border-border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows={3} required={field.required} /> ); default: return ( <Input type={field.type} value={value} onChange={(e) => setFormData({ ...formData, [field.key]: e.target.value })} required={field.required} /> ); } }; const enhancedColumns = [ ...columns, { key: 'actions' as keyof T, label: 'Actions', render: (_: any, item: T) => ( <div className="flex space-x-2"> {onUpdate && ( <Button variant="outline" size="sm" onClick={() => openEditModal(item)} > Edit </Button> )} {onDelete && ( <Button variant="outline" size="sm" onClick={() => handleDelete(item.id)} className="text-rose-700 dark:text-rose-400 hover:text-rose-700 dark:text-rose-400" > Delete </Button> )} </div> ) } ]; return ( <div className="space-y-4"> {/* Header */} <div className="flex justify-between items-center"> <h2 className="text-2xl font-bold text-foreground">{title}</h2> <div className="flex space-x-2"> {onRefresh && ( <Button variant="outline" onClick={onRefresh} disabled={loading}> Refresh </Button> )} {onCreate && createFields.length > 0 && ( <Button onClick={() => setIsCreateModalOpen(true)}> Add New </Button> )} </div> </div> {/* Search */} {searchable && ( <div className="w-full max-w-md"> <Input type="text" placeholder={`Search ${title.toLowerCase()}...`} value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} /> </div> )} {/* Data Table */} <DataTable data={filteredData} columns={enhancedColumns} loading={loading} /> {/* Create Modal */} <Modal isOpen={isCreateModalOpen} onClose={() => { setIsCreateModalOpen(false); setFormData({}); }} title={`Create New ${title.slice(0, -1)}`} > <div className="space-y-4"> {createFields.map((field) => ( <div key={String(field.key)}> <label className="block text-sm font-medium text-foreground mb-1"> {field.label} {field.required && <span className="text-red-500">*</span>} </label> {renderFormField(field)} </div> ))} <div className="flex justify-end space-x-2 pt-4"> <Button variant="outline" onClick={() => { setIsCreateModalOpen(false); setFormData({}); }} > Cancel </Button> <Button onClick={handleCreate}> Create </Button> </div> </div> </Modal> {/* Edit Modal */} <Modal isOpen={isEditModalOpen} onClose={() => { setIsEditModalOpen(false); setEditingItem(null); setFormData({}); }} title={`Edit ${title.slice(0, -1)}`} > <div className="space-y-4"> {createFields.map((field) => ( <div key={String(field.key)}> <label className="block text-sm font-medium text-foreground mb-1"> {field.label} {field.required && <span className="text-red-500">*</span>} </label> {renderFormField(field)} </div> ))} <div className="flex justify-end space-x-2 pt-4"> <Button variant="outline" onClick={() => { setIsEditModalOpen(false); setEditingItem(null); setFormData({}); }} > Cancel </Button> <Button onClick={handleUpdate}> Update </Button> </div> </div> </Modal> </div> ); } 