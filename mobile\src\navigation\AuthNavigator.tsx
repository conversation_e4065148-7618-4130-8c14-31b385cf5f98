import React from 'react'; import { createNativeStackNavigator } from '@react-navigation/native-stack'; import LoginScreen from '../screens/auth/LoginScreen'; import WelcomeScreen from '../screens/auth/WelcomeScreen'; export type AuthStackParamList = { Welcome: undefined; Login: undefined; }; const Stack = createNativeStackNavigator(); const AuthNavigator: React.FC = () => { return ( <Stack.Navigator initialRouteName="Welcome" screenOptions={{ headerShown: false, gestureEnabled: true, animation: 'slide_from_right', }} > <Stack.Screen name="Welcome" component={WelcomeScreen} /> <Stack.Screen name="Login" component={LoginScreen} /> </Stack.Navigator> ); }; export default AuthNavigator; 