/** * Debounce Hooks * Provides debounced values and callbacks for performance optimization */ import { useState, useEffect, useCallback, useRef } from 'react'; /** * Hook that debounces a value */ export function useDebounce<T>(value: T, delay: number): T { const [debouncedValue, setDebouncedValue] = useState<T>(value); useEffect(() => { const handler = setTimeout(() => { setDebouncedValue(value); }, delay); return () => { clearTimeout(handler); }; }, [value, delay]); return debouncedValue; } /** * Hook that provides a debounced callback function */ export function useDebounceCallback<T extends (...args: any[]) => any>( callback: T, delay: number, deps: React.DependencyList = [] ): T { const timeoutRef = useRef<NodeJS.Timeout>(); const debouncedCallback = useCallback( (...args: Parameters<T>) => { if (timeoutRef.current) { clearTimeout(timeoutRef.current); } timeoutRef.current = setTimeout(() => { callback(...args); }, delay); }, [callback, delay, ...deps] ) as T; // Cleanup on unmount useEffect(() => { return () => { if (timeoutRef.current) { clearTimeout(timeoutRef.current); } }; }, []); return debouncedCallback; } /** * Hook that provides both debounced value and immediate value */ export function useDebouncedState<T>( initialValue: T, delay: number ): [T, T, (value: T) => void] { const [immediateValue, setImmediateValue] = useState<T>(initialValue); const debouncedValue = useDebounce(immediateValue, delay); return [immediateValue, debouncedValue, setImmediateValue]; } /** * Hook for debounced search functionality */ export function useDebounceSearch( searchFunction: (query: string) => Promise<any> | any, delay: number = 300 ) { const [query, setQuery] = useState(''); const [results, setResults] = useState<any[]>([]); const [loading, setLoading] = useState(false); const [error, setError] = useState<string | null>(null); const debouncedQuery = useDebounce(query, delay); useEffect(() => { if (!debouncedQuery.trim()) { setResults([]); setLoading(false); setError(null); return; } const performSearch = async () => { setLoading(true); setError(null); try { const searchResults = await searchFunction(debouncedQuery); setResults(searchResults || []); } catch (err) { setError(err instanceof Error ? err.message : 'Search failed'); setResults([]); } finally { setLoading(false); } }; performSearch(); }, [debouncedQuery, searchFunction]); const clearSearch = useCallback(() => { setQuery(''); setResults([]); setError(null); }, []); return { query, setQuery, debouncedQuery, results, loading, error, clearSearch, }; } /** * Hook for debounced form validation */ export function useDebounceValidation<T>( value: T, validator: (value: T) => string | null | Promise<string | null>, delay: number = 500 ) { const [error, setError] = useState<string | null>(null); const [validating, setValidating] = useState(false); const [isValid, setIsValid] = useState<boolean | null>(null); const debouncedValue = useDebounce(value, delay); useEffect(() => { if (!debouncedValue) { setError(null); setIsValid(null); setValidating(false); return; } const validate = async () => { setValidating(true); try { const validationError = await validator(debouncedValue); setError(validationError); setIsValid(!validationError); } catch (err) { setError(err instanceof Error ? err.message : 'Validation failed'); setIsValid(false); } finally { setValidating(false); } }; validate(); }, [debouncedValue, validator]); return { error, validating, isValid, }; } /** * Hook for debounced API calls */ export function useDebounceApi<T, P extends any[]>( apiFunction: (...params: P) => Promise<T>, delay: number = 300 ) { const [data, setData] = useState<T | null>(null); const [loading, setLoading] = useState(false); const [error, setError] = useState<string | null>(null); const debouncedApiCall = useDebounceCallback( async (...params: P) => { setLoading(true); setError(null); try { const result = await apiFunction(...params); setData(result); } catch (err) { setError(err instanceof Error ? err.message : 'API call failed'); setData(null); } finally { setLoading(false); } }, delay ); const reset = useCallback(() => { setData(null); setError(null); setLoading(false); }, []); return { data, loading, error, call: debouncedApiCall, reset, }; } /** * Hook for debounced resize handling */ export function useDebounceResize( callback: (size: { width: number; height: number }) => void, delay: number = 250 ) { const debouncedCallback = useDebounceCallback(callback, delay); useEffect(() => { const handleResize = () => { debouncedCallback({ width: window.innerWidth, height: window.innerHeight, }); }; window.addEventListener('resize', handleResize); // Call immediately to get initial size handleResize(); return () => { window.removeEventListener('resize', handleResize); }; }, [debouncedCallback]); } /** * Hook for debounced scroll handling */ export function useDebounceScroll( callback: (scrollPosition: { x: number; y: number }) => void, delay: number = 100 ) { const debouncedCallback = useDebounceCallback(callback, delay); useEffect(() => { const handleScroll = () => { debouncedCallback({ x: window.scrollX, y: window.scrollY, }); }; window.addEventListener('scroll', handleScroll, { passive: true }); return () => { window.removeEventListener('scroll', handleScroll); }; }, [debouncedCallback]); } 