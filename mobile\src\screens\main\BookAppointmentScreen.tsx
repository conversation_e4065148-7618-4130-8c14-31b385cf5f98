import React, { useState, useEffect } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, Alert, TextInput } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { RootState, AppDispatch } from '../../store'; import { createAppointment } from '../../store/slices/appointmentSlice'; import { DateTimeUtils } from '../../utils/dateTime'; import Button from '../../components/ui/Button'; import Input from '../../components/ui/Input'; import Card from '../../components/ui/Card'; import LoadingSpinner from '../../components/common/LoadingSpinner'; import { useTranslation } from '../../hooks/useTranslation'; interface Doctor { id: number; name: string; specialization: string; available: boolean; } // Mock doctors data - in real app, this would come from API const mockDoctors: Doctor[] = [ { id: 1, name: 'Dr. <PERSON>', specialization: 'Cardiology', available: true }, { id: 2, name: 'Dr. <PERSON>', specialization: 'Dermatology', available: true }, { id: 3, name: 'Dr. Michael Chen', specialization: 'Orthopedics', available: true }, { id: 4, name: 'Dr. Fatima Al-Zahra', specialization: 'Pediatrics', available: true }, { id: 5, name: 'Dr. Robert Smith', specialization: 'Neurology', available: false }, ]; const appointmentTypes = [ { value: 'consultation', label: 'Consultation' }, { value: 'follow_up', label: 'Follow Up' }, { value: 'routine_checkup', label: 'Routine Checkup' }, { value: 'emergency', label: 'Emergency' }, ]; const BookAppointmentScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { user } = useSelector((state: RootState) => state.auth); const { isLoading } = useSelector((state: RootState) => state.appointment); const [step, setStep] = useState(1); const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null); const [selectedDate, setSelectedDate] = useState(''); const [selectedTime, setSelectedTime] = useState(''); const [appointmentType, setAppointmentType] = useState('consultation'); const [reason, setReason] = useState(''); const [availableSlots, setAvailableSlots] = useState<string[]>([]); useEffect(() => { if (selectedDate) { // Generate available time slots for the selected date const slots = DateTimeUtils.getAvailableTimeSlots(selectedDate); setAvailableSlots(slots); setSelectedTime(''); // Reset selected time when date changes } }, [selectedDate]); const handleDoctorSelect = (doctor: Doctor) => { if (!doctor.available) { Alert.alert('Doctor Unavailable', 'This doctor is currently not available for appointments.'); return; } setSelectedDoctor(doctor); setStep(2); }; const handleDateSelect = (date: string) => { const validation = DateTimeUtils.isValidAppointmentDate(date); if (!validation.isValid) { Alert.alert('Invalid Date', validation.message); return; } setSelectedDate(date); setStep(3); }; const handleTimeSelect = (time: string) => { setSelectedTime(time); setStep(4); }; const handleBookAppointment = async () => { if (!selectedDoctor || !selectedDate || !selectedTime || !reason.trim()) { Alert.alert('Missing Information', 'Please fill in all required fields.'); return; } try { const appointmentData = { patient: user!.id, doctor: selectedDoctor.id, appointment_date: selectedDate, appointment_time: selectedTime, appointment_type: appointmentType as any, reason: reason.trim(), }; await dispatch(createAppointment(appointmentData)).unwrap(); Alert.alert( 'Appointment Booked!', `Your appointment with ${selectedDoctor.name} has been scheduled for ${DateTimeUtils.formatDateTime(selectedDate, selectedTime)}.`, [ { text: 'OK', onPress: () => navigation.goBack(), }, ] ); } catch (error: any) { Alert.alert('Booking Failed', error || 'Failed to book appointment. Please try again.'); } }; const renderStepIndicator = () => ( <View className="flex-row justify-center items-center mb-6"> {[1, 2, 3, 4].map((stepNumber) => ( <View key={stepNumber} className="flex-row items-center"> <View className={`w-8 h-8 rounded-full items-center justify-center ${ step >= stepNumber ? 'bg-primary-600' : isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Text className={`text-sm font-semibold ${ step >= stepNumber ? 'text-white' : isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {stepNumber} </Text> </View> {stepNumber < 4 && ( <View className={`w-8 h-0.5 ${ step > stepNumber ? 'bg-primary-600' : isDark ? 'bg-dark-muted' : 'bg-muted' }`} /> )} </View> ))} </View> ); const renderDoctorSelection = () => ( <View> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Select a Doctor </Text> <ScrollView showsVerticalScrollIndicator={false}> {mockDoctors.map((doctor) => ( <TouchableOpacity key={doctor.id} onPress={() => handleDoctorSelect(doctor)} disabled={!doctor.available} > <Card className={`mb-3 ${!doctor.available ? 'opacity-50' : ''}`}> <View className="flex-row items-center"> <View className={`w-12 h-12 rounded-full mr-4 items-center justify-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name="person" size={24} color="#007AFF" /> </View> <View className="flex-1"> <Text className={`font-semibold text-base ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {doctor.name} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {doctor.specialization} </Text> <Text className={`text-xs mt-1 ${ doctor.available ? 'text-emerald-700 dark:text-emerald-400' : 'text-rose-700 dark:text-rose-400' }`}> {doctor.available ? 'Available' : 'Unavailable'} </Text> </View> <Ionicons name="chevron-forward" size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> </View> </Card> </TouchableOpacity> ))} </ScrollView> </View> ); const renderDateSelection = () => { const nextWeek = DateTimeUtils.getWeekDays(); const availableDates = nextWeek.filter(day => day.date >= new Date()); return ( <View> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Select Date </Text> <Text className={`text-sm mb-4 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Appointment with {selectedDoctor?.name} </Text> <View className="flex-row flex-wrap justify-between"> {availableDates.map((day, index) => { const dateStr = DateTimeUtils.toISODate(day.date); const isSelected = selectedDate === dateStr; return ( <TouchableOpacity key={index} onPress={() => handleDateSelect(dateStr)} className={`w-[30%] p-3 rounded-xl mb-3 border ${ isSelected ? 'bg-primary-600 border-primary-600' : isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border' }`} > <Text className={`text-center font-medium ${ isSelected ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {day.dayName} </Text> <Text className={`text-center text-sm mt-1 ${ isSelected ? 'text-white' : isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {day.date.getDate()} </Text> </TouchableOpacity> ); })} </View> <Button title="Back to Doctor Selection" variant="outline" onPress={() => setStep(1)} className="mt-4" /> </View> ); }; const renderTimeSelection = () => ( <View> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Select Time </Text> <Text className={`text-sm mb-4 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {DateTimeUtils.formatDate(selectedDate)} with {selectedDoctor?.name} </Text> <View className="flex-row flex-wrap justify-between"> {availableSlots.map((time) => { const isSelected = selectedTime === time; return ( <TouchableOpacity key={time} onPress={() => handleTimeSelect(time)} className={`w-[30%] p-3 rounded-xl mb-3 border ${ isSelected ? 'bg-primary-600 border-primary-600' : isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border' }`} > <Text className={`text-center font-medium ${ isSelected ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {DateTimeUtils.formatTime(time)} </Text> </TouchableOpacity> ); })} </View> <Button title="Back to Date Selection" variant="outline" onPress={() => setStep(2)} className="mt-4" /> </View> ); const renderAppointmentDetails = () => ( <View> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointment Details </Text> {/* Summary */} <Card className="mb-4"> <Text className={`font-semibold mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointment Summary </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Doctor: {selectedDoctor?.name} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Date & Time: {DateTimeUtils.formatDateTime(selectedDate, selectedTime)} </Text> </Card> {/* Appointment Type */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointment Type </Text> <View className="flex-row flex-wrap"> {appointmentTypes.map((type) => ( <TouchableOpacity key={type.value} onPress={() => setAppointmentType(type.value)} className={`mr-3 mb-2 px-3 py-2 rounded-lg border ${ appointmentType === type.value ? 'bg-primary-600 border-primary-600' : isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border' }`} > <Text className={`text-sm ${ appointmentType === type.value ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {type.label} </Text> </TouchableOpacity> ))} </View> </View> {/* Reason */} <Input label="Reason for Visit" placeholder="Describe your symptoms or reason for the appointment..." value={reason} onChangeText={setReason} multiline numberOfLines={4} className="mb-4" required /> {/* Action Buttons */} <View className="space-y-3"> <Button title={isLoading ? "Booking..." : "Book Appointment"} onPress={handleBookAppointment} loading={isLoading} disabled={!reason.trim()} /> <Button title="Back to Time Selection" variant="outline" onPress={() => setStep(3)} /> </View> </View> ); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <ScrollView className="flex-1 px-6 pt-6" showsVerticalScrollIndicator={false}> {renderStepIndicator()} {step === 1 && renderDoctorSelection()} {step === 2 && renderDateSelection()} {step === 3 && renderTimeSelection()} {step === 4 && renderAppointmentDetails()} </ScrollView> {isLoading && <LoadingSpinner overlay message="Booking appointment..." />} </SafeAreaView> ); }; export default BookAppointmentScreen; 