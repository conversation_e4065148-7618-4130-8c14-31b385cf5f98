import React, { useState, useEffect } from 'react'; import { useTranslation } from 'react-i18next'; import { Button } from '../ui/Button'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import { useTheme } from '../../hooks/useTheme'; import { Calendar, ChevronLeft, ChevronRight, Plus, Clock, User, Stethoscope } from 'lucide-react'; interface Appointment { id: number; appointment_id: string; patient_name: string; doctor_name: string; appointment_date: string; appointment_time: string; duration_minutes: number; appointment_type: string; status: string; reason_for_visit: string; } interface CalendarDay { date: Date; isCurrentMonth: boolean; isToday: boolean; appointments: Appointment[]; } const AppointmentCalendar: React.FC = () => { const { t } = useTranslation(); const { isDark } = useTheme(); const [currentDate, setCurrentDate] = useState(new Date()); const [selectedDate, setSelectedDate] = useState<Date | null>(null); const [appointments, setAppointments] = useState<Appointment[]>([]); const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month'); // Mock appointments data useEffect(() => { const mockAppointments: Appointment[] = [ { id: 1, appointment_id: 'A000001', patient_name: 'John Doe', doctor_name: 'Dr. Smith', appointment_date: '2024-01-15', appointment_time: '09:00', duration_minutes: 30, appointment_type: 'consultation', status: 'confirmed', reason_for_visit: 'Regular checkup', }, { id: 2, appointment_id: 'A000002', patient_name: 'Jane Smith', doctor_name: 'Dr. Johnson', appointment_date: '2024-01-15', appointment_time: '10:30', duration_minutes: 45, appointment_type: 'follow_up', status: 'scheduled', reason_for_visit: 'Follow-up consultation', }, { id: 3, appointment_id: 'A000003', patient_name: 'Robert Wilson', doctor_name: 'Dr. Brown', appointment_date: '2024-01-16', appointment_time: '14:00', duration_minutes: 60, appointment_type: 'consultation', status: 'confirmed', reason_for_visit: 'Initial consultation', }, ]; setAppointments(mockAppointments); }, []); const getDaysInMonth = (date: Date): CalendarDay[] => { const year = date.getFullYear(); const month = date.getMonth(); const firstDay = new Date(year, month, 1); const lastDay = new Date(year, month + 1, 0); const startDate = new Date(firstDay); startDate.setDate(startDate.getDate() - firstDay.getDay()); const days: CalendarDay[] = []; const today = new Date(); for (let i = 0; i < 42; i++) { const currentDay = new Date(startDate); currentDay.setDate(startDate.getDate() + i); const dayAppointments = appointments.filter(apt => apt.appointment_date === currentDay.toISOString().split('T')[0] ); days.push({ date: currentDay, isCurrentMonth: currentDay.getMonth() === month, isToday: currentDay.toDateString() === today.toDateString(), appointments: dayAppointments, }); } return days; }; const navigateMonth = (direction: 'prev' | 'next') => { const newDate = new Date(currentDate); newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1)); setCurrentDate(newDate); }; const getStatusColor = (status: string) => { switch (status) { case 'confirmed': return 'status-success'; case 'scheduled': return 'status-info'; case 'cancelled': return 'status-error'; case 'completed': return 'bg-muted text-muted-foreground border-border'; default: return 'status-warning'; } }; const days = getDaysInMonth(currentDate); const monthNames = [ t('appointments.january'), t('appointments.february'), t('appointments.march'), t('appointments.april'), t('appointments.may'), t('appointments.june'), t('appointments.july'), t('appointments.august'), t('appointments.september'), t('appointments.october'), t('appointments.november'), t('appointments.december') ]; const dayNames = [ t('appointments.sunday'), t('appointments.monday'), t('appointments.tuesday'), t('appointments.wednesday'), t('appointments.thursday'), t('appointments.friday'), t('appointments.saturday') ]; return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto"> <Card variant="glass" className="shadow-xl"> {/* Calendar Header */} <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg"> <Calendar className="w-6 h-6 text-white" /> </div> <div> <CardTitle className="text-2xl font-bold macos-text-primary"> {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()} </CardTitle> <CardDescription className="macos-text-secondary"> {t('appointments.manageAppointments')} </CardDescription> </div> <div className="flex items-center space-x-2"> <Button variant="glass" size="sm" onClick={() => navigateMonth('prev')} className="flex items-center gap-1" > <ChevronLeft className="w-4 h-4" /> </Button> <Button variant="glass" size="sm" onClick={() => setCurrentDate(new Date())} > {t('appointments.today')} </Button> <Button variant="glass" size="sm" onClick={() => navigateMonth('next')} className="flex items-center gap-1" > <ChevronRight className="w-4 h-4" /> </Button> </div> </div> <div className="flex items-center space-x-3"> <div className="flex rounded-xl glass border-0 overflow-hidden"> {(['month', 'week', 'day'] as const).map((mode) => ( <button key={mode} onClick={() => setViewMode(mode)} className={`px-4 py-2 text-sm font-medium capitalize macos-transition ${ viewMode === mode ? 'bg-blue-600 text-white shadow-lg' : 'macos-text-secondary hover:macos-text-primary hover:glass-hover' }`} > {t(`appointments.${mode}`)} </button> ))} </div> <Button variant="glass" className="flex items-center gap-2"> <Plus className="w-4 h-4" /> {t('appointments.newAppointment')} </Button> </div> </div> </CardHeader> {/* Calendar Grid */} <CardContent> {/* Day Headers */} <div className="grid grid-cols-7 gap-2 mb-4"> {dayNames.map((day) => ( <div key={day} className="p-3 text-center text-sm font-semibold macos-text-secondary glass-subtle rounded-lg" > {day} </div> ))} </div> {/* Calendar Days */} <div className="grid grid-cols-7 gap-2"> {days.map((day, index) => ( <div key={index} className={`min-h-[120px] p-3 glass-subtle rounded-xl cursor-pointer macos-transition hover:glass-hover ${ !day.isCurrentMonth ? 'opacity-50' : '' } ${day.isToday ? 'ring-2 ring-blue-500 ring-opacity-50 glass-selected' : ''}`} onClick={() => setSelectedDate(day.date)} > <div className="flex items-center justify-between mb-2"> <span className={`text-sm font-semibold ${ day.isToday ? 'text-sky-700 dark:text-sky-400 dark:text-blue-400' : day.isCurrentMonth ? 'macos-text-primary' : 'macos-text-tertiary' }`} > {day.date.getDate()} </span> {day.appointments.length > 0 && ( <span className="text-xs bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full px-2 py-1 shadow-lg"> {day.appointments.length} </span> )} </div> {/* Appointments */} <div className="space-y-1"> {day.appointments.slice(0, 3).map((appointment) => ( <div key={appointment.id} className={`text-xs p-2 rounded-lg border ${getStatusColor(appointment.status)} macos-transition hover:scale-105`} title={`${appointment.appointment_time} - ${appointment.patient_name} with ${appointment.doctor_name}`} > <div className="font-semibold truncate flex items-center gap-1"> <Clock className="w-3 h-3" /> {appointment.appointment_time} </div> <div className="truncate opacity-90 flex items-center gap-1"> <User className="w-3 h-3" /> {appointment.patient_name} </div> <div className="truncate opacity-75 flex items-center gap-1"> <Stethoscope className="w-3 h-3" /> {appointment.doctor_name} </div> </div> ))} {day.appointments.length > 3 && ( <div className="text-xs macos-text-secondary text-center py-1"> +{day.appointments.length - 3} {t('appointments.more')} </div> )} </div> </div> ))} </div> </CardContent> {/* Legend */} <div className="px-6 py-4 border-t border-white/10 dark:border-white/5 glass-subtle"> <div className="flex items-center justify-center space-x-8 text-sm"> <div className="flex items-center space-x-2"> <div className="w-4 h-4 bg-green-100 border rounded-lg dark:bg-green-900/30 "></div> <span className="macos-text-secondary">{t('appointments.confirmed')}</span> </div> <div className="flex items-center space-x-2"> <div className="w-4 h-4 bg-blue-100 border rounded-lg dark:bg-blue-900/30 "></div> <span className="macos-text-secondary">{t('appointments.scheduled')}</span> </div> <div className="flex items-center space-x-2"> <div className="w-4 h-4 bg-red-100 border rounded-lg dark:bg-red-900/30 "></div> <span className="macos-text-secondary">{t('appointments.cancelled')}</span> </div> <div className="flex items-center space-x-2"> <div className="w-4 h-4 bg-muted border border-border rounded-lg dark:bg-gray-800 dark:border-gray-700"></div> <span className="macos-text-secondary">{t('appointments.completed')}</span> </div> </div> </div> </Card> </div> </div> ); }; export default AppointmentCalendar; 