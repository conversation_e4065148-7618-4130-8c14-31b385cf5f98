import React from 'react'; import { ChevronDown } from 'lucide-react'; import { cn } from '../../utils/styleConverter'; export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> { error?: boolean; options?: { value: string; label: string }[]; } const Select = React.forwardRef<HTMLSelectElement, SelectProps>( ({ className, error, options, children, ...props }, ref) => { return ( <div className="relative"> <select ref={ref} className={cn( 'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 appearance-none', error && 'border-red-500 focus:ring-red-500', className )} {...props} > {children} {options?.map((option) => ( <option key={option.value} value={option.value}> {option.label} </option> ))} </select> <ChevronDown className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 opacity-50 pointer-events-none" /> </div> ); } ); Select.displayName = 'Select'; // Additional components for more advanced select functionality const SelectTrigger = React.forwardRef< HTMLButtonElement, React.ButtonHTMLAttributes<HTMLButtonElement> & { error?: boolean } >(({ className, error, children, ...props }, ref) => ( <button ref={ref} className={cn( 'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50', error && 'border-red-500 focus:ring-red-500', className )} {...props} > {children} <ChevronDown className="h-4 w-4 opacity-50" /> </button> )); SelectTrigger.displayName = 'SelectTrigger'; const SelectContent = React.forwardRef< HTMLDivElement, React.HTMLAttributes<HTMLDivElement> >(({ className, children, ...props }, ref) => ( <div ref={ref} className={cn( 'relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-80', className )} {...props} > {children} </div> )); SelectContent.displayName = 'SelectContent'; const SelectItem = React.forwardRef< HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & { value?: string } >(({ className, children, ...props }, ref) => ( <div ref={ref} className={cn( 'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50', className )} {...props} > {children} </div> )); SelectItem.displayName = 'SelectItem'; const SelectValue = React.forwardRef< HTMLSpanElement, React.HTMLAttributes<HTMLSpanElement> & { placeholder?: string } >(({ className, placeholder, children, ...props }, ref) => ( <span ref={ref} className={cn('block truncate', className)} {...props} > {children || placeholder} </span> )); SelectValue.displayName = 'SelectValue'; export { Select, SelectTrigger, SelectContent, SelectItem, SelectValue }; 