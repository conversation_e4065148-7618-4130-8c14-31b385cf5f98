import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { Prescription, PrescriptionDetail, RefillRequest, prescriptionService } from '../../services/prescriptionService'; interface PrescriptionState { prescriptions: Prescription[]; activePrescriptions: Prescription[]; currentPrescription: PrescriptionDetail | null; refillRequests: any[]; isLoading: boolean; error: string | null; pagination: { page: number; totalPages: number; totalCount: number; }; } const initialState: PrescriptionState = { prescriptions: [], activePrescriptions: [], currentPrescription: null, refillRequests: [], isLoading: false, error: null, pagination: { page: 1, totalPages: 1, totalCount: 0, }, }; // Async thunks export const fetchMyPrescriptions = createAsyncThunk( 'prescription/fetchMyPrescriptions', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await prescriptionService.getMyPrescriptions(page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch prescriptions'); } } ); export const fetchActivePrescriptions = createAsyncThunk( 'prescription/fetchActivePrescriptions', async (_, { rejectWithValue }) => { try { const response = await prescriptionService.getActivePrescriptions(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch active prescriptions'); } } ); export const fetchPrescriptionById = createAsyncThunk( 'prescription/fetchPrescriptionById', async (id: number, { rejectWithValue }) => { try { const response = await prescriptionService.getPrescriptionById(id); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch prescription details'); } } ); export const requestRefill = createAsyncThunk( 'prescription/requestRefill', async (refillData: RefillRequest, { rejectWithValue }) => { try { const response = await prescriptionService.requestRefill(refillData); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to request refill'); } } ); export const fetchRefillRequests = createAsyncThunk( 'prescription/fetchRefillRequests', async (_, { rejectWithValue }) => { try { const response = await prescriptionService.getRefillRequests(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch refill requests'); } } ); export const cancelRefillRequest = createAsyncThunk( 'prescription/cancelRefillRequest', async (refillId: number, { rejectWithValue }) => { try { const response = await prescriptionService.cancelRefillRequest(refillId); return { refillId, ...response }; } catch (error: any) { return rejectWithValue(error.message || 'Failed to cancel refill request'); } } ); export const reportSideEffect = createAsyncThunk( 'prescription/reportSideEffect', async ({ prescriptionId, sideEffect, severity }: { prescriptionId: number; sideEffect: string; severity: string }, { rejectWithValue }) => { try { const response = await prescriptionService.reportSideEffect(prescriptionId, sideEffect, severity); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to report side effect'); } } ); const prescriptionSlice = createSlice({ name: 'prescription', initialState, reducers: { clearError: (state) => { state.error = null; }, clearCurrentPrescription: (state) => { state.currentPrescription = null; }, setPage: (state, action: PayloadAction<number>) => { state.pagination.page = action.payload; }, }, extraReducers: (builder) => { builder // Fetch my prescriptions .addCase(fetchMyPrescriptions.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMyPrescriptions.fulfilled, (state, action) => { state.isLoading = false; state.prescriptions = action.payload.results; state.pagination = { page: action.payload.page || 1, totalPages: action.payload.total_pages || 1, totalCount: action.payload.count || 0, }; }) .addCase(fetchMyPrescriptions.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch active prescriptions .addCase(fetchActivePrescriptions.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchActivePrescriptions.fulfilled, (state, action) => { state.isLoading = false; state.activePrescriptions = action.payload; }) .addCase(fetchActivePrescriptions.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch prescription by ID .addCase(fetchPrescriptionById.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchPrescriptionById.fulfilled, (state, action) => { state.isLoading = false; state.currentPrescription = action.payload; }) .addCase(fetchPrescriptionById.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Request refill .addCase(requestRefill.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(requestRefill.fulfilled, (state, action) => { state.isLoading = false; // Optionally update refill requests list }) .addCase(requestRefill.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch refill requests .addCase(fetchRefillRequests.fulfilled, (state, action) => { state.refillRequests = action.payload; }) // Cancel refill request .addCase(cancelRefillRequest.fulfilled, (state, action) => { state.refillRequests = state.refillRequests.filter( request => request.id !== action.payload.refillId ); }); }, }); export const { clearError, clearCurrentPrescription, setPage } = prescriptionSlice.actions; export default prescriptionSlice.reducer; 