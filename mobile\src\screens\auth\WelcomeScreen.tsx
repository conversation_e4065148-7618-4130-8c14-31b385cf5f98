import React from 'react'; import { View, Text, TouchableOpacity, SafeAreaView, Image } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { NativeStackNavigationProp } from '@react-navigation/native-stack'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { AuthStackParamList } from '../../navigation/AuthNavigator'; type WelcomeScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Welcome'>; const WelcomeScreen: React.FC = () => { const navigation = useNavigation(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <View className="flex-1 justify-center items-center px-8"> {/* Logo/Icon */} <View className={`w-24 h-24 rounded-3xl mb-8 justify-center items-center ${ isDark ? 'bg-dark-card' : 'bg-background' } shadow-lg`}> <Ionicons name="medical" size={48} color="#007AFF" /> </View> {/* Title */} <Text className={`text-4xl font-bold mb-4 text-center ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('auth.welcome')} </Text> {/* Subtitle */} <Text className={`text-lg text-center mb-12 ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {t('auth.welcomeSubtitle')} </Text> {/* Features */} <View className="w-full mb-12"> <FeatureItem icon="calendar-outline" title={t('dashboard.bookAppointment')} description={t('appointments.subtitle')} isDark={isDark} isRTL={isRTL} /> <FeatureItem icon="document-text-outline" title={t('navigation.medicalRecords')} description={t('medicalRecords.subtitle')} isDark={isDark} isRTL={isRTL} /> <FeatureItem icon="notifications-outline" title={t('navigation.notifications')} description={t('notifications.subtitle')} isDark={isDark} isRTL={isRTL} /> </View> {/* Buttons */} <View className="w-full space-y-4"> <TouchableOpacity onPress={() => navigation.navigate('Login')} className="bg-primary-600 py-4 px-8 rounded-xl shadow-lg" > <Text className="text-white text-lg font-semibold text-center"> {t('auth.signIn')} </Text> </TouchableOpacity> <View className={`py-4 px-8 rounded-xl border-2 opacity-50 ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' }`} > <Text className={`text-lg font-semibold text-center ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Registration Restricted </Text> <Text className={`text-xs text-center mt-1 ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> Contact your administrator </Text> </View> </View> </View> </SafeAreaView> ); }; interface FeatureItemProps { icon: keyof typeof Ionicons.glyphMap; title: string; description: string; isDark: boolean; isRTL: boolean; } const FeatureItem: React.FC<FeatureItemProps> = ({ icon, title, description, isDark, isRTL }) => ( <View className={`flex-row items-center mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}> <View className={`w-12 h-12 rounded-xl ${isRTL ? 'ml-4' : 'mr-4'} justify-center items-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name={icon} size={24} color="#007AFF" /> </View> <View className="flex-1"> <Text className={`text-lg font-semibold mb-1 ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {title} </Text> <Text className={`text-sm ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {description} </Text> </View> </View> ); export default WelcomeScreen; 