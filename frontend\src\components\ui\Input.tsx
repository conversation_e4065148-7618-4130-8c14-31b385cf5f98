import * as React from "react" import { cva, type VariantProps } from "class-variance-authority" import { cn } from "../../lib/utils" const inputVariants = cva( "flex w-full text-sm macos-transition macos-focus disabled:cursor-not-allowed disabled:opacity-50 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:macos-text-tertiary", { variants: { variant: { default: "h-10 macos-rounded border border-input bg-background px-3 py-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", glass: "macos-input macos-text-primary", outline: "h-10 macos-rounded border-2 border-border bg-transparent px-3 py-2 focus:border-primary", filled: "h-10 macos-rounded bg-secondary px-3 py-2 border-0 focus:bg-secondary/80", }, size: { default: "h-10 px-3 py-2", sm: "h-8 px-2 py-1 text-xs", lg: "h-12 px-4 py-3 text-base", }, }, defaultVariants: { variant: "default", size: "default", }, } ) export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement>, VariantProps<typeof inputVariants> {} const Input = React.forwardRef<HTMLInputElement, InputProps>( ({ className, variant, size, type, ...props }, ref) => { return ( <input type={type} className={cn(inputVariants({ variant, size, className }))} ref={ref} {...props} /> ) } ) Input.displayName = "Input" export { Input, inputVariants } 