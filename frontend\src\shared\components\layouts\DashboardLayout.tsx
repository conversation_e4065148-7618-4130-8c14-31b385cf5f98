/** * DashboardLayout Component * Reusable layout component for dashboard pages with consistent header, actions, and content structure */ import React from 'react'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'; import { Button } from '../../../components/ui/Button'; import { Badge } from '../../../components/ui/badge'; import { cn } from '../../../lib/utils'; import { useTranslation } from 'react-i18next'; interface DashboardLayoutProps { title: string; subtitle?: string; description?: string; icon?: React.ComponentType<{ className?: string }>; iconColor?: string; actions?: React.ReactNode; badges?: Array<{ label: string; variant?: 'default' | 'secondary' | 'destructive' | 'outline'; icon?: React.ComponentType<{ className?: string }>; }>; children: React.ReactNode; className?: string; headerClassName?: string; contentClassName?: string; maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full'; spacing?: 'sm' | 'md' | 'lg'; variant?: 'default' | 'glass' | 'minimal'; } const DashboardLayout: React.FC<DashboardLayoutProps> = ({ title, subtitle, description, icon: Icon, iconColor = 'feature-blue', actions, badges = [], children, className, headerClassName, contentClassName, maxWidth = '7xl', spacing = 'md', variant = 'glass', }) => { const { t } = useTranslation(); const getMaxWidthClass = () => { const maxWidthClasses = { sm: 'max-w-sm', md: 'max-w-md', lg: 'max-w-lg', xl: 'max-w-xl', '2xl': 'max-w-2xl', '7xl': 'max-w-7xl', full: 'max-w-full', }; return maxWidthClasses[maxWidth]; }; const getSpacingClass = () => { const spacingClasses = { sm: 'space-y-4', md: 'space-y-6', lg: 'space-y-8', }; return spacingClasses[spacing]; }; const getVariantClasses = () => { switch (variant) { case 'minimal': return { container: 'bg-transparent', header: 'bg-transparent border-0 shadow-none', }; case 'default': return { container: 'bg-background', header: 'bg-background border border-border shadow-sm', }; default: return { container: 'bg-gradient-to-br from-background via-muted/50 to-accent/20', header: 'glass border-0 shadow-xl', }; } }; const variantClasses = getVariantClasses(); return ( <div className={cn( 'relative', variantClasses.container, className )}> {/* Background Pattern for glass variant */} {variant === 'glass' && ( <div className="absolute inset-0 opacity-30"> <div className="absolute inset-0" style={{ backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23007AFF' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")` }} /> </div> )} <div className={cn('relative z-10', getMaxWidthClass(), 'mx-auto', getSpacingClass(), 'p-6')}> {/* Header */} <Card className={cn(variantClasses.header, headerClassName)}> <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> {Icon && ( <div className={cn( 'w-12 h-12 rounded-xl flex items-center justify-center shadow-lg', iconColor )}> <Icon className="w-6 h-6 text-white" /> </div> )} <div> <div className="flex items-center gap-3 mb-1"> <CardTitle className="text-2xl font-bold macos-text-primary"> {title} </CardTitle> {badges.map((badge, index) => { const BadgeIcon = badge.icon; return ( <Badge key={index} variant={badge.variant || 'outline'} className="border-current" > {BadgeIcon && <BadgeIcon className="w-3 h-3 mr-1" />} {badge.label} </Badge> ); })} </div> {subtitle && ( <p className="macos-text-secondary font-medium">{subtitle}</p> )} {description && ( <CardDescription className="macos-text-tertiary mt-1"> {description} </CardDescription> )} </div> </div> {actions && ( <div className="flex items-center space-x-3"> {actions} </div> )} </div> </CardHeader> </Card> {/* Content */} <div className={cn(contentClassName)}> {children} </div> </div> </div> ); }; export default DashboardLayout; // Preset layout variants export const GlassDashboardLayout: React.FC<Omit<DashboardLayoutProps, 'variant'>> = (props) => ( <DashboardLayout {...props} variant="glass" /> ); export const MinimalDashboardLayout: React.FC<Omit<DashboardLayoutProps, 'variant'>> = (props) => ( <DashboardLayout {...props} variant="minimal" /> ); export const DefaultDashboardLayout: React.FC<Omit<DashboardLayoutProps, 'variant'>> = (props) => ( <DashboardLayout {...props} variant="default" /> ); // Dashboard section component for organizing content interface DashboardSectionProps { title?: string; description?: string; actions?: React.ReactNode; children: React.ReactNode; className?: string; variant?: 'default' | 'glass' | 'transparent'; collapsible?: boolean; defaultExpanded?: boolean; } export const DashboardSection: React.FC<DashboardSectionProps> = ({ title, description, actions, children, className, variant = 'transparent', collapsible = false, defaultExpanded = true, }) => { const [expanded, setExpanded] = React.useState(defaultExpanded); const getVariantClasses = () => { switch (variant) { case 'glass': return 'glass border-0 shadow-lg'; case 'default': return 'bg-background border border-border shadow-sm'; default: return 'bg-transparent border-0 shadow-none'; } }; if (!title && !description && variant === 'transparent') { return <div className={className}>{children}</div>; } return ( <Card className={cn(getVariantClasses(), className)}> {(title || description || actions) && ( <CardHeader className={collapsible ? 'cursor-pointer' : ''} onClick={collapsible ? () => setExpanded(!expanded) : undefined}> <div className="flex items-center justify-between"> <div> {title && ( <CardTitle className="text-lg font-semibold macos-text-primary"> {title} </CardTitle> )} {description && ( <CardDescription className="macos-text-secondary"> {description} </CardDescription> )} </div> <div className="flex items-center space-x-3"> {actions} {collapsible && ( <Button variant="ghost" size="sm"> {expanded ? '−' : '+'} </Button> )} </div> </div> </CardHeader> )} {(!collapsible || expanded) && ( <CardContent className={title || description ? 'pt-0' : ''}> {children} </CardContent> )} </Card> ); }; // Quick actions component for dashboard interface QuickActionsProps { actions: Array<{ id: string; title: string; description: string; icon: React.ComponentType<{ className?: string }>; onClick: () => void; disabled?: boolean; badge?: string | number; }>; columns?: 2 | 3 | 4; className?: string; } export const QuickActions: React.FC<QuickActionsProps> = ({ actions, columns = 4, className, }) => { const getGridClasses = () => { const colClasses = { 2: 'grid-cols-1 md:grid-cols-2', 3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3', 4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4', }; return colClasses[columns]; }; return ( <div className={cn('grid gap-4', getGridClasses(), className)}> {actions.map((action) => { const Icon = action.icon; return ( <Button key={action.id} variant="glass" className="h-auto p-4 flex flex-col items-center space-y-2 relative" onClick={action.onClick} disabled={action.disabled} > {action.badge && ( <Badge className="absolute -top-2 -right-2 px-2 py-1 text-xs"> {action.badge} </Badge> )} <Icon className="w-6 h-6 macos-accent-text" /> <div className="text-center"> <p className="font-medium macos-text-primary text-sm">{action.title}</p> <p className="text-xs macos-text-tertiary">{action.description}</p> </div> </Button> ); })} </div> ); }; 