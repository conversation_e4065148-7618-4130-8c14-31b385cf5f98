/** * Centralized token management utility for HMS Mobile * Consolidates duplicate token storage operations across services */ import * as SecureStore from 'expo-secure-store'; export interface TokenPair { access: string; refresh: string; } export class TokenManager { private static readonly ACCESS_TOKEN_KEY = 'token'; private static readonly REFRESH_TOKEN_KEY = 'refreshToken'; /** * Store authentication tokens securely */ static async storeTokens(tokens: TokenPair): Promise<void> { try { await Promise.all([ SecureStore.setItemAsync(this.ACCESS_TOKEN_KEY, tokens.access), SecureStore.setItemAsync(this.REFRESH_TOKEN_KEY, tokens.refresh) ]); } catch (error) { console.error('Failed to store tokens:', error); throw new Error('Failed to store authentication tokens'); } } /** * Get stored access token */ static async getAccessToken(): Promise<string | null> { try { return await SecureStore.getItemAsync(this.ACCESS_TOKEN_KEY); } catch (error) { console.error('Failed to get access token:', error); return null; } } /** * Get stored refresh token */ static async getRefreshToken(): Promise<string | null> { try { return await SecureStore.getItemAsync(this.REFRESH_TOKEN_KEY); } catch (error) { console.error('Failed to get refresh token:', error); return null; } } /** * Get both tokens */ static async getTokens(): Promise<TokenPair | null> { try { const [access, refresh] = await Promise.all([ this.getAccessToken(), this.getRefreshToken() ]); if (access && refresh) { return { access, refresh }; } return null; } catch (error) { console.error('Failed to get tokens:', error); return null; } } /** * Update access token (typically after refresh) */ static async updateAccessToken(accessToken: string): Promise<void> { try { await SecureStore.setItemAsync(this.ACCESS_TOKEN_KEY, accessToken); } catch (error) { console.error('Failed to update access token:', error); throw new Error('Failed to update access token'); } } /** * Clear all stored tokens */ static async clearTokens(): Promise<void> { try { await Promise.all([ SecureStore.deleteItemAsync(this.ACCESS_TOKEN_KEY), SecureStore.deleteItemAsync(this.REFRESH_TOKEN_KEY) ]); } catch (error) { console.error('Failed to clear tokens:', error); // Don't throw error here as we want to ensure cleanup happens } } /** * Check if user is authenticated (has valid tokens) */ static async isAuthenticated(): Promise<boolean> { try { const tokens = await this.getTokens(); return tokens !== null; } catch (error) { console.error('Failed to check authentication status:', error); return false; } } /** * Check if access token is expired (basic check) * Note: This is a simple check based on JWT structure */ static isTokenExpired(token: string): boolean { try { const payload = JSON.parse(atob(token.split('.')[1])); const currentTime = Math.floor(Date.now() / 1000); return payload.exp < currentTime; } catch (error) { console.error('Failed to parse token:', error); return true; // Assume expired if we can't parse } } /** * Get token expiration time */ static getTokenExpiration(token: string): Date | null { try { const payload = JSON.parse(atob(token.split('.')[1])); return new Date(payload.exp * 1000); } catch (error) { console.error('Failed to get token expiration:', error); return null; } } /** * Check if access token needs refresh (expires within 5 minutes) */ static async needsRefresh(): Promise<boolean> { try { const accessToken = await this.getAccessToken(); if (!accessToken) return false; const payload = JSON.parse(atob(accessToken.split('.')[1])); const currentTime = Math.floor(Date.now() / 1000); const fiveMinutesFromNow = currentTime + (5 * 60); // 5 minutes in seconds return payload.exp < fiveMinutesFromNow; } catch (error) { console.error('Failed to check if token needs refresh:', error); return true; // Assume needs refresh if we can't check } } /** * Get user ID from access token */ static async getUserIdFromToken(): Promise<string | null> { try { const accessToken = await this.getAccessToken(); if (!accessToken) return null; const payload = JSON.parse(atob(accessToken.split('.')[1])); return payload.user_id || payload.sub || null; } catch (error) { console.error('Failed to get user ID from token:', error); return null; } } /** * Get user role from access token */ static async getUserRoleFromToken(): Promise<string | null> { try { const accessToken = await this.getAccessToken(); if (!accessToken) return null; const payload = JSON.parse(atob(accessToken.split('.')[1])); return payload.role || null; } catch (error) { console.error('Failed to get user role from token:', error); return null; } } /** * Validate token format (basic JWT structure check) */ static isValidTokenFormat(token: string): boolean { try { const parts = token.split('.'); if (parts.length !== 3) return false; // Try to decode the payload JSON.parse(atob(parts[1])); return true; } catch (error) { return false; } } /** * Get authorization header value */ static async getAuthorizationHeader(): Promise<string | null> { try { const accessToken = await this.getAccessToken(); return accessToken ? `Bearer ${accessToken}` : null; } catch (error) { console.error('Failed to get authorization header:', error); return null; } } /** * Handle token refresh response */ static async handleTokenRefresh(response: { access: string; refresh?: string }): Promise<void> { try { await this.updateAccessToken(response.access); // Update refresh token if provided if (response.refresh) { await SecureStore.setItemAsync(this.REFRESH_TOKEN_KEY, response.refresh); } } catch (error) { console.error('Failed to handle token refresh:', error); throw new Error('Failed to update tokens after refresh'); } } /** * Debug method to log token info (development only) */ static async debugTokenInfo(): Promise<void> { if (__DEV__) { try { const tokens = await this.getTokens(); if (tokens) { console.log('Token Debug Info:'); console.log('- Has access token:', !!tokens.access); console.log('- Has refresh token:', !!tokens.refresh); console.log('- Access token expired:', this.isTokenExpired(tokens.access)); console.log('- Needs refresh:', await this.needsRefresh()); console.log('- User ID:', await this.getUserIdFromToken()); console.log('- User role:', await this.getUserRoleFromToken()); } else { console.log('No tokens found'); } } catch (error) { console.error('Failed to debug token info:', error); } } } } 