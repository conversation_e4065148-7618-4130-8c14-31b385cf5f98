import * as SecureStore from 'expo-secure-store'; /** * Secure storage utilities for HMS Mobile * Provides consistent interface for storing sensitive data */ export class SecureStorage { /** * Store a value securely */ static async setItem(key: string, value: string): Promise<void> { try { await SecureStore.setItemAsync(key, value); } catch (error) { console.error(`Failed to store ${key}:`, error); throw error; } } /** * Retrieve a value securely */ static async getItem(key: string): Promise<string | null> { try { return await SecureStore.getItemAsync(key); } catch (error) { console.error(`Failed to retrieve ${key}:`, error); return null; } } /** * Remove a value securely */ static async removeItem(key: string): Promise<void> { try { await SecureStore.deleteItemAsync(key); } catch (error) { console.error(`Failed to remove ${key}:`, error); throw error; } } /** * Clear all stored values */ static async clear(): Promise<void> { try { // Clear known keys const keys = ['token', 'refreshToken', 'user']; await Promise.all(keys.map(key => this.removeItem(key))); } catch (error) { console.error('Failed to clear storage:', error); throw error; } } /** * Check if a key exists */ static async hasItem(key: string): Promise<boolean> { try { const value = await SecureStore.getItemAsync(key); return value !== null; } catch (error) { console.error(`Failed to check ${key}:`, error); return false; } } } // Token management utilities export const TokenStorage = { async setTokens(accessToken: string, refreshToken: string): Promise<void> { await Promise.all([ SecureStorage.setItem('token', accessToken), SecureStorage.setItem('refreshToken', refreshToken), ]); }, async getAccessToken(): Promise<string | null> { return SecureStorage.getItem('token'); }, async getRefreshToken(): Promise<string | null> { return SecureStorage.getItem('refreshToken'); }, async clearTokens(): Promise<void> { await Promise.all([ SecureStorage.removeItem('token'), SecureStorage.removeItem('refreshToken'), ]); }, async hasValidTokens(): Promise<boolean> { const [accessToken, refreshToken] = await Promise.all([ SecureStorage.getItem('token'), SecureStorage.getItem('refreshToken'), ]); return !!(accessToken && refreshToken); }, }; 