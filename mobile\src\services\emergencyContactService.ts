import { apiService } from './api'; export interface EmergencyContact { id: number; name: string; type: 'hospital' | 'ambulance' | 'police' | 'fire' | 'poison' | 'personal'; phone: string; address?: string; description: string; available24h: boolean; icon: string; created_at: string; updated_at: string; } export interface PersonalContact { id: number; name: string; relationship: string; phone: string; email?: string; isPrimary: boolean; patient_id: string; created_at: string; updated_at: string; } export interface CreatePersonalContactData { name: string; relationship: string; phone: string; email?: string; isPrimary: boolean; } export interface UpdatePersonalContactData extends Partial<CreatePersonalContactData> { id: number; } class EmergencyContactService { // System Emergency Contacts (Read-only) async getSystemEmergencyContacts(): Promise<EmergencyContact[]> { return apiService.get<EmergencyContact[]>(`/emergency/system-contacts/`); } // Personal Emergency Contacts (Full CRUD) async getMyEmergencyContacts(): Promise<PersonalContact[]> { return apiService.get<PersonalContact[]>(`/emergency/my-contacts/`); } async getPersonalContactById(id: number): Promise<PersonalContact> { return apiService.get<PersonalContact>(`/emergency/my-contacts/${id}/`); } async createPersonalContact(contactData: CreatePersonalContactData): Promise<PersonalContact> { return apiService.post<PersonalContact>(`/emergency/my-contacts/`, contactData); } async updatePersonalContact(id: number, contactData: Partial<CreatePersonalContactData>): Promise<PersonalContact> { return apiService.patch<PersonalContact>(`/emergency/my-contacts/${id}/`, contactData); } async deletePersonalContact(id: number): Promise<{ message: string }> { return apiService.delete<{ message: string }>(`/emergency/my-contacts/${id}/`); } async setPrimaryContact(id: number): Promise<PersonalContact> { return apiService.post<PersonalContact>(`/emergency/my-contacts/${id}/set-primary/`); } async sendEmergencyAlert(contactIds: number[], message: string): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/emergency/send-alert/`, { contact_ids: contactIds, message }); } async testEmergencyContact(id: number): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/emergency/my-contacts/${id}/test/`); } // Emergency Procedures and Information async getEmergencyProcedures(): Promise<any[]> { return apiService.get<any[]>(`/emergency/procedures/`); } async getNearbyHospitals(latitude: number, longitude: number, radius = 10): Promise<any[]> { return apiService.get<any[]>(`/emergency/nearby-hospitals/?lat=${latitude}&lng=${longitude}&radius=${radius}`); } } export const emergencyContactService = new EmergencyContactService(); 