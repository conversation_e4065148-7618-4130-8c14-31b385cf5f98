import React, { useState, useEffect } from 'react'; import { useTranslation } from 'react-i18next'; import { Button } from '../ui/Button'; import DataTable from '../ui/DataTable'; import CRUDModal from '../ui/CRUDModal'; import { crudService } from '../../services/crudService'; import { Loader2, Edit, Trash2, Plus } from 'lucide-react'; interface InventoryItem { id: number; sku: string; name: string; category: string; current_stock: number; minimum_stock: number; unit_cost: number; item_type: string; supplier: string; expiry_date?: string; } interface StockAlert { id: number; item_name: string; current_stock: number; minimum_stock: number; alert_type: 'low_stock' | 'expired' | 'expiring_soon'; days_until_expiry?: number; } const InventoryManagement: React.FC = () => { const { t } = useTranslation(); const [activeTab, setActiveTab] = useState<'inventory' | 'alerts' | 'orders' | 'reports'>('inventory'); const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]); const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]); const [loading, setLoading] = useState(true); const [showModal, setShowModal] = useState(false); const [modalMode, setModalMode] = useState<'create' | 'edit'>('create'); const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null); const [actionLoading, setActionLoading] = useState<number | null>(null); // Load inventory from API useEffect(() => { loadInventory(); }, []); const loadInventory = async () => { setLoading(true); try { const response = await crudService.getInventoryItems(); setInventoryItems(response.results || []); } catch (error) { console.error('Failed to load inventory:', error); // Fallback to mock data if API fails const mockInventory: InventoryItem[] = [ { id: 1, sku: 'MED001', name: 'Paracetamol 500mg', category: 'Medicines', current_stock: 150, minimum_stock: 50, unit_cost: 0.25, item_type: 'medicine', supplier: 'PharmaCorp', expiry_date: '2024-12-31', }, { id: 2, sku: 'EQP001', name: 'Digital Thermometer', category: 'Equipment', current_stock: 25, minimum_stock: 10, unit_cost: 45.00, item_type: 'equipment', supplier: 'MedTech Solutions', }, { id: 3, sku: 'SUP001', name: 'Surgical Gloves (Box)', category: 'Supplies', current_stock: 8, minimum_stock: 20, unit_cost: 12.50, item_type: 'supplies', supplier: 'MedSupply Inc', }, { id: 4, sku: 'MED002', name: 'Insulin Injection', category: 'Medicines', current_stock: 30, minimum_stock: 15, unit_cost: 25.00, item_type: 'medicine', supplier: 'BioPharma', expiry_date: '2024-03-15', }, ]; const mockAlerts: StockAlert[] = [ { id: 1, item_name: 'Surgical Gloves (Box)', current_stock: 8, minimum_stock: 20, alert_type: 'low_stock', }, { id: 2, item_name: 'Insulin Injection', current_stock: 30, minimum_stock: 15, alert_type: 'expiring_soon', days_until_expiry: 45, }, ]; setInventoryItems(mockInventory); setStockAlerts(mockAlerts); } finally { setLoading(false); } }; // CRUD Handlers const handleCreateItem = () => { setSelectedItem(null); setModalMode('create'); setShowModal(true); }; const handleEditItem = (item: InventoryItem) => { setSelectedItem(item); setModalMode('edit'); setShowModal(true); }; const handleDeleteItem = async (itemId: number) => { if (!confirm('Are you sure you want to delete this item?')) return; setActionLoading(itemId); try { await crudService.deleteInventoryItem(itemId); await loadInventory(); } catch (error) { console.error('Failed to delete item:', error); alert('Failed to delete item. Please try again.'); } finally { setActionLoading(null); } }; const handleSubmitItem = async (itemData: any) => { try { if (modalMode === 'create') { await crudService.createInventoryItem(itemData); } else if (selectedItem) { await crudService.updateInventoryItem(selectedItem.id, itemData); } await loadInventory(); } catch (error) { console.error('Failed to save item:', error); throw error; } }; // Additional handlers const handleImportItems = () => { // Create a file input for CSV import const input = document.createElement('input'); input.type = 'file'; input.accept = '.csv,.xlsx,.xls'; input.onchange = (e) => { const file = (e.target as HTMLInputElement).files?.[0]; if (file) { // For now, show a placeholder message alert(`Import functionality will process: ${file.name}\n\nThis feature will be implemented to:\n- Parse CSV/Excel files\n- Validate inventory data\n- Bulk import items\n- Show import results`); // TODO: Implement actual file parsing and import logic // const formData = new FormData(); // formData.append('file', file); // await crudService.importInventoryItems(formData); } }; input.click(); }; const handleReorderItem = (item: InventoryItem) => { const quantity = prompt(`How many units of "${item.name}" would you like to reorder?`, '10'); if (quantity && !isNaN(Number(quantity))) { alert(`Reorder request created:\n\nItem: ${item.name}\nQuantity: ${quantity}\nSupplier: ${item.supplier}\n\nThis will be sent to the procurement department.`); // TODO: Implement actual reorder logic // await crudService.createReorderRequest({ // item_id: item.id, // quantity: Number(quantity), // supplier: item.supplier // }); } }; // Form fields for inventory modal const inventoryFormFields = [ { key: 'sku', label: 'SKU', type: 'text' as const, required: true }, { key: 'name', label: 'Item Name', type: 'text' as const, required: true }, { key: 'category', label: 'Category', type: 'text' as const, required: true }, { key: 'current_stock', label: 'Current Stock', type: 'number' as const, required: true }, { key: 'minimum_stock', label: 'Minimum Stock', type: 'number' as const, required: true }, { key: 'unit_cost', label: 'Unit Cost', type: 'number' as const, required: true }, { key: 'item_type', label: 'Item Type', type: 'select' as const, required: true, options: [ { value: 'medicine', label: 'Medicine' }, { value: 'equipment', label: 'Equipment' }, { value: 'supplies', label: 'Supplies' } ] }, { key: 'supplier', label: 'Supplier', type: 'text' as const, required: true }, { key: 'expiry_date', label: 'Expiry Date', type: 'date' as const } ]; const inventoryColumns = [ { key: 'sku', title: t('inventory.sku'), sortable: true, render: (value: string) => ( <span className="font-medium text-sky-700 dark:text-sky-400">{value}</span> ), }, { key: 'name', title: t('inventory.itemName'), sortable: true, render: (value: string, record: InventoryItem) => ( <div> <div className="font-medium text-foreground">{value}</div> <div className="text-sm text-gray-500">{record.category}</div> </div> ), }, { key: 'item_type', title: t('inventory.type'), render: (value: string) => ( <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-info capitalize"> {value} </span> ), }, { key: 'current_stock', title: t('inventory.stock'), sortable: true, render: (value: any, record: InventoryItem) => { const numValue = typeof value === 'number' ? value : parseInt(value) || 0; const minStock = typeof record.minimum_stock === 'number' ? record.minimum_stock : parseInt(record.minimum_stock) || 0; const isLowStock = numValue <= minStock; return ( <div className="flex items-center"> <span className={`font-medium ${isLowStock ? 'text-rose-700 dark:text-rose-400' : 'text-foreground'}`}> {numValue} </span> {isLowStock && ( <svg className="w-4 h-4 text-red-500 ml-1" fill="currentColor" viewBox="0 0 20 20"> <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" /> </svg> )} </div> ); }, }, { key: 'minimum_stock', title: t('inventory.minStock'), sortable: true, render: (value: any) => { const numValue = typeof value === 'number' ? value : parseInt(value) || 0; return numValue.toString(); }, }, { key: 'unit_cost', title: t('inventory.unitCost'), sortable: true, render: (value: any) => { const numValue = typeof value === 'number' ? value : parseFloat(value); return isNaN(numValue) ? '$0.00' : `$${numValue.toFixed(2)}`; }, }, { key: 'supplier', title: t('inventory.supplier'), render: (value: any) => { if (typeof value === 'object' && value !== null) { return value.name || value.supplier_name || JSON.stringify(value); } return value ? String(value) : '-'; }, }, { key: 'expiry_date', title: t('inventory.expiry'), render: (value: string) => { if (!value) return '-'; const expiryDate = new Date(value); const today = new Date(); const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)); let colorClass = 'text-foreground'; if (daysUntilExpiry < 0) colorClass = 'text-rose-700 dark:text-rose-400'; else if (daysUntilExpiry < 30) colorClass = 'text-orange-600'; else if (daysUntilExpiry < 90) colorClass = 'text-amber-700 dark:text-amber-400'; return ( <span className={colorClass}> {expiryDate.toLocaleDateString()} </span> ); }, }, ]; const alertColumns = [ { key: 'item_name', title: t('inventory.itemName'), sortable: true, render: (value: any) => { if (typeof value === 'object' && value !== null) { return value.name || value.item_name || JSON.stringify(value); } return value ? String(value) : '-'; }, }, { key: 'alert_type', title: t('inventory.alertType'), render: (value: string) => { const colors = { low_stock: 'status-error', expired: 'status-error', expiring_soon: 'bg-orange-100 text-orange-800', }; return ( <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[value as keyof typeof colors]}`}> {value.replace('_', ' ').toUpperCase()} </span> ); }, }, { key: 'current_stock', title: t('inventory.currentStock'), render: (value: any) => { const numValue = typeof value === 'number' ? value : parseInt(value) || 0; return numValue.toString(); }, }, { key: 'minimum_stock', title: t('inventory.minStock'), render: (value: any) => { const numValue = typeof value === 'number' ? value : parseInt(value) || 0; return numValue.toString(); }, }, { key: 'days_until_expiry', title: t('inventory.daysUntilExpiry'), render: (value: any) => { const numValue = typeof value === 'number' ? value : parseInt(value); return !isNaN(numValue) && numValue > 0 ? `${numValue} days` : '-'; }, }, ]; const renderInventoryActions = (record: InventoryItem) => ( <div className="flex items-center space-x-2"> <Button size="sm" variant="outline" onClick={() => handleEditItem(record)} > <Edit className="w-4 h-4 mr-1" /> {t('inventory.edit')} </Button> <Button size="sm" variant="destructive" onClick={() => handleDeleteItem(record.id)} disabled={actionLoading === record.id} > {actionLoading === record.id ? ( <Loader2 className="w-4 h-4 mr-1 animate-spin" /> ) : ( <Trash2 className="w-4 h-4 mr-1" /> )} Delete </Button> <Button size="sm" variant="outline" onClick={() => handleReorderItem(record)} > {t('inventory.reorder')} </Button> </div> ); const renderAlertActions = (record: StockAlert) => ( <div className="flex items-center space-x-2"> <Button size="sm"> {t('inventory.resolve')} </Button> <Button size="sm" variant="outline"> {t('inventory.orderNow')} </Button> </div> ); const renderTabContent = () => { switch (activeTab) { case 'inventory': return ( <DataTable data={inventoryItems} columns={inventoryColumns} loading={loading} actions={{ title: t('common.actions'), render: renderInventoryActions, }} /> ); case 'alerts': return ( <DataTable data={stockAlerts} columns={alertColumns} loading={loading} actions={{ title: t('common.actions'), render: renderAlertActions, }} /> ); case 'orders': return ( <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-8 rounded-lg text-center"> <h3 className="text-lg font-medium text-foreground mb-2">{t('inventory.purchaseOrders')}</h3> <p className="text-muted-foreground">{t('inventory.purchaseOrdersFeatures')}</p> </div> ); case 'reports': return ( <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-8 rounded-lg text-center"> <h3 className="text-lg font-medium text-foreground mb-2">{t('inventory.inventoryReports')}</h3> <p className="text-muted-foreground">{t('inventory.inventoryReportsFeatures')}</p> </div> ); default: return null; } }; const totalValue = inventoryItems.reduce((sum, item) => { const stock = typeof item.current_stock === 'number' ? item.current_stock : parseInt(item.current_stock) || 0; const cost = typeof item.unit_cost === 'number' ? item.unit_cost : parseFloat(item.unit_cost) || 0; return sum + (stock * cost); }, 0); const lowStockItems = inventoryItems.filter(item => { const stock = typeof item.current_stock === 'number' ? item.current_stock : parseInt(item.current_stock) || 0; const minStock = typeof item.minimum_stock === 'number' ? item.minimum_stock : parseInt(item.minimum_stock) || 0; return stock <= minStock; }).length; return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */} <div className="flex items-center justify-between"> <div> <h1 className="text-2xl font-bold text-foreground">{t('inventory.title')}</h1> <p className="text-muted-foreground">{t('inventory.subtitle')}</p> </div> <div className="flex space-x-3"> <Button variant="outline" onClick={handleImportItems} > {t('inventory.importItems')} </Button> <Button onClick={handleCreateItem}> <Plus className="w-4 h-4 mr-2" /> {t('inventory.addNewItem')} </Button> </div> </div> {/* Stats Cards */} <div className="grid grid-cols-1 md:grid-cols-4 gap-6"> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-blue-100 rounded-lg"> <svg className="w-6 h-6 text-sky-700 dark:text-sky-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{t('inventory.totalItems')}</p> <p className="text-2xl font-bold text-foreground">{inventoryItems.length}</p> </div> </div> </div> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-green-100 rounded-lg"> <svg className="w-6 h-6 text-emerald-700 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{t('inventory.totalValue')}</p> <p className="text-2xl font-bold text-foreground">${(typeof totalValue === 'number' ? totalValue : 0).toLocaleString()}</p> </div> </div> </div> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-red-100 rounded-lg"> <svg className="w-6 h-6 text-rose-700 dark:text-rose-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{t('inventory.lowStockAlerts')}</p> <p className="text-2xl font-bold text-foreground">{lowStockItems}</p> </div> </div> </div> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-orange-100 rounded-lg"> <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{t('inventory.expiringSoon')}</p> <p className="text-2xl font-bold text-foreground">2</p> </div> </div> </div> </div> {/* Tabs */} <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg rounded-lg"> <div className="border-b border-border"> <nav className="-mb-px flex space-x-8 px-6"> {[ { id: 'inventory', label: t('inventory.inventory'), icon: '📦' }, { id: 'alerts', label: t('inventory.alerts'), icon: '⚠️', count: stockAlerts.length }, { id: 'orders', label: t('inventory.purchaseOrders'), icon: '📋' }, { id: 'reports', label: t('inventory.reports'), icon: '📊' }, ].map((tab) => ( <button key={tab.id} onClick={() => setActiveTab(tab.id as any)} className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${ activeTab === tab.id ? 'border-blue-500 text-sky-700 dark:text-sky-400' : 'border-transparent text-gray-500 hover:text-foreground hover:border-border' }`} > <span className="mr-2">{tab.icon}</span> {tab.label} {tab.count && ( <span className="ml-2 status-error text-xs font-medium px-2.5 py-0.5 rounded-full"> {tab.count} </span> )} </button> ))} </nav> </div> <div className="p-6"> {renderTabContent()} </div> </div> {/* CRUD Modal */} <CRUDModal isOpen={showModal} onClose={() => setShowModal(false)} onSubmit={handleSubmitItem} title={modalMode === 'create' ? 'Add New Item' : 'Edit Item'} fields={inventoryFormFields} initialData={selectedItem || {}} mode={modalMode} /> </div> </div> ); }; export default InventoryManagement; 