import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { <PERSON>, Doctor<PERSON><PERSON><PERSON>, DoctorSearchFilters, doctorService } from '../../services/doctorService'; interface DoctorState { doctors: Doctor[]; featuredDoctors: Doctor[]; favoriteDoctors: Doctor[]; currentDoctor: <PERSON><PERSON><PERSON><PERSON> | null; specializations: Array<{ id: number; name: string; description: string }>; isLoading: boolean; error: string | null; pagination: { page: number; totalPages: number; totalCount: number; }; searchFilters: DoctorSearchFilters; } const initialState: DoctorState = { doctors: [], featuredDoctors: [], favoriteDoctors: [], currentDoctor: null, specializations: [], isLoading: false, error: null, pagination: { page: 1, totalPages: 1, totalCount: 0, }, searchFilters: {}, }; // Async thunks export const fetchDoctors = createAsyncThunk( 'doctor/fetchDoctors', async ({ page = 1, pageSize = 20, filters }: { page?: number; pageSize?: number; filters?: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> }, { rejectWithValue }) => { try { const response = await doctorService.getDoctors(page, pageSize, filters); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch doctors'); } } ); export const fetchDoctorById = createAsyncThunk( 'doctor/fetchDoctorById', async (id: number, { rejectWithValue }) => { try { const response = await doctorService.getDoctorById(id); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch doctor details'); } } ); export const searchDoctors = createAsyncThunk( 'doctor/searchDoctors', async ({ query, filters }: { query: string; filters?: DoctorSearchFilters }, { rejectWithValue }) => { try { const response = await doctorService.searchDoctors(query, filters); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to search doctors'); } } ); export const fetchDoctorsBySpecialization = createAsyncThunk( 'doctor/fetchDoctorsBySpecialization', async (specialization: string, { rejectWithValue }) => { try { const response = await doctorService.getDoctorsBySpecialization(specialization); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch doctors by specialization'); } } ); export const fetchFeaturedDoctors = createAsyncThunk( 'doctor/fetchFeaturedDoctors', async (_, { rejectWithValue }) => { try { const response = await doctorService.getFeaturedDoctors(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch featured doctors'); } } ); export const fetchSpecializations = createAsyncThunk( 'doctor/fetchSpecializations', async (_, { rejectWithValue }) => { try { const response = await doctorService.getSpecializations(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch specializations'); } } ); export const fetchFavoriteDoctors = createAsyncThunk( 'doctor/fetchFavoriteDoctors', async (_, { rejectWithValue }) => { try { const response = await doctorService.getFavoriteDoctors(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch favorite doctors'); } } ); export const addDoctorToFavorites = createAsyncThunk( 'doctor/addDoctorToFavorites', async (doctorId: number, { rejectWithValue }) => { try { await doctorService.addDoctorToFavorites(doctorId); return doctorId; } catch (error: any) { return rejectWithValue(error.message || 'Failed to add doctor to favorites'); } } ); export const removeDoctorFromFavorites = createAsyncThunk( 'doctor/removeDoctorFromFavorites', async (doctorId: number, { rejectWithValue }) => { try { await doctorService.removeDoctorFromFavorites(doctorId); return doctorId; } catch (error: any) { return rejectWithValue(error.message || 'Failed to remove doctor from favorites'); } } ); const doctorSlice = createSlice({ name: 'doctor', initialState, reducers: { clearError: (state) => { state.error = null; }, clearCurrentDoctor: (state) => { state.currentDoctor = null; }, setSearchFilters: (state, action: PayloadAction<DoctorSearchFilters>) => { state.searchFilters = action.payload; }, setPage: (state, action: PayloadAction<number>) => { state.pagination.page = action.payload; }, }, extraReducers: (builder) => { builder // Fetch doctors .addCase(fetchDoctors.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchDoctors.fulfilled, (state, action) => { state.isLoading = false; state.doctors = action.payload.results; state.pagination = { page: action.payload.page || 1, totalPages: action.payload.total_pages || 1, totalCount: action.payload.count || 0, }; }) .addCase(fetchDoctors.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch doctor by ID .addCase(fetchDoctorById.fulfilled, (state, action) => { state.currentDoctor = action.payload; }) // Search doctors .addCase(searchDoctors.fulfilled, (state, action) => { state.doctors = action.payload; }) // Fetch doctors by specialization .addCase(fetchDoctorsBySpecialization.fulfilled, (state, action) => { state.doctors = action.payload; }) // Fetch featured doctors .addCase(fetchFeaturedDoctors.fulfilled, (state, action) => { state.featuredDoctors = action.payload; }) // Fetch specializations .addCase(fetchSpecializations.fulfilled, (state, action) => { state.specializations = action.payload; }) // Fetch favorite doctors .addCase(fetchFavoriteDoctors.fulfilled, (state, action) => { state.favoriteDoctors = action.payload; }) // Add doctor to favorites .addCase(addDoctorToFavorites.fulfilled, (state, action) => { const doctor = state.doctors.find(d => d.id === action.payload); if (doctor && !state.favoriteDoctors.find(d => d.id === action.payload)) { state.favoriteDoctors.push(doctor); } }) // Remove doctor from favorites .addCase(removeDoctorFromFavorites.fulfilled, (state, action) => { state.favoriteDoctors = state.favoriteDoctors.filter(d => d.id !== action.payload); }); }, }); export const { clearError, clearCurrentDoctor, setSearchFilters, setPage } = doctorSlice.actions; export default doctorSlice.reducer; 