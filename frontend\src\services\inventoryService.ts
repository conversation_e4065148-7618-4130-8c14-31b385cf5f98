import { BaseApiService } from '../shared/services/BaseApiService'; import type { BaseEntity } from '../shared/types'; export interface Category extends BaseEntity { name: string; description?: string; } export interface Supplier extends BaseEntity { name: string; contact_person?: string; email?: string; phone?: string; address?: string; tax_id?: string; payment_terms?: string; is_active: boolean; } export interface InventoryItem extends BaseEntity { category: Category; supplier?: Supplier; name: string; description?: string; sku: string; barcode?: string; item_type: string; current_stock: number; minimum_stock: number; maximum_stock: number; reorder_level: number; unit_cost: number; selling_price: number; dosage_form?: string; strength?: string; manufacturer?: string; is_active: boolean; requires_prescription: boolean; is_low_stock: boolean; stock_value: number; } export interface StockMovement extends BaseEntity { item: InventoryItem; user: { id: number; full_name: string; }; movement_type: string; quantity: number; unit_cost: number; reference_type?: string; reference_id?: string; notes?: string; batch_number?: string; expiry_date?: string; movement_date: string; } export interface PurchaseOrder extends BaseEntity { po_number: string; supplier: Supplier; order_date: string; expected_delivery_date?: string; actual_delivery_date?: string; status: string; subtotal: number; tax_amount: number; total_amount: number; notes?: string; items: PurchaseOrderItem[]; } export interface PurchaseOrderItem extends BaseEntity { item: InventoryItem; quantity_ordered: number; quantity_received: number; unit_cost: number; total_cost: number; batch_number?: string; expiry_date?: string; quantity_pending: number; is_fully_received: boolean; } class InventoryService extends BaseApiService<InventoryItem> { constructor() { super({ baseURL: '/inventory', endpoints: { list: '/items/', detail: '/items/:id/', create: '/items/', update: '/items/:id/', delete: '/items/:id/', }, }); } // Categories - using base service methods async getCategories(params?: any) { return this.get('/categories/', { params }); } async getCategory(id: number) { return this.get('/categories/:id/', { params: { id } }); } async createCategory(data: Partial<Category>) { return this.post('/categories/', data); } async updateCategory(id: number, data: Partial<Category>) { return this.patch('/categories/:id/', data, { params: { id } }); } async deleteCategory(id: number) { return this.delete('/categories/:id/', { params: { id } }); } // Suppliers - using base service methods async getSuppliers(params?: any) { return this.get('/suppliers/', { params }); } async getSupplier(id: number) { return this.get('/suppliers/:id/', { params: { id } }); } async createSupplier(data: Partial<Supplier>) { return this.post('/suppliers/', data); } async updateSupplier(id: number, data: Partial<Supplier>) { return this.patch('/suppliers/:id/', data, { params: { id } }); } async deleteSupplier(id: number) { return this.delete('/suppliers/:id/', { params: { id } }); } async getActiveSuppliers() { return this.get('/suppliers/active/'); } // Inventory Items - using inherited CRUD methods and custom endpoints async getInventoryItems(params?: any) { return this.getAll(params); } async getInventoryItem(id: number) { return this.getById(id); } async createInventoryItem(data: any) { return this.create(data); } async updateInventoryItem(id: number, data: any) { return this.update(id, data); } async deleteInventoryItem(id: number) { return this.deleteById(id); } async getLowStockItems() { return this.get('/items/low_stock/'); } async getOutOfStockItems() { return this.get('/items/out_of_stock/'); } async getMedicines() { return this.get('/items/medicines/'); } async getEquipment() { return this.get('/items/equipment/'); } async adjustStock(id: number, adjustment: number, notes?: string) { return this.customAction(id, 'adjust_stock', { adjustment, notes }); } // Stock Movements - using base service methods async getStockMovements(params?: any) { return this.get('/stock-movements/', { params }); } async createStockMovement(data: any) { return this.post('/stock-movements/', data); } // Purchase Orders - using base service methods async getPurchaseOrders(params?: any) { return this.get('/purchase-orders/', { params }); } async getPurchaseOrder(id: number) { return this.get('/purchase-orders/:id/', { params: { id } }); } async createPurchaseOrder(data: any) { return this.post('/purchase-orders/', data); } async updatePurchaseOrder(id: number, data: any) { return this.patch('/purchase-orders/:id/', data, { params: { id } }); } async deletePurchaseOrder(id: number) { return this.delete('/purchase-orders/:id/', { params: { id } }); } async confirmPurchaseOrder(id: number) { return this.post('/purchase-orders/:id/confirm/', {}, { params: { id } }); } async cancelPurchaseOrder(id: number) { return this.post('/purchase-orders/:id/cancel/', {}, { params: { id } }); } async receiveItems(id: number, items: any[]) { return this.post('/purchase-orders/:id/receive_items/', { items }, { params: { id } }); } } export default new InventoryService(); 