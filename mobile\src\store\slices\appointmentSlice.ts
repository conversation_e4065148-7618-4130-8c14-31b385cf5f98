import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { AppointmentState, Appointment, CreateAppointmentData, AppointmentSlot } from '../../types/appointment'; import { appointmentService } from '../../services/appointmentService'; const initialState: AppointmentState = { appointments: [], availableSlots: [], currentAppointment: null, isLoading: false, error: null, }; // Async thunks export const fetchAppointments = createAsyncThunk( 'appointment/fetchAppointments', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await appointmentService.getAppointments(page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch appointments'); } } ); export const fetchMyAppointments = createAsyncThunk( 'appointment/fetchMyAppointments', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await appointmentService.getMyAppointments(page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch appointments'); } } ); export const fetchUpcomingAppointments = createAsyncThunk( 'appointment/fetchUpcomingAppointments', async ({ page = 1, pageSize = 10 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await appointmentService.getUpcomingAppointments(page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch upcoming appointments'); } } ); export const fetchAppointment = createAsyncThunk( 'appointment/fetchAppointment', async (id: number, { rejectWithValue }) => { try { const appointment = await appointmentService.getAppointment(id); return appointment; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch appointment'); } } ); export const createAppointment = createAsyncThunk( 'appointment/createAppointment', async (data: CreateAppointmentData, { rejectWithValue }) => { try { const appointment = await appointmentService.createAppointment(data); return appointment; } catch (error: any) { return rejectWithValue(error.message || 'Failed to create appointment'); } } ); export const updateAppointment = createAsyncThunk( 'appointment/updateAppointment', async ({ id, data }: { id: number; data: Partial<Appointment> }, { rejectWithValue }) => { try { const appointment = await appointmentService.updateAppointment(id, data); return appointment; } catch (error: any) { return rejectWithValue(error.message || 'Failed to update appointment'); } } ); export const cancelAppointment = createAsyncThunk( 'appointment/cancelAppointment', async (id: number, { rejectWithValue }) => { try { const appointment = await appointmentService.cancelAppointment(id); return appointment; } catch (error: any) { return rejectWithValue(error.message || 'Failed to cancel appointment'); } } ); export const confirmAppointment = createAsyncThunk( 'appointment/confirmAppointment', async (id: number, { rejectWithValue }) => { try { const appointment = await appointmentService.confirmAppointment(id); return appointment; } catch (error: any) { return rejectWithValue(error.message || 'Failed to confirm appointment'); } } ); export const fetchAvailableSlots = createAsyncThunk( 'appointment/fetchAvailableSlots', async ({ doctorId, date }: { doctorId: number; date: string }, { rejectWithValue }) => { try { const slots = await appointmentService.getAvailableSlots(doctorId, date); return slots; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch available slots'); } } ); export const rescheduleAppointment = createAsyncThunk( 'appointment/rescheduleAppointment', async ({ id, newDate, newTime }: { id: number; newDate: string; newTime: string }, { rejectWithValue }) => { try { const appointment = await appointmentService.rescheduleAppointment(id, newDate, newTime); return appointment; } catch (error: any) { return rejectWithValue(error.message || 'Failed to reschedule appointment'); } } ); const appointmentSlice = createSlice({ name: 'appointment', initialState, reducers: { clearError: (state) => { state.error = null; }, setCurrentAppointment: (state, action) => { state.currentAppointment = action.payload; }, clearAppointments: (state) => { state.appointments = []; state.currentAppointment = null; state.availableSlots = []; }, clearAvailableSlots: (state) => { state.availableSlots = []; }, }, extraReducers: (builder) => { // Fetch Appointments builder .addCase(fetchAppointments.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchAppointments.fulfilled, (state, action) => { state.isLoading = false; state.appointments = action.payload.results; state.error = null; }) .addCase(fetchAppointments.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch My Appointments builder .addCase(fetchMyAppointments.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMyAppointments.fulfilled, (state, action) => { state.isLoading = false; state.appointments = action.payload.results; state.error = null; }) .addCase(fetchMyAppointments.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch Upcoming Appointments builder .addCase(fetchUpcomingAppointments.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchUpcomingAppointments.fulfilled, (state, action) => { state.isLoading = false; // For upcoming appointments, we might want to merge or replace state.appointments = action.payload.results; state.error = null; }) .addCase(fetchUpcomingAppointments.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch Appointment builder .addCase(fetchAppointment.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchAppointment.fulfilled, (state, action) => { state.isLoading = false; state.currentAppointment = action.payload; state.error = null; }) .addCase(fetchAppointment.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Create Appointment builder .addCase(createAppointment.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(createAppointment.fulfilled, (state, action) => { state.isLoading = false; state.appointments.unshift(action.payload); state.error = null; }) .addCase(createAppointment.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Update Appointment builder .addCase(updateAppointment.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(updateAppointment.fulfilled, (state, action) => { state.isLoading = false; const index = state.appointments.findIndex(a => a.id === action.payload.id); if (index !== -1) { state.appointments[index] = action.payload; } if (state.currentAppointment?.id === action.payload.id) { state.currentAppointment = action.payload; } state.error = null; }) .addCase(updateAppointment.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Cancel Appointment builder .addCase(cancelAppointment.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(cancelAppointment.fulfilled, (state, action) => { state.isLoading = false; const index = state.appointments.findIndex(a => a.id === action.payload.id); if (index !== -1) { state.appointments[index] = action.payload; } if (state.currentAppointment?.id === action.payload.id) { state.currentAppointment = action.payload; } state.error = null; }) .addCase(cancelAppointment.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Confirm Appointment builder .addCase(confirmAppointment.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(confirmAppointment.fulfilled, (state, action) => { state.isLoading = false; const index = state.appointments.findIndex(a => a.id === action.payload.id); if (index !== -1) { state.appointments[index] = action.payload; } if (state.currentAppointment?.id === action.payload.id) { state.currentAppointment = action.payload; } state.error = null; }) .addCase(confirmAppointment.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch Available Slots builder .addCase(fetchAvailableSlots.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchAvailableSlots.fulfilled, (state, action) => { state.isLoading = false; state.availableSlots = action.payload; state.error = null; }) .addCase(fetchAvailableSlots.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Reschedule Appointment builder .addCase(rescheduleAppointment.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(rescheduleAppointment.fulfilled, (state, action) => { state.isLoading = false; const index = state.appointments.findIndex(a => a.id === action.payload.id); if (index !== -1) { state.appointments[index] = action.payload; } if (state.currentAppointment?.id === action.payload.id) { state.currentAppointment = action.payload; } state.error = null; }) .addCase(rescheduleAppointment.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); }, }); export const { clearError, setCurrentAppointment, clearAppointments, clearAvailableSlots } = appointmentSlice.actions; export default appointmentSlice.reducer; 