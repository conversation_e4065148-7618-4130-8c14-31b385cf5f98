/** * Mock Patient Data * Centralized patient data to ensure consistency across components */ // Current logged-in patient data (<PERSON>000123) export const CURRENT_PATIENT_DATA = { id: 123, patient_id: 'P000123', name: '<PERSON>', age: 35, gender: 'Male', bloodType: 'O+', allergies: ['Penicillin', 'Shellfish'], chronicConditions: ['Hypertension', 'Type 2 Diabetes'], emergencyContact: '+****************', email: '<EMAIL>', phone: '+****************', address: '123 Main St, City, State', insurance: 'Blue Cross Blue Shield', // Medical History totalVisits: 24, lastVisit: '2024-12-10', // Current Medications activePrescriptions: [ { id: 1, medication: 'Lisinopril 10mg', prescribedBy: 'Dr. <PERSON>', date: '2024-12-10', duration: '3 months', instructions: 'Take once daily with food', status: 'active' }, { id: 2, medication: 'Metformin 500mg', prescribedBy: 'Dr. <PERSON>', date: '2024-11-15', duration: '6 months', instructions: 'Take twice daily before meals', status: 'active' } ], // Upcoming Appointments upcomingAppointments: [ { id: 1, date: '2024-12-15', time: '10:30 AM', doctor: 'Dr. Sarah Johnson', department: 'Cardiology', type: 'Follow-up', location: 'Room 205, 2nd Floor', status: 'confirmed', notes: 'Bring previous test results' } ], // Medical Visits History medicalVisits: [ { id: 1, date: '2024-12-10', doctor: 'Dr. Sarah Johnson', department: 'Cardiology', diagnosis: 'Hypertension Management', status: 'completed', notes: 'Blood pressure well controlled. Continue current medication.' }, { id: 2, date: '2024-11-15', doctor: 'Dr. Michael Brown', department: 'Endocrinology', diagnosis: 'Type 2 Diabetes Follow-up', status: 'completed', notes: 'HbA1c levels improved. Maintain current treatment plan.' } ] } as const; // Other mock patients for lists and doctor views export const MOCK_PATIENTS = [ { id: 1, patient_id: 'P000001', name: 'Sarah Johnson', age: 45, gender: 'Female', bloodType: 'A+', phone: '+****************', email: '<EMAIL>', address: '123 Main St, City, State', lastVisit: '2024-12-10', nextAppointment: '2024-12-20', status: 'stable', conditions: ['Hypertension', 'Type 2 Diabetes'], allergies: ['Penicillin'], emergencyContact: 'John Johnson - +****************', insurance: 'Blue Cross Blue Shield', notes: 'Regular follow-up for diabetes management' }, { id: 2, patient_id: 'P000002', name: 'Michael Brown', age: 62, gender: 'Male', bloodType: 'O-', phone: '+****************', email: '<EMAIL>', address: '456 Oak Ave, City, State', lastVisit: '2024-12-08', nextAppointment: '2024-12-18', status: 'critical', conditions: ['Heart Disease', 'High Cholesterol'], allergies: ['Shellfish', 'Latex'], emergencyContact: 'Mary Brown - +****************', insurance: 'Medicare', notes: 'Post-surgery monitoring required' }, { id: 3, patient_id: 'P000003', name: 'Emily Davis', age: 28, gender: 'Female', bloodType: 'B+', phone: '+****************', email: '<EMAIL>', address: '789 Pine St, City, State', lastVisit: '2024-12-05', nextAppointment: '2025-01-15', status: 'stable', conditions: ['Asthma'], allergies: [], emergencyContact: 'Robert Davis - +****************', insurance: 'Aetna', notes: 'Seasonal asthma management' } ] as const; // Available doctors for appointment booking export const MOCK_DOCTORS = [ { id: 1, name: 'Dr. John Smith', specialty: 'Family Medicine', experience: '20 years', rating: 4.7, department: 'General Medicine' }, { id: 2, name: 'Dr. Anna Davis', specialty: 'Internal Medicine', experience: '8 years', rating: 4.8, department: 'General Medicine' }, { id: 3, name: 'Dr. Sarah Johnson', specialty: 'Cardiology', experience: '15 years', rating: 4.9, department: 'Cardiology' }, { id: 4, name: 'Dr. Michael Brown', specialty: 'Endocrinology', experience: '12 years', rating: 4.6, department: 'Endocrinology' } ] as const; // Departments for appointment booking export const MOCK_DEPARTMENTS = [ { id: 1, name: 'General Medicine', description: 'General health and wellness', icon: '🩺', doctors: [1, 2] }, { id: 2, name: 'Cardiology', description: 'Heart and cardiovascular conditions', icon: '🫀', doctors: [3] }, { id: 3, name: 'Endocrinology', description: 'Hormone and metabolic disorders', icon: '🧬', doctors: [4] }, { id: 4, name: 'Neurology', description: 'Brain and nervous system disorders', icon: '🧠', doctors: [] }, { id: 5, name: 'Orthopedics', description: 'Bone, joint, and muscle conditions', icon: '🦴', doctors: [] }, { id: 6, name: 'Ophthalmology', description: 'Eye and vision care', icon: '👁️', doctors: [] } ] as const; 