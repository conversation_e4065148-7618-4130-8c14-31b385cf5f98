import React, { useState } from 'react'; import { View, Text, TextInput, TouchableOpacity, SafeAreaView, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { NativeStackNavigationProp } from '@react-navigation/native-stack'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { AuthStackParamList } from '../../navigation/AuthNavigator'; import { RootState, AppDispatch } from '../../store'; import { login, clearError } from '../../store/slices/authSlice'; type LoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Login'>; const LoginScreen: React.FC = () => { const navigation = useNavigation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const { isLoading, error } = useSelector((state: RootState) => state.auth); const [formData, setFormData] = useState({ username: '', password: '', }); const [showPassword, setShowPassword] = useState(false); const [showTestUsers, setShowTestUsers] = useState(false); // Test users for easy selection const testUsers = [ { username: 'admin', password: 'admin123', role: 'Administrator', icon: '👑' }, { username: 'dr.smith', password: 'doctor123', role: 'Doctor', icon: '🩺' }, { username: 'nurse.mary', password: 'nurse123', role: 'Nurse', icon: '👩‍⚕️' }, { username: 'receptionist', password: 'reception123', role: 'Receptionist', icon: '🏥' }, { username: 'patient.doe', password: 'patient123', role: 'Patient', icon: '🤒' }, ]; const handleTestUserSelect = (testUser: typeof testUsers[0]) => { setFormData({ username: testUser.username, password: testUser.password, }); setShowTestUsers(false); }; const handleInputChange = (field: string, value: string) => { setFormData(prev => ({ ...prev, [field]: value })); if (error) { dispatch(clearError()); } }; const handleLogin = async () => { if (!formData.username.trim() || !formData.password.trim()) { Alert.alert(t('common.error'), t('auth.fillAllFields')); return; } try { await dispatch(login(formData)).unwrap(); // Navigation will be handled automatically by AppNavigator } catch (error: any) { Alert.alert(t('auth.loginFailed'), error || t('auth.invalidCredentials')); } }; return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1" > <ScrollView className="flex-1" showsVerticalScrollIndicator={false}> <View className="flex-1 px-8 pt-16"> {/* Header */} <View className="items-center mb-12"> <TouchableOpacity onPress={() => navigation.goBack()} className="absolute left-0 top-0" > <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} /> </TouchableOpacity> <Text className={`text-3xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('auth.welcomeBack')} </Text> <Text className={`text-base mt-2 ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {t('auth.signInToAccount')} </Text> </View> {/* Test Users Selector */} <View className={`mb-6 border rounded-xl p-4 ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-blue-50' }`}> <TouchableOpacity onPress={() => setShowTestUsers(!showTestUsers)} className="flex-row items-center justify-between" > <View className="flex-row items-center"> <Text className="text-lg mr-2">🧪</Text> <Text className={`text-sm font-medium ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Quick Test Login </Text> </View> <Ionicons name={showTestUsers ? 'chevron-up' : 'chevron-down'} size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> </TouchableOpacity> {showTestUsers && ( <View className="mt-4 space-y-2"> {testUsers.map((user, index) => ( <TouchableOpacity key={index} onPress={() => handleTestUserSelect(user)} className={`p-3 rounded-lg border ${ isDark ? 'border-dark-border bg-dark-background' : 'border-border bg-background' }`} > <View className="flex-row items-center"> <Text className="text-lg mr-3">{user.icon}</Text> <View className="flex-1"> <Text className={`text-sm font-medium ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {user.role} </Text> <Text className={`text-xs ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {user.username} </Text> </View> </View> </TouchableOpacity> ))} <Text className={`text-xs text-center mt-2 ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> Tap any user above to auto-fill credentials </Text> </View> )} </View> {/* Form */} <View className="space-y-6"> {/* Username Field */} <View> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('auth.username')} </Text> <View className={`flex-row items-center border rounded-xl px-4 py-4 ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' }`}> <Ionicons name="person-outline" size={20} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginRight: 12 }} /> <TextInput value={formData.username} onChangeText={(value) => handleInputChange('username', value)} placeholder={t('auth.enterUsername')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`flex-1 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`} autoCapitalize="none" autoCorrect={false} /> </View> </View> {/* Password Field */} <View> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('auth.password')} </Text> <View className={`flex-row items-center border rounded-xl px-4 py-4 ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' }`}> <Ionicons name="lock-closed-outline" size={20} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginRight: 12 }} /> <TextInput value={formData.password} onChangeText={(value) => handleInputChange('password', value)} placeholder={t('auth.enterPassword')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`flex-1 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`} secureTextEntry={!showPassword} autoCapitalize="none" autoCorrect={false} /> <TouchableOpacity onPress={() => setShowPassword(!showPassword)} className="ml-2" > <Ionicons name={showPassword ? 'eye-off-outline' : 'eye-outline'} size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> </TouchableOpacity> </View> </View> {/* Error Message */} {error && ( <View className="bg-destructive/10 border border-destructive/20 rounded-xl p-4"> <Text className="text-destructive text-sm text-center"> {error} </Text> </View> )} {/* Login Button */} <TouchableOpacity onPress={handleLogin} disabled={isLoading} className={`py-4 px-8 rounded-xl shadow-lg ${ isLoading ? 'bg-gray-400' : 'bg-primary-600' }`} > <Text className="text-white text-lg font-semibold text-center"> {isLoading ? t('auth.signingIn') : t('auth.signIn')} </Text> </TouchableOpacity> {/* Registration Notice */} <View className="flex-row justify-center items-center mt-8"> <Text className={`text-base text-center ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> Need an account? Contact your system administrator </Text> </View> </View> </View> </ScrollView> </KeyboardAvoidingView> </SafeAreaView> ); }; export default LoginScreen; 