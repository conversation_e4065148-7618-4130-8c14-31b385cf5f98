import React, { useState, useEffect } from 'react'; import { useTranslation } from 'react-i18next'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import { Button } from '../ui/Button'; import { Badge } from '../ui/badge'; import { Input } from '../ui/Input'; import CRUDModal from '../ui/CRUDModal'; import { crudService } from '../../services/crudService'; import { CreditCard, Plus, Search, DollarSign, Calendar, User, Eye, Edit, Download, Filter, AlertCircle, CheckCircle, Clock, Loader2, Trash2 } from 'lucide-react'; interface Bill { id: string; patientId: string; patientName: string; serviceDate: string; dueDate: string; services: string[]; totalAmount: number; paidAmount: number; status: 'pending' | 'partial' | 'paid' | 'overdue'; paymentMethod?: string; insuranceClaim?: string; notes?: string; } const BillingManagement: React.FC = () => { const { t } = useTranslation(); const [searchTerm, setSearchTerm] = useState(''); const [selectedStatus, setSelectedStatus] = useState('all'); const [showAddForm, setShowAddForm] = useState(false); const [showModal, setShowModal] = useState(false); const [modalMode, setModalMode] = useState<'create' | 'edit'>('create'); const [selectedBill, setSelectedBill] = useState<Bill | null>(null); const [bills, setBills] = useState<Bill[]>([]); const [loading, setLoading] = useState(true); const [actionLoading, setActionLoading] = useState<string | null>(null); // Load bills from API useEffect(() => { loadBills(); }, []); const loadBills = async () => { setLoading(true); try { const response = await crudService.getInvoices(); setBills(response.results || []); } catch (error) { console.error('Failed to load bills:', error); // Fallback to mock data if API fails setBills([ { id: 'INV001', patientId: 'P001', patientName: 'Sarah Johnson', serviceDate: '2024-12-10', dueDate: '2024-12-25', services: ['Consultation', 'Blood Test', 'X-Ray'], totalAmount: 450.00, paidAmount: 450.00, status: 'paid', paymentMethod: 'Credit Card', insuranceClaim: 'INS-2024-001' }, { id: 'INV002', patientId: 'P002', patientName: 'Michael Brown', serviceDate: '2024-12-09', dueDate: '2024-12-24', services: ['Emergency Visit', 'CT Scan', 'Medication'], totalAmount: 1250.00, paidAmount: 500.00, status: 'partial', paymentMethod: 'Insurance + Cash', insuranceClaim: 'INS-2024-002' }, { id: 'INV003', patientId: 'P003', patientName: 'Emily Wilson', serviceDate: '2024-12-08', dueDate: '2024-12-23', services: ['Consultation', 'Prescription'], totalAmount: 180.00, paidAmount: 0.00, status: 'pending', notes: 'Waiting for insurance approval' }, { id: 'INV004', patientId: 'P004', patientName: 'Robert Davis', serviceDate: '2024-11-25', dueDate: '2024-12-10', services: ['Surgery', 'Hospital Stay', 'Medication'], totalAmount: 5500.00, paidAmount: 0.00, status: 'overdue', notes: 'Patient contacted, payment plan requested' } ]); } finally { setLoading(false); } }; // CRUD Handlers const handleCreateBill = () => { setSelectedBill(null); setModalMode('create'); setShowModal(true); }; const handleEditBill = (bill: Bill) => { setSelectedBill(bill); setModalMode('edit'); setShowModal(true); }; const handleDeleteBill = async (billId: string) => { if (!confirm('Are you sure you want to delete this bill?')) return; setActionLoading(billId); try { await crudService.deleteInvoice(billId); await loadBills(); } catch (error) { console.error('Failed to delete bill:', error); alert('Failed to delete bill. Please try again.'); } finally { setActionLoading(null); } }; const handleSubmitBill = async (billData: any) => { try { if (modalMode === 'create') { await crudService.createInvoice(billData); } else if (selectedBill) { await crudService.updateInvoice(selectedBill.id, billData); } await loadBills(); } catch (error) { console.error('Failed to save bill:', error); throw error; } }; // Additional handlers const handlePrintBill = (bill: Bill) => { // Create a printable version of the bill const printWindow = window.open('', '_blank'); if (printWindow) { printWindow.document.write(` <html> <head> <title>Invoice - ${bill.id}</title> <style> body { font-family: Arial, sans-serif; margin: 20px; } .header { text-align: center; margin-bottom: 30px; } .bill-details { margin-bottom: 20px; } .services { margin-top: 20px; } .total { font-weight: bold; font-size: 18px; margin-top: 20px; } </style> </head> <body> <div class="header"> <h1>Medical Invoice</h1> <p>Invoice ID: ${bill.id}</p> </div> <div class="bill-details"> <p><strong>Patient:</strong> ${bill.patientName}</p> <p><strong>Patient ID:</strong> ${bill.patientId}</p> <p><strong>Service Date:</strong> ${bill.serviceDate}</p> <p><strong>Due Date:</strong> ${bill.dueDate}</p> <p><strong>Status:</strong> ${bill.status}</p> </div> <div class="services"> <h3>Services:</h3> <p>${bill.services}</p> </div> <div class="total"> <p>Total Amount: $${bill.totalAmount}</p> <p>Paid Amount: $${bill.paidAmount}</p> <p>Balance: $${bill.totalAmount - bill.paidAmount}</p> </div> </body> </html> `); printWindow.document.close(); printWindow.print(); } }; // Form fields for bill modal const billFormFields = [ { key: 'patientName', label: 'Patient Name', type: 'text' as const, required: true }, { key: 'patientId', label: 'Patient ID', type: 'text' as const, required: true }, { key: 'totalAmount', label: 'Total Amount', type: 'number' as const, required: true }, { key: 'paidAmount', label: 'Paid Amount', type: 'number' as const, required: true }, { key: 'status', label: 'Status', type: 'select' as const, required: true, options: [ { value: 'pending', label: 'Pending' }, { value: 'partial', label: 'Partial' }, { value: 'paid', label: 'Paid' }, { value: 'overdue', label: 'Overdue' } ] }, { key: 'dueDate', label: 'Due Date', type: 'date' as const, required: true }, { key: 'serviceDate', label: 'Service Date', type: 'date' as const, required: true }, { key: 'services', label: 'Services', type: 'textarea' as const, placeholder: 'Enter services separated by commas' }, { key: 'paymentMethod', label: 'Payment Method', type: 'text' as const }, { key: 'insuranceClaim', label: 'Insurance Claim', type: 'text' as const }, { key: 'notes', label: 'Notes', type: 'textarea' as const } ]; const filteredBills = bills.filter(bill => { // Safe access to properties with fallbacks const patientName = bill.patientName || bill.patient_name || ''; const billId = bill.id || ''; const patientId = bill.patientId || bill.patient_id || ''; const status = bill.status || ''; const matchesSearch = patientName.toLowerCase().includes(searchTerm.toLowerCase()) || billId.toString().toLowerCase().includes(searchTerm.toLowerCase()) || patientId.toLowerCase().includes(searchTerm.toLowerCase()); const matchesStatus = selectedStatus === 'all' || status === selectedStatus; return matchesSearch && matchesStatus; }); const getStatusColor = (status: string) => { switch (status) { case 'paid': return 'status-success'; case 'partial': return 'status-warning'; case 'pending': return 'status-info'; case 'overdue': return 'status-error'; default: return 'bg-muted text-muted-foreground border-border'; } }; const getStatusIcon = (status: string) => { switch (status) { case 'paid': return <CheckCircle className="w-4 h-4 text-emerald-700 dark:text-emerald-400" />; case 'partial': return <Clock className="w-4 h-4 text-amber-700 dark:text-amber-400" />; case 'pending': return <Clock className="w-4 h-4 text-sky-700 dark:text-sky-400" />; case 'overdue': return <AlertCircle className="w-4 h-4 text-rose-700 dark:text-rose-400" />; default: return <CreditCard className="w-4 h-4 text-muted-foreground" />; } }; const formatCurrency = (amount: number) => { return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount); }; const calculateTotalRevenue = () => { return bills.reduce((total, bill) => total + bill.paidAmount, 0); }; const calculatePendingAmount = () => { return bills.reduce((total, bill) => total + (bill.totalAmount - bill.paidAmount), 0); }; return ( <div className="space-y-6 p-6"> {/* Header */} <div className="flex items-center justify-between"> <div> <h1 className="text-3xl font-bold text-foreground"> {t('billing.title')} </h1> <p className="text-lg text-muted-foreground mt-2"> {t('billing.subtitle')} </p> </div> <Button onClick={handleCreateBill} className="flex items-center"> <Plus className="w-4 h-4 mr-2" /> {t('billing.newBill')} </Button> </div> {/* Filters and Search */} <Card variant="glass"> <CardContent className="p-6"> <div className="flex flex-col md:flex-row gap-4"> <div className="flex-1"> <div className="relative"> <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" /> <Input placeholder="Search by patient name, invoice ID, or patient ID..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10" /> </div> </div> <div className="flex items-center gap-2"> <Filter className="w-4 h-4 text-gray-500" /> <select value={selectedStatus} onChange={(e) => setSelectedStatus(e.target.value)} className="border border-border rounded-md px-3 py-2 text-sm" > <option value="all">All Status</option> <option value="pending">Pending</option> <option value="partial">Partial</option> <option value="paid">Paid</option> <option value="overdue">Overdue</option> </select> </div> </div> </CardContent> </Card> {/* Statistics */} <div className="grid grid-cols-1 md:grid-cols-4 gap-6"> <Card variant="glass"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Total Revenue</p> <p className="text-2xl font-bold text-foreground">{formatCurrency(calculateTotalRevenue())}</p> </div> <DollarSign className="w-8 h-8 text-emerald-700 dark:text-emerald-400" /> </div> </CardContent> </Card> <Card variant="glass"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Pending Amount</p> <p className="text-2xl font-bold text-foreground">{formatCurrency(calculatePendingAmount())}</p> </div> <Clock className="w-8 h-8 text-orange-600" /> </div> </CardContent> </Card> <Card variant="glass"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Overdue Bills</p> <p className="text-2xl font-bold text-foreground"> {bills.filter(b => b.status === 'overdue').length} </p> </div> <AlertCircle className="w-8 h-8 text-rose-700 dark:text-rose-400" /> </div> </CardContent> </Card> <Card variant="glass"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Total Bills</p> <p className="text-2xl font-bold text-foreground">{bills.length}</p> </div> <CreditCard className="w-8 h-8 text-sky-700 dark:text-sky-400" /> </div> </CardContent> </Card> </div> {/* Bills List */} {loading ? ( <Card> <CardContent className="p-12 text-center"> <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-sky-700 dark:text-sky-400" /> <p className="text-muted-foreground">Loading bills...</p> </CardContent> </Card> ) : ( <div className="space-y-4"> {filteredBills.map((bill) => ( <Card key={bill.id}> <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center gap-4 mb-3"> {getStatusIcon(bill.status)} <h3 className="text-lg font-semibold text-foreground"> Invoice #{bill.id} </h3> <Badge className={getStatusColor(bill.status)}> {bill.status} </Badge> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"> <div> <p className="text-sm text-muted-foreground">Patient: {bill.patientName}</p> <p className="text-sm text-muted-foreground">ID: {bill.patientId}</p> <p className="text-sm text-muted-foreground">Service Date: {bill.serviceDate}</p> </div> <div> <p className="text-sm text-muted-foreground">Due Date: {bill.dueDate}</p> <p className="text-sm text-muted-foreground">Total: {formatCurrency(bill.totalAmount)}</p> <p className="text-sm text-muted-foreground">Paid: {formatCurrency(bill.paidAmount)}</p> </div> <div> <p className="text-sm text-muted-foreground"> Balance: {formatCurrency(bill.totalAmount - bill.paidAmount)} </p> {bill.paymentMethod && ( <p className="text-sm text-muted-foreground">Payment: {bill.paymentMethod}</p> )} {bill.insuranceClaim && ( <p className="text-sm text-muted-foreground">Insurance: {bill.insuranceClaim}</p> )} </div> </div> <div className="mb-4"> <p className="text-sm font-medium text-foreground mb-1">Services:</p> <div className="flex flex-wrap gap-2"> {(bill.services || []).map((service, index) => ( <Badge key={index} variant="outline" className="text-xs"> {service} </Badge> ))} </div> </div> {bill.notes && ( <div> <p className="text-sm font-medium text-foreground mb-1">Notes:</p> <p className="text-sm text-muted-foreground">{bill.notes}</p> </div> )} </div> <div className="flex items-center gap-2 ml-4"> <Button variant="outline" size="sm" onClick={() => handleEditBill(bill)} > <Eye className="w-4 h-4 mr-1" /> View </Button> <Button variant="outline" size="sm" onClick={() => handleEditBill(bill)} > <Edit className="w-4 h-4 mr-1" /> Edit </Button> <Button variant="destructive" size="sm" onClick={() => handleDeleteBill(bill.id)} disabled={actionLoading === bill.id} > {actionLoading === bill.id ? ( <Loader2 className="w-4 h-4 mr-1 animate-spin" /> ) : ( <Trash2 className="w-4 h-4 mr-1" /> )} Delete </Button> <Button variant="outline" size="sm" onClick={() => handlePrintBill(bill)} > <Download className="w-4 h-4 mr-1" /> Print </Button> </div> </div> </CardContent> </Card> ))} </div> )} {!loading && filteredBills.length === 0 && ( <Card> <CardContent className="p-12 text-center"> <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" /> <h3 className="text-lg font-medium text-foreground mb-2">No bills found</h3> <p className="text-muted-foreground"> {searchTerm ? 'Try adjusting your search criteria' : 'Start by creating a new invoice'} </p> </CardContent> </Card> )} {/* CRUD Modal */} <CRUDModal isOpen={showModal} onClose={() => setShowModal(false)} onSubmit={handleSubmitBill} title={modalMode === 'create' ? 'Create New Bill' : 'Edit Bill'} fields={billFormFields} initialData={selectedBill || {}} mode={modalMode} /> </div> ); }; export default BillingManagement; 