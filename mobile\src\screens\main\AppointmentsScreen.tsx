import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl, Alert } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { NativeStackNavigationProp } from '@react-navigation/native-stack'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { MainStackParamList } from '../../navigation/MainNavigator'; import { RootState, AppDispatch } from '../../store'; import { fetchMyAppointments, cancelAppointment, confirmAppointment } from '../../store/slices/appointmentSlice'; import { useTranslation } from '../../hooks/useTranslation'; type AppointmentsScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'MainTabs'>; const AppointmentsScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { appointments, isLoading } = useSelector((state: RootState) => state.appointment); const [refreshing, setRefreshing] = useState(false); const [filter, setFilter] = useState<'all' | 'upcoming' | 'past'>('upcoming'); useEffect(() => { loadAppointments(); }, [dispatch]); const loadAppointments = async () => { try { await dispatch(fetchMyAppointments({ page: 1, pageSize: 50 })); } catch (error) { console.error('Failed to load appointments:', error); } }; const onRefresh = async () => { setRefreshing(true); await loadAppointments(); setRefreshing(false); }; const handleCancelAppointment = (appointmentId: number) => { Alert.alert( 'Cancel Appointment', 'Are you sure you want to cancel this appointment?', [ { text: 'No', style: 'cancel' }, { text: 'Yes', style: 'destructive', onPress: () => dispatch(cancelAppointment(appointmentId)) } ] ); }; const handleConfirmAppointment = (appointmentId: number) => { dispatch(confirmAppointment(appointmentId)); }; const getFilteredAppointments = () => { const now = new Date(); return appointments.filter(appointment => { const appointmentDate = new Date(appointment.appointment_date); switch (filter) { case 'upcoming': return appointmentDate >= now && ['scheduled', 'confirmed'].includes(appointment.status); case 'past': return appointmentDate < now || ['completed', 'cancelled'].includes(appointment.status); default: return true; } }); }; const filteredAppointments = getFilteredAppointments(); const getStatusColor = (status: string) => { switch (status) { case 'confirmed': return 'status-success'; case 'scheduled': return 'status-info'; case 'completed': return 'bg-muted text-foreground'; case 'cancelled': return 'status-error'; case 'in_progress': return 'status-warning'; default: return 'bg-muted text-foreground'; } }; return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <View className="flex-row justify-between items-center"> <Text className={`text-2xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointments </Text> <TouchableOpacity onPress={() => navigation.navigate('BookAppointment')} className="bg-primary-600 px-4 py-2 rounded-lg" > <Text className="text-white font-medium">Book New</Text> </TouchableOpacity> </View> </View> {/* Filter Tabs */} <View className="px-6 mb-4"> <View className={`flex-row rounded-lg p-1 ${isDark ? 'bg-dark-card' : 'bg-muted'} ${isRTL ? 'flex-row-reverse' : ''}`}> {(['upcoming', 'past', 'all'] as const).map((filterOption) => ( <TouchableOpacity key={filterOption} onPress={() => setFilter(filterOption)} className={`flex-1 py-2 px-4 rounded-md ${ filter === filterOption ? 'bg-primary-600' : 'bg-transparent' }`} > <Text className={`text-center font-medium ${ filter === filterOption ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)} </Text> </TouchableOpacity> ))} </View> </View> {/* Appointments List */} <ScrollView className="flex-1 px-6" refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } showsVerticalScrollIndicator={false} > {isLoading && !refreshing ? ( <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm`}> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Loading appointments... </Text> </View> ) : filteredAppointments.length > 0 ? ( <View className="space-y-4 pb-6"> {filteredAppointments.map((appointment) => ( <TouchableOpacity key={appointment.id} onPress={() => navigation.navigate('AppointmentDetail', { appointmentId: appointment.id })} className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`} > <View className="flex-row justify-between items-start mb-3"> <View className="flex-1"> <Text className={`font-semibold text-lg ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Dr. {appointment.doctor_name} </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {appointment.appointment_type.replace('_', ' ').toUpperCase()} </Text> </View> <View className={`px-3 py-1 rounded-full ${getStatusColor(appointment.status)}`}> <Text className="text-xs font-medium"> {appointment.status.toUpperCase()} </Text> </View> </View> <View className="flex-row items-center mb-2"> <Ionicons name="calendar-outline" size={16} color={isDark ? '#8E8E93' : '#8E8E93'} /> <Text className={`ml-2 text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {new Date(appointment.appointment_date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })} </Text> </View> <View className="flex-row items-center mb-3"> <Ionicons name="time-outline" size={16} color={isDark ? '#8E8E93' : '#8E8E93'} /> <Text className={`ml-2 text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {appointment.appointment_time} </Text> </View> {appointment.reason && ( <Text className={`text-sm mb-3 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Reason: {appointment.reason} </Text> )} {/* Action Buttons */} {appointment.status === 'scheduled' && ( <View className="flex-row space-x-3"> <TouchableOpacity onPress={() => handleConfirmAppointment(appointment.id)} className="flex-1 bg-green-600 py-2 px-4 rounded-lg" > <Text className="text-white text-center font-medium">Confirm</Text> </TouchableOpacity> <TouchableOpacity onPress={() => handleCancelAppointment(appointment.id)} className="flex-1 bg-red-600 py-2 px-4 rounded-lg" > <Text className="text-white text-center font-medium">Cancel</Text> </TouchableOpacity> </View> )} {appointment.status === 'confirmed' && ( <TouchableOpacity onPress={() => handleCancelAppointment(appointment.id)} className="bg-red-600 py-2 px-4 rounded-lg" > <Text className="text-white text-center font-medium">Cancel Appointment</Text> </TouchableOpacity> )} </TouchableOpacity> ))} </View> ) : ( <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm`}> <View className="items-center"> <Ionicons name="calendar-outline" size={48} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-lg font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> No {filter === 'all' ? '' : filter} appointments </Text> <Text className={`text-center mb-4 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {filter === 'upcoming' ? "You don't have any upcoming appointments." : filter === 'past' ? "You don't have any past appointments." : "You don't have any appointments yet." } </Text> <TouchableOpacity onPress={() => navigation.navigate('BookAppointment')} className="bg-primary-600 py-3 px-6 rounded-lg" > <Text className="text-white font-medium">Book Your First Appointment</Text> </TouchableOpacity> </View> </View> )} </ScrollView> </SafeAreaView> ); }; export default AppointmentsScreen; 