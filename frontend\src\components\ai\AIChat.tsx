import React, { useState, useRef, useEffect } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Send, Bot, User, Loader2, <PERSON>, <PERSON>ethoscope } from 'lucide-react'; import type { RootState, AppDispatch } from '../../store'; import { sendMessage } from '../../store/slices/aiSlice'; import type { ChatRequest } from '../../services/aiService'; interface AIChatProps { patientId?: number; conversationType?: 'diagnosis' | 'treatment' | 'consultation' | 'emergency' | 'general'; className?: string; } const AIChat: React.FC<AIChatProps> = ({ patientId, conversationType = 'general', className = '' }) => { const dispatch = useDispatch<AppDispatch>(); const { currentConversation, isSendingMessage, chatError, lastChatResponse } = useSelector((state: RootState) => state.ai); const [message, setMessage] = useState(''); const [messages, setMessages] = useState<Array<{ role: 'user' | 'assistant'; content: string; timestamp: Date; processing_time?: number; }>>([]); const messagesEndRef = useRef<HTMLDivElement>(null); const inputRef = useRef<HTMLInputElement>(null); // Auto-scroll to bottom const scrollToBottom = () => { messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }; useEffect(() => { scrollToBottom(); }, [messages]); // Handle new AI response useEffect(() => { if (lastChatResponse && lastChatResponse.success) { setMessages(prev => [...prev, { role: 'assistant', content: lastChatResponse.response, timestamp: new Date(), processing_time: lastChatResponse.processing_time }]); } }, [lastChatResponse]); const handleSendMessage = async () => { if (!message.trim() || isSendingMessage) return; const userMessage = { role: 'user' as const, content: message.trim(), timestamp: new Date() }; // Add user message immediately setMessages(prev => [...prev, userMessage]); const chatRequest: ChatRequest = { message: message.trim(), conversation_type: conversationType, patient_id: patientId, conversation_id: currentConversation?.conversation_id }; setMessage(''); try { await dispatch(sendMessage(chatRequest)).unwrap(); } catch (error) { console.error('Failed to send message:', error); } }; const handleKeyPress = (e: React.KeyboardEvent) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); handleSendMessage(); } }; const getConversationIcon = () => { switch (conversationType) { case 'diagnosis': return <Stethoscope className="w-5 h-5" />; case 'treatment': return <Brain className="w-5 h-5" />; default: return <Bot className="w-5 h-5" />; } }; const getConversationTitle = () => { switch (conversationType) { case 'diagnosis': return 'AI Diagnosis Assistant'; case 'treatment': return 'AI Treatment Planner'; case 'consultation': return 'AI Medical Consultation'; case 'emergency': return 'AI Emergency Assessment'; default: return 'AI Medical Assistant'; } }; return ( <div className={`flex flex-col h-full macos-card ${className}`}> {/* Header */} <div className="flex items-center gap-3 p-4 border-b macos-border macos-accent-bg-subtle rounded-t-lg"> <div className="p-2 macos-accent-bg rounded-lg text-white"> {getConversationIcon()} </div> <div> <h3 className="font-semibold macos-text-primary"> {getConversationTitle()} </h3> <p className="text-sm macos-text-secondary"> Powered by Gemini AI + LangGraph </p> </div> </div> {/* Messages */} <div className="flex-1 overflow-y-auto p-4 space-y-4"> {messages.length === 0 && ( <div className="text-center py-8"> <Bot className="w-12 h-12 mx-auto macos-text-tertiary mb-4" /> <p className="macos-text-secondary"> Start a conversation with the AI medical assistant </p> <p className="text-sm macos-text-tertiary mt-2"> Ask about symptoms, treatments, or general medical questions </p> </div> )} {messages.map((msg, index) => ( <div key={index} className={`flex gap-3 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`} > {msg.role === 'assistant' && ( <div className="flex-shrink-0 w-8 h-8 macos-accent-bg rounded-full flex items-center justify-center"> <Bot className="w-4 h-4 text-white" /> </div> )} <div className={`max-w-[80%] p-3 rounded-lg macos-transition ${ msg.role === 'user' ? 'macos-accent-bg text-white ml-auto' : 'macos-surface macos-text-primary' }`} > <div className="whitespace-pre-wrap">{msg.content}</div> <div className="flex items-center justify-between mt-2 text-xs opacity-70"> <span>{msg.timestamp.toLocaleTimeString()}</span> {msg.processing_time && ( <span>⚡ {msg.processing_time.toFixed(2)}s</span> )} </div> </div> {msg.role === 'user' && ( <div className="flex-shrink-0 w-8 h-8 macos-surface rounded-full flex items-center justify-center"> <User className="w-4 h-4 macos-text-secondary" /> </div> )} </div> ))} {isSendingMessage && ( <div className="flex gap-3 justify-start"> <div className="flex-shrink-0 w-8 h-8 macos-accent-bg rounded-full flex items-center justify-center"> <Bot className="w-4 h-4 text-white" /> </div> <div className="macos-surface p-3 rounded-lg"> <div className="flex items-center gap-2"> <Loader2 className="w-4 h-4 animate-spin macos-accent-text" /> <span className="macos-text-secondary">AI is thinking...</span> </div> </div> </div> )} {chatError && ( <div className="macos-error-bg border macos-error-border rounded-lg p-3"> <p className="macos-error-text text-sm"> Error: {chatError} </p> </div> )} <div ref={messagesEndRef} /> </div> {/* Input */} <div className="p-4 border-t macos-border"> <div className="flex gap-2"> <input ref={inputRef} type="text" value={message} onChange={(e) => setMessage(e.target.value)} onKeyPress={handleKeyPress} placeholder="Ask the AI assistant..." className="macos-input flex-1" disabled={isSendingMessage} /> <button onClick={handleSendMessage} disabled={!message.trim() || isSendingMessage} className="macos-button macos-accent-bg text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2" > {isSendingMessage ? ( <Loader2 className="w-4 h-4 animate-spin" /> ) : ( <Send className="w-4 h-4" /> )} </button> </div> <div className="flex items-center justify-between mt-2 text-xs macos-text-tertiary"> <span>Press Enter to send, Shift+Enter for new line</span> <span className="flex items-center gap-1"> <div className="w-2 h-2 bg-green-500 rounded-full"></div> AI Online </span> </div> </div> </div> ); }; export default AIChat; 