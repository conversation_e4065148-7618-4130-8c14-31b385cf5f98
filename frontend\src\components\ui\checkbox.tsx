import React from 'react'; import { Check } from 'lucide-react'; import { cn } from '../../utils/styleConverter'; export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> { error?: boolean; } const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>( ({ className, error, ...props }, ref) => { return ( <div className="relative inline-flex items-center"> <input type="checkbox" ref={ref} className={cn( 'peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 sr-only', error && 'border-red-500 focus-visible:ring-red-500', className )} {...props} /> <div className={cn( 'h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 peer-checked:bg-primary peer-checked:text-primary-foreground flex items-center justify-center', error && 'border-red-500 focus-visible:ring-red-500', )} > <Check className="h-3 w-3 opacity-0 peer-checked:opacity-100 transition-opacity" /> </div> </div> ); } ); Checkbox.displayName = 'Checkbox'; export { Checkbox }; 