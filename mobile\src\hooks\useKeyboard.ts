import { useEffect, useState } from 'react'; import { Keyboard, KeyboardEvent } from 'react-native'; /** * Hook to track keyboard visibility and height */ export function useKeyboard() { const [keyboardHeight, setKeyboardHeight] = useState(0); const [isKeyboardVisible, setIsKeyboardVisible] = useState(false); useEffect(() => { const keyboardDidShowListener = Keyboard.addListener( 'keyboardDidShow', (e: KeyboardEvent) => { setKeyboardHeight(e.endCoordinates.height); setIsKeyboardVisible(true); } ); const keyboardDidHideListener = Keyboard.addListener( 'keyboardDidHide', () => { setKeyboardHeight(0); setIsKeyboardVisible(false); } ); return () => { keyboardDidShowListener?.remove(); keyboardDidHideListener?.remove(); }; }, []); return { keyboardHeight, isKeyboardVisible, }; } 