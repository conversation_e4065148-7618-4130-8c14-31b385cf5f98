import React from 'react'; import { useTranslation } from 'react-i18next'; import { Brain, Heart, Shield, Zap, Users, Calendar, FileText, BarChart3, Stethoscope, Pill, TestTube, CreditCard } from 'lucide-react'; import { getFeatureClass } from '../../utils/styleUtils'; const FeaturesSection: React.FC = () => { const { t } = useTranslation(); const features = [ { icon: Brain, title: t('landing.features.aiDiagnostics.title'), description: t('landing.features.aiDiagnostics.description'), color: 'blue' }, { icon: Heart, title: t('landing.features.patientCare.title'), description: t('landing.features.patientCare.description'), color: 'red' }, { icon: Calendar, title: t('landing.features.smartScheduling.title'), description: t('landing.features.smartScheduling.description'), color: 'green' }, { icon: FileText, title: t('landing.features.digitalRecords.title'), description: t('landing.features.digitalRecords.description'), color: 'purple' }, { icon: TestTube, title: t('landing.features.labManagement.title'), description: t('landing.features.labManagement.description'), color: 'orange' }, { icon: Pill, title: t('landing.features.pharmacyIntegration.title'), description: t('landing.features.pharmacyIntegration.description'), color: 'pink' }, { icon: BarChart3, title: t('landing.features.analytics.title'), description: t('landing.features.analytics.description'), color: 'indigo' }, { icon: Shield, title: t('landing.features.security.title'), description: t('landing.features.security.description'), color: 'gray' } ]; return ( <section className="section-spacing bg-muted/50"> <div className="container-padding"> {/* Section Header */} <div className="text-center mb-16"> <div className="flex items-center justify-center mb-4"> <div className="flex items-center space-x-2 badge-info px-4 py-2 rounded-full"> <Stethoscope className="w-5 h-5" /> <span className="font-medium">{t('landing.features.badge')}</span> </div> </div> <h2 className="text-responsive-3xl font-bold text-heading mb-6"> {t('landing.features.title')} </h2> <p className="text-responsive-lg text-subheading max-w-3xl mx-auto"> {t('landing.features.subtitle')} </p> </div> {/* Features Grid */} <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"> {features.map((feature, index) => { const Icon = feature.icon; return ( <div key={index} className="group card-base rounded-2xl p-8 shadow-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-2 interactive" > <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300 ${getFeatureClass(feature.color)}`}> <Icon className="w-8 h-8 transition-all duration-300" /> </div> <h3 className="text-responsive-lg font-bold text-heading mb-4 group-hover:text-primary transition-colors"> {feature.title} </h3> <p className="text-body leading-relaxed"> {feature.description} </p> </div> ); })} </div> {/* Bottom CTA */} <div className="text-center mt-16"> <div className="bg-gradient-to-r from-primary to-purple-600 rounded-3xl p-12 text-primary-foreground"> <h3 className="text-responsive-2xl font-bold mb-4"> {t('landing.features.ctaTitle')} </h3> <p className="text-responsive-lg opacity-90 mb-8 max-w-2xl mx-auto"> {t('landing.features.ctaDescription')} </p> <div className="flex flex-col sm:flex-row gap-4 justify-center"> <button className="btn-secondary px-8 py-4 rounded-xl font-semibold"> {t('landing.features.startTrial')} </button> <button className="btn-outline border-2 border-primary-foreground text-primary-foreground px-8 py-4 rounded-xl font-semibold hover:bg-primary-foreground hover:text-primary transition-colors"> {t('landing.features.contactSales')} </button> </div> </div> </div> </div> </section> ); }; export default FeaturesSection; 