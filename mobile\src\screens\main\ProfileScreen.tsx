import React, { useEffect } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, Alert } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { RootState, AppDispatch } from '../../store'; import { fetchMyProfile } from '../../store/slices/patientSlice'; import { logout } from '../../store/slices/authSlice'; import { useTranslation } from '../../hooks/useTranslation'; const ProfileScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { user } = useSelector((state: RootState) => state.auth); const { currentPatient, isLoading } = useSelector((state: RootState) => state.patient); useEffect(() => { dispatch(fetchMyProfile()); }, [dispatch]); const handleLogout = () => { Alert.alert( 'Logout', 'Are you sure you want to logout?', [ { text: 'Cancel', style: 'cancel' }, { text: 'Logout', style: 'destructive', onPress: () => dispatch(logout()) } ] ); }; return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <ScrollView className="flex-1" showsVerticalScrollIndicator={false}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Profile </Text> </View> {/* Profile Info */} <View className="px-6 mb-6"> <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <View className="items-center mb-6"> <View className={`w-20 h-20 rounded-full mb-4 justify-center items-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name="person" size={40} color="#007AFF" /> </View> <Text className={`text-xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {user?.full_name || `${user?.first_name} ${user?.last_name}`} </Text> {currentPatient && ( <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Patient ID: {currentPatient.patient_id} </Text> )} </View> <View className="space-y-4"> <ProfileItem label="Email" value={user?.email || 'Not provided'} isDark={isDark} /> <ProfileItem label="Phone" value={user?.phone_number || 'Not provided'} isDark={isDark} /> <ProfileItem label="Role" value={user?.role?.toUpperCase() || 'PATIENT'} isDark={isDark} /> {currentPatient?.blood_group && ( <ProfileItem label="Blood Group" value={currentPatient.blood_group} isDark={isDark} /> )} </View> </View> </View> {/* Medical Information */} {currentPatient && ( <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Medical Information </Text> <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <View className="space-y-4"> {currentPatient.allergies && ( <ProfileItem label="Allergies" value={currentPatient.allergies} isDark={isDark} /> )} {currentPatient.chronic_conditions && ( <ProfileItem label="Chronic Conditions" value={currentPatient.chronic_conditions} isDark={isDark} /> )} {currentPatient.current_medications && ( <ProfileItem label="Current Medications" value={currentPatient.current_medications} isDark={isDark} /> )} {currentPatient.insurance_provider && ( <ProfileItem label="Insurance Provider" value={currentPatient.insurance_provider} isDark={isDark} /> )} </View> </View> </View> )} {/* Emergency Contact */} {currentPatient?.emergency_contact_name && ( <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Emergency Contact </Text> <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <View className="space-y-4"> <ProfileItem label="Name" value={currentPatient.emergency_contact_name} isDark={isDark} /> {currentPatient.emergency_contact_phone && ( <ProfileItem label="Phone" value={currentPatient.emergency_contact_phone} isDark={isDark} /> )} {currentPatient.emergency_contact_relationship && ( <ProfileItem label="Relationship" value={currentPatient.emergency_contact_relationship} isDark={isDark} /> )} </View> </View> </View> )} {/* Actions */} <View className="px-6 mb-8"> <TouchableOpacity onPress={() => navigation.navigate('Notifications' as never)} className={`py-4 px-6 rounded-xl mb-4 border ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' }`} > <View className="flex-row items-center justify-center"> <Ionicons name="notifications-outline" size={20} color={isDark ? '#FFFFFF' : '#000000'} /> <Text className={`ml-2 text-center font-semibold text-lg ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Notifications </Text> </View> </TouchableOpacity> <TouchableOpacity onPress={() => navigation.navigate('Settings' as never)} className={`py-4 px-6 rounded-xl mb-4 border ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' }`} > <View className="flex-row items-center justify-center"> <Ionicons name="settings-outline" size={20} color={isDark ? '#FFFFFF' : '#000000'} /> <Text className={`ml-2 text-center font-semibold text-lg ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Settings </Text> </View> </TouchableOpacity> <TouchableOpacity onPress={() => navigation.navigate('EditProfile' as never)} className="bg-primary-600 py-4 px-6 rounded-xl mb-4" > <Text className="text-white text-center font-semibold text-lg"> Edit Profile </Text> </TouchableOpacity> <TouchableOpacity onPress={handleLogout} className={`py-4 px-6 rounded-xl border-2 ${ isDark ? 'border-red-600 bg-red-600/10' : 'border-red-600 bg-red-50' }`} > <Text className="text-rose-700 dark:text-rose-400 text-center font-semibold text-lg"> Logout </Text> </TouchableOpacity> </View> </ScrollView> </SafeAreaView> ); }; interface ProfileItemProps { label: string; value: string; isDark: boolean; } const ProfileItem: React.FC<ProfileItemProps> = ({ label, value, isDark }) => ( <View className="flex-row justify-between items-start"> <Text className={`text-sm font-medium ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {label} </Text> <Text className={`text-sm text-right flex-1 ml-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {value} </Text> </View> ); export default ProfileScreen; 