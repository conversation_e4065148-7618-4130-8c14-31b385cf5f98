import React from 'react'; import { View, TouchableOpacity } from 'react-native'; import { useTheme } from '../../contexts/ThemeContext'; interface CardProps { children: React.ReactNode; onPress?: () => void; className?: string; variant?: 'default' | 'glass' | 'elevated'; padding?: 'none' | 'sm' | 'md' | 'lg'; } const Card: React.FC<CardProps> = ({ children, onPress, className = '', variant = 'default', padding = 'md', }) => { const { isDark, glassmorphismLevel } = useTheme(); const getVariantStyles = () => { const baseStyles = 'rounded-xl border'; switch (variant) { case 'glass': if (glassmorphismLevel === 'none') { return `${baseStyles} ${isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border'}`; } return `${baseStyles} ${ isDark ? 'bg-dark-card/80 border-dark-border/20 backdrop-blur-md' : 'bg-background/80 border-white/20 backdrop-blur-md' }`; case 'elevated': return `${baseStyles} ${ isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border' } shadow-lg`; default: return `${baseStyles} ${ isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border' } shadow-sm`; } }; const getPaddingStyles = () => { switch (padding) { case 'none': return ''; case 'sm': return 'p-3'; case 'md': return 'p-4'; case 'lg': return 'p-6'; default: return 'p-4'; } }; const cardStyles = `${getVariantStyles()} ${getPaddingStyles()} ${className}`; if (onPress) { return ( <TouchableOpacity onPress={onPress} className={cardStyles} activeOpacity={0.7} > {children} </TouchableOpacity> ); } return ( <View className={cardStyles}> {children} </View> ); }; export default Card; 