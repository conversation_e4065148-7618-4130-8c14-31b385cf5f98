import React from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, Switch, Alert } from 'react-native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { RootState, AppDispatch } from '../../store'; import { toggleTheme, setGlassmorphismLevel, setReducedMotion } from '../../store/slices/themeSlice'; const SettingsScreen: React.FC = () => { const dispatch = useDispatch(); const { isDark } = useTheme(); const { t, currentLanguage, changeLanguage, isRTL } = useTranslation(); const theme = useSelector((state: RootState) => state.theme); const handleThemeToggle = () => { dispatch(toggleTheme()); }; const handleGlassmorphismChange = () => { const levels = ['none', 'subtle', 'medium', 'strong'] as const; const currentIndex = levels.indexOf(theme.glassmorphismLevel); const nextIndex = (currentIndex + 1) % levels.length; dispatch(setGlassmorphismLevel(levels[nextIndex])); }; const handleReducedMotionToggle = () => { dispatch(setReducedMotion(!theme.reducedMotion)); }; const handleLanguageChange = async () => { const newLanguage = currentLanguage === 'ar' ? 'en' : 'ar'; const success = await changeLanguage(newLanguage); if (success) { Alert.alert( t('settings.languageChanged'), t('settings.restartRequired'), [{ text: t('common.ok') }] ); } }; return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <ScrollView className="flex-1" showsVerticalScrollIndicator={false}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('settings.title')} </Text> </View> {/* Appearance Section */} <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('settings.appearance')} </Text> <View className={`rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <SettingItem icon="language-outline" title={t('settings.language')} subtitle={`${t('common.current')}: ${currentLanguage === 'ar' ? t('settings.arabic') : t('settings.english')}`} onPress={handleLanguageChange} isDark={isDark} isRTL={isRTL} showArrow /> <SettingItem icon="moon-outline" title={t('settings.theme')} subtitle={`${t('common.current')}: ${theme.mode === 'system' ? t('settings.systemMode') : theme.mode === 'dark' ? t('settings.darkMode') : t('settings.lightMode')}`} onPress={handleThemeToggle} isDark={isDark} isRTL={isRTL} showArrow showBorder /> <SettingItem icon="color-palette-outline" title={t('settings.glassmorphism')} subtitle={`${t('common.level')}: ${theme.glassmorphismLevel.charAt(0).toUpperCase() + theme.glassmorphismLevel.slice(1)}`} onPress={handleGlassmorphismChange} isDark={isDark} isRTL={isRTL} showArrow showBorder /> <SettingItem icon="accessibility-outline" title={t('settings.reducedMotion')} subtitle={t('settings.minimizeAnimations')} onPress={handleReducedMotionToggle} isDark={isDark} isRTL={isRTL} rightComponent={ <Switch value={theme.reducedMotion} onValueChange={handleReducedMotionToggle} trackColor={{ false: '#767577', true: '#007AFF' }} thumbColor={theme.reducedMotion ? '#FFFFFF' : '#f4f3f4'} /> } /> </View> </View> {/* Notifications Section */} <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Notifications </Text> <View className={`rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <SettingItem icon="notifications-outline" title="Push Notifications" subtitle="Appointment reminders and updates" onPress={() => Alert.alert('Coming Soon', 'Notification settings will be available in the next update.')} isDark={isDark} rightComponent={ <Switch value={true} onValueChange={() => {}} trackColor={{ false: '#767577', true: '#007AFF' }} thumbColor={'#FFFFFF'} /> } /> <SettingItem icon="mail-outline" title="Email Notifications" subtitle="Appointment confirmations and updates" onPress={() => Alert.alert('Coming Soon', 'Email notification settings will be available in the next update.')} isDark={isDark} rightComponent={ <Switch value={true} onValueChange={() => {}} trackColor={{ false: '#767577', true: '#007AFF' }} thumbColor={'#FFFFFF'} /> } showBorder /> <SettingItem icon="time-outline" title="Reminder Time" subtitle="1 hour before appointment" onPress={() => Alert.alert('Coming Soon', 'Reminder settings will be available in the next update.')} isDark={isDark} showArrow /> </View> </View> {/* Privacy & Security Section */} <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Privacy & Security </Text> <View className={`rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <SettingItem icon="finger-print-outline" title="Biometric Authentication" subtitle="Use fingerprint or face ID" onPress={() => Alert.alert('Coming Soon', 'Biometric authentication will be available in the next update.')} isDark={isDark} rightComponent={ <Switch value={false} onValueChange={() => {}} trackColor={{ false: '#767577', true: '#007AFF' }} thumbColor={'#f4f3f4'} /> } /> <SettingItem icon="shield-outline" title="Privacy Policy" subtitle="Read our privacy policy" onPress={() => Alert.alert('Privacy Policy', 'Privacy policy content will be available in the next update.')} isDark={isDark} showArrow showBorder /> <SettingItem icon="document-text-outline" title="Terms of Service" subtitle="Read our terms of service" onPress={() => Alert.alert('Terms of Service', 'Terms of service content will be available in the next update.')} isDark={isDark} showArrow /> </View> </View> {/* Support Section */} <View className="px-6 mb-8"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Support </Text> <View className={`rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <SettingItem icon="help-circle-outline" title="Help & FAQ" subtitle="Get help and find answers" onPress={() => Alert.alert('Help & FAQ', 'Help section will be available in the next update.')} isDark={isDark} showArrow /> <SettingItem icon="mail-outline" title="Contact Support" subtitle="Get in touch with our team" onPress={() => Alert.alert('Contact Support', 'Support contact will be available in the next update.')} isDark={isDark} showArrow showBorder /> <SettingItem icon="information-circle-outline" title="About" subtitle="HMS Mobile v1.0.0" onPress={() => Alert.alert('About HMS Mobile', 'HMS Mobile v1.0.0\n\nA comprehensive hospital management system for patients.')} isDark={isDark} showArrow /> </View> </View> </ScrollView> </SafeAreaView> ); }; interface SettingItemProps { icon: keyof typeof Ionicons.glyphMap; title: string; subtitle: string; onPress: () => void; isDark: boolean; isRTL?: boolean; showArrow?: boolean; showBorder?: boolean; rightComponent?: React.ReactNode; } const SettingItem: React.FC<SettingItemProps> = ({ icon, title, subtitle, onPress, isDark, isRTL = false, showArrow = false, showBorder = false, rightComponent }) => ( <TouchableOpacity onPress={onPress} className={`flex-row items-center p-4 ${isRTL ? 'flex-row-reverse' : ''} ${ showBorder ? `border-b ${isDark ? 'border-dark-border' : 'border-border'}` : '' }`} > <View className={`w-10 h-10 rounded-lg ${isRTL ? 'ml-4' : 'mr-4'} justify-center items-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name={icon} size={20} color="#007AFF" /> </View> <View className="flex-1"> <Text className={`font-medium text-base ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {title} </Text> <Text className={`text-sm mt-1 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {subtitle} </Text> </View> {rightComponent || (showArrow && ( <Ionicons name={isRTL ? "chevron-back" : "chevron-forward"} size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> ))} </TouchableOpacity> ); export default SettingsScreen; 