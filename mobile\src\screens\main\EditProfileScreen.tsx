import React, { useState, useEffect } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, Alert, TextInput, KeyboardAvoidingView, Platform } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { RootState } from '../../store'; import { updateMyProfile } from '../../store/slices/patientSlice'; import LoadingSpinner from '../../components/common/LoadingSpinner'; import Card from '../../components/ui/Card'; import { useTranslation } from '../../hooks/useTranslation'; const EditProfileScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { currentPatient, isLoading } = useSelector((state: RootState) => state.patient); const { user } = useSelector((state: RootState) => state.auth); const [formData, setFormData] = useState({ first_name: '', last_name: '', email: '', phone_number: '', date_of_birth: '', gender: '', address: '', emergency_contact_name: '', emergency_contact_phone: '', blood_group: '', allergies: '', medical_conditions: '', insurance_provider: '', insurance_policy_number: '' }); const [errors, setErrors] = useState<Record<string, string>>({}); const [saving, setSaving] = useState(false); useEffect(() => { if (currentPatient && user) { setFormData({ first_name: user.first_name || '', last_name: user.last_name || '', email: user.email || '', phone_number: currentPatient.phone_number || '', date_of_birth: currentPatient.date_of_birth || '', gender: currentPatient.gender || '', address: currentPatient.address || '', emergency_contact_name: currentPatient.emergency_contact_name || '', emergency_contact_phone: currentPatient.emergency_contact_phone || '', blood_group: currentPatient.blood_group || '', allergies: currentPatient.allergies || '', medical_conditions: currentPatient.medical_conditions || '', insurance_provider: currentPatient.insurance_provider || '', insurance_policy_number: currentPatient.insurance_policy_number || '' }); } }, [currentPatient, user]); const validateForm = () => { const newErrors: Record<string, string> = {}; if (!formData.first_name.trim()) { newErrors.first_name = 'First name is required'; } if (!formData.last_name.trim()) { newErrors.last_name = 'Last name is required'; } if (!formData.email.trim()) { newErrors.email = 'Email is required'; } else if (!/\S+@\S+\.\S+/.test(formData.email)) { newErrors.email = 'Email is invalid'; } if (!formData.phone_number.trim()) { newErrors.phone_number = 'Phone number is required'; } if (!formData.date_of_birth.trim()) { newErrors.date_of_birth = 'Date of birth is required'; } setErrors(newErrors); return Object.keys(newErrors).length === 0; }; const handleSave = async () => { if (!validateForm()) { Alert.alert('Validation Error', 'Please fix the errors before saving.'); return; } setSaving(true); try { await dispatch(updateMyProfile(formData) as any); Alert.alert('Success', 'Profile updated successfully!', [ { text: 'OK', onPress: () => navigation.goBack() } ]); } catch (error: any) { Alert.alert('Error', error.message || 'Failed to update profile'); } finally { setSaving(false); } }; const renderInput = ( label: string, key: keyof typeof formData, placeholder: string, multiline = false, keyboardType: any = 'default' ) => ( <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {label} </Text> <TextInput value={formData[key]} onChangeText={(text) => setFormData(prev => ({ ...prev, [key]: text }))} placeholder={placeholder} placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'} multiline={multiline} numberOfLines={multiline ? 3 : 1} keyboardType={keyboardType} className={`border rounded-lg px-3 py-3 ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' } ${errors[key] ? 'border-red-500' : ''}`} /> {errors[key] && ( <Text className="text-red-500 text-xs mt-1">{errors[key]}</Text> )} </View> ); if (isLoading) { return <LoadingSpinner />; } return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1" > {/* Header */} <View className={`px-6 py-4 border-b ${ isDark ? 'border-dark-border' : 'border-border' }`}> <View className="flex-row items-center justify-between"> <TouchableOpacity onPress={() => navigation.goBack()}> <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} /> </TouchableOpacity> <Text className={`text-lg font-semibold ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Edit Profile </Text> <TouchableOpacity onPress={handleSave} disabled={saving} className="bg-primary-600 px-4 py-2 rounded-lg" > {saving ? ( <LoadingSpinner size="small" /> ) : ( <Text className="text-white font-medium">Save</Text> )} </TouchableOpacity> </View> </View> <ScrollView className="flex-1 px-6 py-4"> {/* Personal Information */} <Card className="mb-6"> <Text className={`text-lg font-semibold mb-4 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Personal Information </Text> {renderInput('First Name', 'first_name', 'Enter your first name')} {renderInput('Last Name', 'last_name', 'Enter your last name')} {renderInput('Email', 'email', 'Enter your email', false, 'email-address')} {renderInput('Phone Number', 'phone_number', 'Enter your phone number', false, 'phone-pad')} {renderInput('Date of Birth', 'date_of_birth', 'YYYY-MM-DD')} {renderInput('Gender', 'gender', 'Male/Female/Other')} {renderInput('Address', 'address', 'Enter your address', true)} </Card> {/* Emergency Contact */} <Card className="mb-6"> <Text className={`text-lg font-semibold mb-4 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Emergency Contact </Text> {renderInput('Contact Name', 'emergency_contact_name', 'Emergency contact name')} {renderInput('Contact Phone', 'emergency_contact_phone', 'Emergency contact phone', false, 'phone-pad')} </Card> {/* Medical Information */} <Card className="mb-6"> <Text className={`text-lg font-semibold mb-4 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Medical Information </Text> {renderInput('Blood Group', 'blood_group', 'A+, B+, O+, etc.')} {renderInput('Allergies', 'allergies', 'List any allergies', true)} {renderInput('Medical Conditions', 'medical_conditions', 'List any medical conditions', true)} </Card> {/* Insurance Information */} <Card className="mb-6"> <Text className={`text-lg font-semibold mb-4 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Insurance Information </Text> {renderInput('Insurance Provider', 'insurance_provider', 'Insurance company name')} {renderInput('Policy Number', 'insurance_policy_number', 'Insurance policy number')} </Card> </ScrollView> </KeyboardAvoidingView> </SafeAreaView> ); }; export default EditProfileScreen; 