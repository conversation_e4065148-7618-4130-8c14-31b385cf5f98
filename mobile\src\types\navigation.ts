import type { NavigatorScreenParams } from '@react-navigation/native'; import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs'; // Define screen props without stack navigator for now export type ScreenProps<T = any> = { navigation: any; route: any; }; // Root Stack Navigator export type RootStackParamList = { Welcome: undefined; Login: undefined; Register: undefined; MainTabs: NavigatorScreenParams<MainTabParamList>; AppointmentDetail: { appointmentId: number }; MedicalRecordDetail: { recordId: number }; AddMedicalRecord: undefined; EditProfile: undefined; DoctorDetail: { doctorId: number }; BookAppointment: undefined; Settings: undefined; }; // Main Tab Navigator export type MainTabParamList = { Dashboard: undefined; Appointments: undefined; MedicalRecords: undefined; DoctorsList: undefined; Notifications: undefined; Profile: undefined; Settings: undefined; }; // Screen Props Types export type RootStackScreenProps<T extends keyof RootStackParamList> = ScreenProps; export type MainTabScreenProps<T extends keyof MainTabParamList> = BottomTabScreenProps< MainTabParamList, T >; // Specific Screen Props export type WelcomeScreenProps = RootStackScreenProps<'Welcome'>; export type LoginScreenProps = RootStackScreenProps<'Login'>; export type RegisterScreenProps = RootStackScreenProps<'Register'>; export type DashboardScreenProps = MainTabScreenProps<'Dashboard'>; export type AppointmentsScreenProps = MainTabScreenProps<'Appointments'>; export type MedicalRecordsScreenProps = MainTabScreenProps<'MedicalRecords'>; export type DoctorsListScreenProps = MainTabScreenProps<'DoctorsList'>; export type NotificationsScreenProps = MainTabScreenProps<'Notifications'>; export type ProfileScreenProps = MainTabScreenProps<'Profile'>; export type SettingsScreenProps = MainTabScreenProps<'Settings'>; export type AppointmentDetailScreenProps = RootStackScreenProps<'AppointmentDetail'>; export type MedicalRecordDetailScreenProps = RootStackScreenProps<'MedicalRecordDetail'>; export type AddMedicalRecordScreenProps = RootStackScreenProps<'AddMedicalRecord'>; export type EditProfileScreenProps = RootStackScreenProps<'EditProfile'>; export type DoctorDetailScreenProps = RootStackScreenProps<'DoctorDetail'>; export type BookAppointmentScreenProps = RootStackScreenProps<'BookAppointment'>; declare global { namespace ReactNavigation { interface RootParamList extends RootStackParamList {} } } 