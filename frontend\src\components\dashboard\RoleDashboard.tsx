import React from 'react'; import { useSelector } from 'react-redux'; import type { RootState } from '../../store'; import AdminDashboard from './AdminDashboard'; import DoctorDashboard from './DoctorDashboard'; import PatientDashboard from './PatientDashboard'; import NurseDashboard from './NurseDashboard'; import ReceptionistDashboard from './ReceptionistDashboard'; import { AlertCircle } from 'lucide-react'; const RoleDashboard: React.FC = () => { const { user } = useSelector((state: RootState) => state.auth); // If no user is logged in, show a message if (!user) { return ( <div className="flex items-center justify-center min-h-[400px]"> <div className="w-full max-w-md bg-background rounded-lg shadow-lg p-6"> <div className="flex items-center text-orange-600 mb-4"> <AlertCircle className="w-5 h-5 mr-2" /> <h3 className="text-lg font-semibold">Authentication Required</h3> </div> <p className="text-muted-foreground"> Please log in to access your dashboard. </p> </div> </div> ); } // Route to appropriate dashboard based on user role switch (user.role) { case 'admin': return <AdminDashboard />; case 'doctor': return <DoctorDashboard />; case 'patient': return <PatientDashboard />; case 'nurse': return <NurseDashboard />; case 'receptionist': return <ReceptionistDashboard />; default: return ( <div className="flex items-center justify-center min-h-[400px]"> <div className="w-full max-w-md bg-background rounded-lg shadow-lg p-6"> <div className="flex items-center text-rose-700 dark:text-rose-400 mb-4"> <AlertCircle className="w-5 h-5 mr-2" /> <h3 className="text-lg font-semibold">Unknown Role</h3> </div> <p className="text-muted-foreground"> Your user role "{user.role}" is not recognized. Please contact your administrator. </p> </div> </div> ); } }; export default RoleDashboard; 