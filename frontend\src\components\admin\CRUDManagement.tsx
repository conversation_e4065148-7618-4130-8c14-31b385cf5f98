import React, { useState, useEffect } from 'react'; import { CRUDDataTable } from '../crud/CRUDDataTable'; import { Card } from '../ui/card'; import { Button } from '../ui/Button'; interface CRUDManagementProps { userRole: string; } export const CRUDManagement: React.FC<CRUDManagementProps> = ({ userRole }) => { const [activeModule, setActiveModule] = useState('users'); const [data, setData] = useState<any[]>([]); const [loading, setLoading] = useState(false); const modules = [ { id: 'users', name: 'Users', endpoint: '/api/users/users/' }, { id: 'patients', name: 'Patients', endpoint: '/api/patients/patients/' }, { id: 'appointments', name: 'Appointments', endpoint: '/api/appointments/appointments/' }, { id: 'departments', name: 'Departments', endpoint: '/api/medical/departments/' }, { id: 'wards', name: 'Wards', endpoint: '/api/medical/wards/' }, { id: 'beds', name: 'Beds', endpoint: '/api/medical/beds/' }, { id: 'inventory', name: 'Inventory', endpoint: '/api/inventory/items/' }, { id: 'categories', name: 'Categories', endpoint: '/api/inventory/categories/' }, { id: 'suppliers', name: 'Suppliers', endpoint: '/api/inventory/suppliers/' }, { id: 'invoices', name: 'Invoices', endpoint: '/api/billing/invoices/' }, { id: 'staff', name: 'Staff', endpoint: '/api/staff/staff-profiles/' }, { id: 'emergency', name: 'Emergency Contacts', endpoint: '/api/emergency/contacts/' }, { id: 'notifications', name: 'Notifications', endpoint: '/api/communications/notifications/' }, { id: 'messages', name: 'Messages', endpoint: '/api/communications/messages/' } ]; const getModuleConfig = (moduleId: string) => { switch (moduleId) { case 'users': return { columns: [ { key: 'id', label: 'ID' }, { key: 'username', label: 'Username' }, { key: 'email', label: 'Email' }, { key: 'full_name', label: 'Full Name' }, { key: 'role', label: 'Role' }, { key: 'is_active', label: 'Active', render: (value: boolean) => value ? 'Yes' : 'No' } ], createFields: [ { key: 'username', label: 'Username', type: 'text', required: true }, { key: 'email', label: 'Email', type: 'email', required: true }, { key: 'first_name', label: 'First Name', type: 'text', required: true }, { key: 'last_name', label: 'Last Name', type: 'text', required: true }, { key: 'role', label: 'Role', type: 'select', required: true, options: [ { value: 'admin', label: 'Admin' }, { value: 'doctor', label: 'Doctor' }, { value: 'nurse', label: 'Nurse' }, { value: 'patient', label: 'Patient' }, { value: 'receptionist', label: 'Receptionist' } ]}, { key: 'password', label: 'Password', type: 'password', required: true } ] }; case 'departments': return { columns: [ { key: 'id', label: 'ID' }, { key: 'name', label: 'Name' }, { key: 'description', label: 'Description' }, { key: 'location', label: 'Location' }, { key: 'phone', label: 'Phone' }, { key: 'is_active', label: 'Active', render: (value: boolean) => value ? 'Yes' : 'No' } ], createFields: [ { key: 'name', label: 'Name', type: 'text', required: true }, { key: 'description', label: 'Description', type: 'textarea' }, { key: 'location', label: 'Location', type: 'text' }, { key: 'phone', label: 'Phone', type: 'text' }, { key: 'email', label: 'Email', type: 'email' } ] }; case 'categories': return { columns: [ { key: 'id', label: 'ID' }, { key: 'name', label: 'Name' }, { key: 'description', label: 'Description' }, { key: 'created_at', label: 'Created', render: (value: string) => new Date(value).toLocaleDateString() } ], createFields: [ { key: 'name', label: 'Name', type: 'text', required: true }, { key: 'description', label: 'Description', type: 'textarea' } ] }; case 'suppliers': return { columns: [ { key: 'id', label: 'ID' }, { key: 'name', label: 'Name' }, { key: 'contact_person', label: 'Contact Person' }, { key: 'phone', label: 'Phone' }, { key: 'email', label: 'Email' }, { key: 'is_active', label: 'Active', render: (value: boolean) => value ? 'Yes' : 'No' } ], createFields: [ { key: 'name', label: 'Name', type: 'text', required: true }, { key: 'contact_person', label: 'Contact Person', type: 'text' }, { key: 'phone', label: 'Phone', type: 'text' }, { key: 'email', label: 'Email', type: 'email' }, { key: 'address', label: 'Address', type: 'textarea' } ] }; case 'emergency': return { columns: [ { key: 'id', label: 'ID' }, { key: 'name', label: 'Name' }, { key: 'organization', label: 'Organization' }, { key: 'phone_primary', label: 'Phone' }, { key: 'contact_type', label: 'Type' }, { key: 'is_active', label: 'Active', render: (value: boolean) => value ? 'Yes' : 'No' } ], createFields: [ { key: 'name', label: 'Name', type: 'text', required: true }, { key: 'organization', label: 'Organization', type: 'text', required: true }, { key: 'phone_primary', label: 'Primary Phone', type: 'text', required: true }, { key: 'phone_secondary', label: 'Secondary Phone', type: 'text' }, { key: 'email', label: 'Email', type: 'email' }, { key: 'contact_type', label: 'Contact Type', type: 'select', required: true, options: [ { value: 'ambulance', label: 'Ambulance Service' }, { value: 'fire', label: 'Fire Department' }, { value: 'police', label: 'Police' }, { value: 'poison_control', label: 'Poison Control' }, { value: 'blood_bank', label: 'Blood Bank' }, { value: 'other_hospital', label: 'Other Hospital' } ]} ] }; default: return { columns: [ { key: 'id', label: 'ID' }, { key: 'name', label: 'Name' }, { key: 'created_at', label: 'Created', render: (value: string) => new Date(value).toLocaleDateString() } ], createFields: [] }; } }; const fetchData = async (endpoint: string) => { setLoading(true); try { const token = localStorage.getItem('token'); const response = await fetch(`http://127.0.0.1:8000${endpoint}`, { headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' } }); if (response.ok) { const result = await response.json(); setData(result.results || result); } else { console.error('Failed to fetch data:', response.statusText); setData([]); } } catch (error) { console.error('Error fetching data:', error); setData([]); } finally { setLoading(false); } }; const handleCreate = async (endpoint: string, item: any) => { const token = localStorage.getItem('token'); const response = await fetch(`http://127.0.0.1:8000${endpoint}`, { method: 'POST', headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }, body: JSON.stringify(item) }); if (!response.ok) { const error = await response.json(); throw new Error(JSON.stringify(error)); } }; const handleUpdate = async (endpoint: string, id: string | number, item: any) => { const token = localStorage.getItem('token'); const response = await fetch(`http://127.0.0.1:8000${endpoint}${id}/`, { method: 'PATCH', headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }, body: JSON.stringify(item) }); if (!response.ok) { const error = await response.json(); throw new Error(JSON.stringify(error)); } }; const handleDelete = async (endpoint: string, id: string | number) => { const token = localStorage.getItem('token'); const response = await fetch(`http://127.0.0.1:8000${endpoint}${id}/`, { method: 'DELETE', headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' } }); if (!response.ok) { throw new Error('Failed to delete item'); } }; useEffect(() => { const module = modules.find(m => m.id === activeModule); if (module) { fetchData(module.endpoint); } }, [activeModule]); const activeModuleData = modules.find(m => m.id === activeModule); const config = getModuleConfig(activeModule); return ( <div className="p-6"> <div className="mb-6"> <h1 className="text-3xl font-bold text-foreground mb-4">CRUD Management</h1> <p className="text-muted-foreground">Manage all system data with full Create, Read, Update, Delete operations</p> </div> {/* Module Navigation */} <Card className="mb-6"> <div className="p-4"> <h2 className="text-lg font-semibold mb-3">Select Module</h2> <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2"> {modules.map((module) => ( <Button key={module.id} variant={activeModule === module.id ? 'default' : 'outline'} size="sm" onClick={() => setActiveModule(module.id)} className="text-xs" > {module.name} </Button> ))} </div> </div> </Card> {/* Data Table */} {activeModuleData && ( <Card> <div className="p-6"> <CRUDDataTable title={activeModuleData.name} data={data} columns={config.columns} onCreate={userRole === 'admin' ? (item) => handleCreate(activeModuleData.endpoint, item) : undefined} onUpdate={userRole === 'admin' ? (id, item) => handleUpdate(activeModuleData.endpoint, id, item) : undefined} onDelete={userRole === 'admin' ? (id) => handleDelete(activeModuleData.endpoint, id) : undefined} onRefresh={() => fetchData(activeModuleData.endpoint)} createFields={config.createFields} loading={loading} searchable={true} /> </div> </Card> )} </div> ); }; 