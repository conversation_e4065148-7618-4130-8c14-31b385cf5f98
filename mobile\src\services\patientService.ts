import { apiService } from './api'; import { userService } from './userService'; import { Patient, MedicalRecord } from '../types/patient'; import { PaginatedResponse } from '../types/api'; class PatientService { async getPatients(page = 1, pageSize = 20): Promise<PaginatedResponse<Patient>> { return apiService.getPaginated<Patient>(`/patients/patients/?page=${page}&page_size=${pageSize}`); } async getPatient(id: number): Promise<Patient> { return apiService.get<Patient>(`/patients/patients/${id}/`); } async getPatientByPatientId(patientId: string): Promise<Patient> { const response = await apiService.getPaginated<Patient>(`/patients/patients/?patient_id=${patientId}`); if (response.results.length === 0) { throw new Error('Patient not found'); } return response.results[0]; } async createPatient(data: Partial<Patient>): Promise<Patient> { return apiService.post<Patient>('/patients/patients/', data); } async updatePatient(id: number, data: Partial<Patient>): Promise<Patient> { return apiService.patch<Patient>(`/patients/patients/${id}/`, data); } async deletePatient(id: number): Promise<void> { return apiService.delete(`/patients/patients/${id}/`); } async getMedicalRecords(patientId: number, page = 1, pageSize = 20): Promise<PaginatedResponse<MedicalRecord>> { return apiService.getPaginated<MedicalRecord>(`/patients/medical-records/?patient=${patientId}&page=${page}&page_size=${pageSize}`); } async getMedicalRecord(id: number): Promise<MedicalRecord> { return apiService.get<MedicalRecord>(`/patients/medical-records/${id}/`); } async createMedicalRecord(data: Partial<MedicalRecord>): Promise<MedicalRecord> { return apiService.post<MedicalRecord>('/patients/medical-records/', data); } async updateMedicalRecord(id: number, data: Partial<MedicalRecord>): Promise<MedicalRecord> { return apiService.patch<MedicalRecord>(`/patients/medical-records/${id}/`, data); } async deleteMedicalRecord(id: number): Promise<void> { return apiService.delete(`/patients/medical-records/${id}/`); } // Patient-specific methods for mobile app async getMyProfile(): Promise<Patient> { return userService.getCurrentPatient(); } async updateMyProfile(data: Partial<Patient>): Promise<Patient> { const currentPatient = await userService.getCurrentPatient(); const result = await apiService.patch<Patient>(`/patients/patients/${currentPatient.id}/`, data); // Clear cache after update userService.clearPatientCache(); return result; } async getMyMedicalRecords(page = 1, pageSize = 20): Promise<PaginatedResponse<MedicalRecord>> { const currentPatient = await userService.getCurrentPatient(); return apiService.getPaginated<MedicalRecord>(`/patients/medical-records/?patient=${currentPatient.id}&page=${page}&page_size=${pageSize}`); } } export const patientService = new PatientService(); 