import React, { useEffect } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { RootState } from '../../store'; import { fetchUpcomingAppointments } from '../../store/slices/appointmentSlice'; import { fetchMyProfile } from '../../store/slices/patientSlice'; import { useTranslation } from '../../hooks/useTranslation'; const DashboardScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { user } = useSelector((state: RootState) => state.auth); const { appointments, isLoading: appointmentsLoading } = useSelector((state: RootState) => state.appointment); const { currentPatient } = useSelector((state: RootState) => state.patient); const [refreshing, setRefreshing] = React.useState(false); useEffect(() => { loadDashboardData(); }, [dispatch]); const loadDashboardData = async () => { try { await Promise.all([ dispatch(fetchUpcomingAppointments({ page: 1, pageSize: 5 })), dispatch(fetchMyProfile()), ]); } catch (error) { console.error('Failed to load dashboard data:', error); } }; const onRefresh = async () => { setRefreshing(true); await loadDashboardData(); setRefreshing(false); }; const getGreeting = () => { const hour = new Date().getHours(); const firstName = user?.first_name || t('common.user'); if (hour < 12) return `${t('dashboard.goodMorning')}, ${firstName}`; if (hour < 18) return `${t('dashboard.goodAfternoon')}, ${firstName}`; return `${t('dashboard.goodEvening')}, ${firstName}`; }; const upcomingAppointments = appointments.slice(0, 3); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <ScrollView className="flex-1" refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } showsVerticalScrollIndicator={false} > {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-lg ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {getGreeting()} </Text> <Text className={`text-xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('dashboard.welcomeToDashboard')} </Text> {currentPatient && ( <View className={`flex-row items-center mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('dashboard.patientId')}: {currentPatient.patient_id} </Text> <View className={`w-2 h-2 bg-green-500 rounded-full ${isRTL ? 'mr-3' : 'ml-3'}`} /> <Text className={`text-emerald-700 dark:text-emerald-400 text-sm ${isRTL ? 'mr-1' : 'ml-1'} font-medium`}>{t('common.active')}</Text> </View> )} </View> {/* Personal Stats */} <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Your Health Overview </Text> <View className="flex-row justify-between mb-4"> <StatsCard icon="calendar" title={t('navigation.appointments')} value={appointments.length.toString()} subtitle="Total" color="#007AFF" isDark={isDark} /> <StatsCard icon="document-text" title="Records" value="12" subtitle="Medical" color="#34C759" isDark={isDark} /> <StatsCard icon="time" title="Next Visit" value={upcomingAppointments.length > 0 ? "Tomorrow" : "None"} subtitle="Scheduled" color="#FF9500" isDark={isDark} /> </View> </View> {/* Quick Actions */} <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Quick Actions </Text> <View className="flex-row flex-wrap justify-between"> <QuickActionCard icon="calendar-outline" title="Book Appointment" onPress={() => navigation.navigate('BookAppointment')} isDark={isDark} /> <QuickActionCard icon="people-outline" title="Find Doctors" onPress={() => navigation.navigate('DoctorsList')} isDark={isDark} /> <QuickActionCard icon="document-text-outline" title={t('navigation.medicalRecords')} onPress={() => navigation.navigate('MedicalRecords')} isDark={isDark} /> <QuickActionCard icon="flask-outline" title="Lab Results" onPress={() => navigation.navigate('LabResults')} isDark={isDark} /> <QuickActionCard icon="medical-outline" title="Prescriptions" onPress={() => navigation.navigate('Prescriptions')} isDark={isDark} /> <QuickActionCard icon="heart-outline" title="Health Summary" onPress={() => navigation.navigate('HealthSummary')} isDark={isDark} /> <QuickActionCard icon="card-outline" title="Billing" onPress={() => navigation.navigate('Billing')} isDark={isDark} /> <QuickActionCard icon="call-outline" title="Emergency" onPress={() => navigation.navigate('Emergency')} isDark={isDark} /> </View> </View> {/* Upcoming Appointments */} <View className="px-6 mb-6"> <View className="flex-row justify-between items-center mb-4"> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Upcoming Appointments </Text> <TouchableOpacity onPress={() => navigation.navigate('Appointments')} > <Text className="text-primary-600 font-medium">View All</Text> </TouchableOpacity> </View> {appointmentsLoading ? ( <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm`}> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Loading appointments... </Text> </View> ) : upcomingAppointments.length > 0 ? ( <View className="space-y-3"> {upcomingAppointments.map((appointment) => ( <TouchableOpacity key={appointment.id} onPress={() => navigation.navigate('AppointmentDetail', { appointmentId: appointment.id })} className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`} > <View className="flex-row justify-between items-start"> <View className="flex-1"> <Text className={`font-semibold text-base ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Dr. {appointment.doctor_name} </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {appointment.appointment_type.replace('_', ' ').toUpperCase()} </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {new Date(appointment.appointment_date).toLocaleDateString()} at {appointment.appointment_time} </Text> </View> <View className={`px-3 py-1 rounded-full ${ appointment.status === 'confirmed' ? 'bg-green-100' : appointment.status === 'scheduled' ? 'bg-blue-100' : 'bg-muted' }`}> <Text className={`text-xs font-medium ${ appointment.status === 'confirmed' ? 'text-emerald-700 dark:text-emerald-400' : appointment.status === 'scheduled' ? 'text-sky-700 dark:text-sky-400' : 'text-foreground' }`}> {appointment.status.toUpperCase()} </Text> </View> </View> </TouchableOpacity> ))} </View> ) : ( <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm`}> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> No upcoming appointments </Text> <TouchableOpacity onPress={() => navigation.navigate('BookAppointment')} className="mt-3" > <Text className="text-primary-600 text-center font-medium"> Book your first appointment </Text> </TouchableOpacity> </View> )} </View> {/* Health Summary */} <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Health Summary </Text> <View className={`p-4 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> {currentPatient ? ( <View className="space-y-3"> {currentPatient.blood_group && ( <View className="flex-row justify-between"> <Text className={`${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Blood Group </Text> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {currentPatient.blood_group} </Text> </View> )} {currentPatient.allergies && ( <View className="flex-row justify-between"> <Text className={`${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Allergies </Text> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {currentPatient.allergies} </Text> </View> )} <TouchableOpacity onPress={() => navigation.navigate('MedicalRecords')} className="mt-2" > <Text className="text-primary-600 font-medium"> View Complete Medical History </Text> </TouchableOpacity> </View> ) : ( <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Complete your profile to see health summary </Text> )} </View> </View> </ScrollView> </SafeAreaView> ); }; interface QuickActionCardProps { icon: keyof typeof Ionicons.glyphMap; title: string; onPress: () => void; isDark: boolean; } const QuickActionCard: React.FC<QuickActionCardProps> = ({ icon, title, onPress, isDark }) => ( <TouchableOpacity onPress={onPress} className={`w-[48%] p-4 rounded-xl mb-3 ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`} > <View className={`w-12 h-12 rounded-xl mb-3 justify-center items-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name={icon} size={24} color="#007AFF" /> </View> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {title} </Text> </TouchableOpacity> ); interface StatsCardProps { icon: keyof typeof Ionicons.glyphMap; title: string; value: string; subtitle: string; color: string; isDark: boolean; } const StatsCard: React.FC<StatsCardProps> = ({ icon, title, value, subtitle, color, isDark }) => ( <View className={`flex-1 p-4 rounded-xl mx-1 ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm border ${ isDark ? 'border-dark-border' : 'border-border' }`}> <View className="items-center"> <View className={`w-10 h-10 rounded-full mb-2 justify-center items-center`} style={{ backgroundColor: `${color}20` }}> <Ionicons name={icon} size={20} color={color} /> </View> <Text className={`text-xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {value} </Text> <Text className={`text-xs ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {subtitle} </Text> <Text className={`text-xs font-medium ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {title} </Text> </View> </View> ); export default DashboardScreen; 