import React from 'react'; import { View, Text, ActivityIndicator } from 'react-native'; import { useTheme } from '../contexts/ThemeContext'; const LoadingScreen: React.FC = () => { const { isDark } = useTheme(); return ( <View className={`flex-1 justify-center items-center ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <ActivityIndicator size="large" color={isDark ? '#007AFF' : '#007AFF'} className="mb-4" /> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> HMS Mobile </Text> <Text className={`text-sm mt-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Loading... </Text> </View> ); }; export default LoadingScreen; 