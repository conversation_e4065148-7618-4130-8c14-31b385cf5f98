/** * Date Utilities * Centralized date formatting, manipulation, and validation functions */ export type DateFormat = | 'YYYY-MM-DD' | 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'DD-MM-YYYY' | 'YYYY/MM/DD' | 'MMM DD, YYYY' | 'MMMM DD, YYYY' | 'DD MMM YYYY' | 'DD MMMM YYYY'; export type TimeFormat = | 'HH:mm' | 'HH:mm:ss' | 'hh:mm A' | 'hh:mm:ss A'; export interface DateRange { start: Date; end: Date; } export interface TimeSlot { start: string; end: string; label?: string; } /** * Format date according to specified format */ export function formatDate(date: Date | string | number, format: DateFormat = 'YYYY-MM-DD'): string { const d = new Date(date); if (isNaN(d.getTime())) { return 'Invalid Date'; } const year = d.getFullYear(); const month = d.getMonth() + 1; const day = d.getDate(); const monthNames = [ 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December' ]; const monthNamesShort = [ 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec' ]; const pad = (num: number) => num.toString().padStart(2, '0'); switch (format) { case 'YYYY-MM-DD': return `${year}-${pad(month)}-${pad(day)}`; case 'DD/MM/YYYY': return `${pad(day)}/${pad(month)}/${year}`; case 'MM/DD/YYYY': return `${pad(month)}/${pad(day)}/${year}`; case 'DD-MM-YYYY': return `${pad(day)}-${pad(month)}-${year}`; case 'YYYY/MM/DD': return `${year}/${pad(month)}/${pad(day)}`; case 'MMM DD, YYYY': return `${monthNamesShort[month - 1]} ${pad(day)}, ${year}`; case 'MMMM DD, YYYY': return `${monthNames[month - 1]} ${pad(day)}, ${year}`; case 'DD MMM YYYY': return `${pad(day)} ${monthNamesShort[month - 1]} ${year}`; case 'DD MMMM YYYY': return `${pad(day)} ${monthNames[month - 1]} ${year}`; default: return d.toISOString().split('T')[0]; } } /** * Format time according to specified format */ export function formatTime(date: Date | string | number, format: TimeFormat = 'HH:mm'): string { const d = new Date(date); if (isNaN(d.getTime())) { return 'Invalid Time'; } const hours = d.getHours(); const minutes = d.getMinutes(); const seconds = d.getSeconds(); const pad = (num: number) => num.toString().padStart(2, '0'); switch (format) { case 'HH:mm': return `${pad(hours)}:${pad(minutes)}`; case 'HH:mm:ss': return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`; case 'hh:mm A': const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours; const ampm = hours >= 12 ? 'PM' : 'AM'; return `${pad(hour12)}:${pad(minutes)} ${ampm}`; case 'hh:mm:ss A': const hour12WithSeconds = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours; const ampmWithSeconds = hours >= 12 ? 'PM' : 'AM'; return `${pad(hour12WithSeconds)}:${pad(minutes)}:${pad(seconds)} ${ampmWithSeconds}`; default: return `${pad(hours)}:${pad(minutes)}`; } } /** * Format date and time together */ export function formatDateTime( date: Date | string | number, dateFormat: DateFormat = 'YYYY-MM-DD', timeFormat: TimeFormat = 'HH:mm' ): string { return `${formatDate(date, dateFormat)} ${formatTime(date, timeFormat)}`; } /** * Get relative time (e.g., "2 hours ago", "in 3 days") */ export function getRelativeTime(date: Date | string | number): string { const d = new Date(date); const now = new Date(); const diffMs = now.getTime() - d.getTime(); const diffSeconds = Math.floor(diffMs / 1000); const diffMinutes = Math.floor(diffSeconds / 60); const diffHours = Math.floor(diffMinutes / 60); const diffDays = Math.floor(diffHours / 24); const diffWeeks = Math.floor(diffDays / 7); const diffMonths = Math.floor(diffDays / 30); const diffYears = Math.floor(diffDays / 365); if (diffSeconds < 60) { return diffSeconds <= 0 ? 'just now' : `${diffSeconds} seconds ago`; } else if (diffMinutes < 60) { return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`; } else if (diffHours < 24) { return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`; } else if (diffDays < 7) { return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`; } else if (diffWeeks < 4) { return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`; } else if (diffMonths < 12) { return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`; } else { return `${diffYears} year${diffYears !== 1 ? 's' : ''} ago`; } } /** * Check if date is today */ export function isToday(date: Date | string | number): boolean { const d = new Date(date); const today = new Date(); return d.toDateString() === today.toDateString(); } /** * Check if date is yesterday */ export function isYesterday(date: Date | string | number): boolean { const d = new Date(date); const yesterday = new Date(); yesterday.setDate(yesterday.getDate() - 1); return d.toDateString() === yesterday.toDateString(); } /** * Check if date is tomorrow */ export function isTomorrow(date: Date | string | number): boolean { const d = new Date(date); const tomorrow = new Date(); tomorrow.setDate(tomorrow.getDate() + 1); return d.toDateString() === tomorrow.toDateString(); } /** * Check if date is in the past */ export function isPast(date: Date | string | number): boolean { const d = new Date(date); const now = new Date(); return d.getTime() < now.getTime(); } /** * Check if date is in the future */ export function isFuture(date: Date | string | number): boolean { const d = new Date(date); const now = new Date(); return d.getTime() > now.getTime(); } /** * Add days to a date */ export function addDays(date: Date | string | number, days: number): Date { const d = new Date(date); d.setDate(d.getDate() + days); return d; } /** * Add months to a date */ export function addMonths(date: Date | string | number, months: number): Date { const d = new Date(date); d.setMonth(d.getMonth() + months); return d; } /** * Add years to a date */ export function addYears(date: Date | string | number, years: number): Date { const d = new Date(date); d.setFullYear(d.getFullYear() + years); return d; } /** * Get start of day */ export function startOfDay(date: Date | string | number): Date { const d = new Date(date); d.setHours(0, 0, 0, 0); return d; } /** * Get end of day */ export function endOfDay(date: Date | string | number): Date { const d = new Date(date); d.setHours(23, 59, 59, 999); return d; } /** * Get start of week (Monday) */ export function startOfWeek(date: Date | string | number): Date { const d = new Date(date); const day = d.getDay(); const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday return new Date(d.setDate(diff)); } /** * Get end of week (Sunday) */ export function endOfWeek(date: Date | string | number): Date { const d = startOfWeek(date); d.setDate(d.getDate() + 6); return endOfDay(d); } /** * Get start of month */ export function startOfMonth(date: Date | string | number): Date { const d = new Date(date); return new Date(d.getFullYear(), d.getMonth(), 1); } /** * Get end of month */ export function endOfMonth(date: Date | string | number): Date { const d = new Date(date); return new Date(d.getFullYear(), d.getMonth() + 1, 0); } /** * Get days between two dates */ export function daysBetween(date1: Date | string | number, date2: Date | string | number): number { const d1 = new Date(date1); const d2 = new Date(date2); const diffTime = Math.abs(d2.getTime() - d1.getTime()); return Math.ceil(diffTime / (1000 * 60 * 60 * 24)); } /** * Check if date is within range */ export function isDateInRange(date: Date | string | number, range: DateRange): boolean { const d = new Date(date); return d >= range.start && d <= range.end; } /** * Generate date range */ export function generateDateRange(start: Date | string | number, end: Date | string | number): Date[] { const dates: Date[] = []; const startDate = new Date(start); const endDate = new Date(end); const currentDate = new Date(startDate); while (currentDate <= endDate) { dates.push(new Date(currentDate)); currentDate.setDate(currentDate.getDate() + 1); } return dates; } /** * Parse date string with multiple format support */ export function parseDate(dateString: string): Date | null { if (!dateString) return null; // Try ISO format first let date = new Date(dateString); if (!isNaN(date.getTime())) { return date; } // Try common formats const formats = [ /^(\d{4})-(\d{2})-(\d{2})$/, // YYYY-MM-DD /^(\d{2})\/(\d{2})\/(\d{4})$/, // DD/MM/YYYY or MM/DD/YYYY /^(\d{2})-(\d{2})-(\d{4})$/, // DD-MM-YYYY ]; for (const format of formats) { const match = dateString.match(format); if (match) { const [, part1, part2, part3] = match; // Try different interpretations const attempts = [ new Date(parseInt(part3), parseInt(part2) - 1, parseInt(part1)), // DD/MM/YYYY new Date(parseInt(part3), parseInt(part1) - 1, parseInt(part2)), // MM/DD/YYYY new Date(parseInt(part1), parseInt(part2) - 1, parseInt(part3)), // YYYY-MM-DD ]; for (const attempt of attempts) { if (!isNaN(attempt.getTime())) { return attempt; } } } } return null; } /** * Validate date string */ export function isValidDate(dateString: string): boolean { return parseDate(dateString) !== null; } /** * Get age from birth date */ export function getAge(birthDate: Date | string | number): number { const birth = new Date(birthDate); const today = new Date(); let age = today.getFullYear() - birth.getFullYear(); const monthDiff = today.getMonth() - birth.getMonth(); if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) { age--; } return age; } /** * Generate time slots for a day */ export function generateTimeSlots( startTime: string = '09:00', endTime: string = '17:00', intervalMinutes: number = 30 ): TimeSlot[] { const slots: TimeSlot[] = []; const [startHour, startMinute] = startTime.split(':').map(Number); const [endHour, endMinute] = endTime.split(':').map(Number); const startDate = new Date(); startDate.setHours(startHour, startMinute, 0, 0); const endDate = new Date(); endDate.setHours(endHour, endMinute, 0, 0); const current = new Date(startDate); while (current < endDate) { const slotStart = formatTime(current, 'HH:mm'); current.setMinutes(current.getMinutes() + intervalMinutes); const slotEnd = formatTime(current, 'HH:mm'); slots.push({ start: slotStart, end: slotEnd, label: `${slotStart} - ${slotEnd}`, }); } return slots; } 