/** * Date and time utilities for HMS Mobile * Provides consistent date/time formatting and manipulation */ export const DateTimeUtils = { /** * Format date for display */ formatDate(date: string | Date, format: 'short' | 'long' | 'medium' = 'medium'): string { const dateObj = typeof date === 'string' ? new Date(date) : date; if (isNaN(dateObj.getTime())) { return 'Invalid Date'; } const formatOptions = { short: { month: 'short' as const, day: 'numeric' as const, year: 'numeric' as const }, medium: { weekday: 'short' as const, month: 'short' as const, day: 'numeric' as const, year: 'numeric' as const }, long: { weekday: 'long' as const, month: 'long' as const, day: 'numeric' as const, year: 'numeric' as const }, }; const options: Intl.DateTimeFormatOptions = formatOptions[format] || formatOptions.short; return dateObj.toLocaleDateString('en-US', options); }, /** * Format time for display */ formatTime(time: string | Date, format: '12h' | '24h' = '12h'): string { let timeObj: Date; if (typeof time === 'string') { // Handle time string (HH:MM format) if (time.includes(':')) { const [hours, minutes] = time.split(':'); timeObj = new Date(); timeObj.setHours(parseInt(hours), parseInt(minutes), 0, 0); } else { timeObj = new Date(time); } } else { timeObj = time; } if (isNaN(timeObj.getTime())) { return 'Invalid Time'; } const options: Intl.DateTimeFormatOptions = { hour: 'numeric', minute: '2-digit', hour12: format === '12h', }; return timeObj.toLocaleTimeString('en-US', options); }, /** * Format date and time together */ formatDateTime(date: string | Date, time?: string, format: 'short' | 'long' = 'short'): string { const dateStr = this.formatDate(date, format === 'short' ? 'medium' : 'long'); if (time) { const timeStr = this.formatTime(time); return `${dateStr} at ${timeStr}`; } const dateObj = typeof date === 'string' ? new Date(date) : date; const timeStr = this.formatTime(dateObj); return `${dateStr} at ${timeStr}`; }, /** * Get relative time (e.g., "2 hours ago", "in 3 days") */ getRelativeTime(date: string | Date): string { const dateObj = typeof date === 'string' ? new Date(date) : date; const now = new Date(); const diffMs = dateObj.getTime() - now.getTime(); const diffMinutes = Math.floor(diffMs / (1000 * 60)); const diffHours = Math.floor(diffMs / (1000 * 60 * 60)); const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24)); if (Math.abs(diffMinutes) < 1) { return 'Just now'; } else if (Math.abs(diffMinutes) < 60) { return diffMinutes > 0 ? `in ${diffMinutes} minutes` : `${Math.abs(diffMinutes)} minutes ago`; } else if (Math.abs(diffHours) < 24) { return diffHours > 0 ? `in ${diffHours} hours` : `${Math.abs(diffHours)} hours ago`; } else if (Math.abs(diffDays) < 7) { return diffDays > 0 ? `in ${diffDays} days` : `${Math.abs(diffDays)} days ago`; } else { return this.formatDate(dateObj, 'short'); } }, /** * Check if date is today */ isToday(date: string | Date): boolean { const dateObj = typeof date === 'string' ? new Date(date) : date; const today = new Date(); return dateObj.toDateString() === today.toDateString(); }, /** * Check if date is tomorrow */ isTomorrow(date: string | Date): boolean { const dateObj = typeof date === 'string' ? new Date(date) : date; const tomorrow = new Date(); tomorrow.setDate(tomorrow.getDate() + 1); return dateObj.toDateString() === tomorrow.toDateString(); }, /** * Check if date is in the past */ isPast(date: string | Date): boolean { const dateObj = typeof date === 'string' ? new Date(date) : date; const now = new Date(); return dateObj < now; }, /** * Check if date is in the future */ isFuture(date: string | Date): boolean { const dateObj = typeof date === 'string' ? new Date(date) : date; const now = new Date(); return dateObj > now; }, /** * Get date in ISO format (YYYY-MM-DD) */ toISODate(date: Date = new Date()): string { return date.toISOString().split('T')[0]; }, /** * Get time in HH:MM format */ toTimeString(date: Date = new Date()): string { return date.toTimeString().slice(0, 5); }, /** * Parse appointment date and time */ parseAppointmentDateTime(date: string, time: string): Date { const [hours, minutes] = time.split(':'); const appointmentDate = new Date(date); appointmentDate.setHours(parseInt(hours), parseInt(minutes), 0, 0); return appointmentDate; }, /** * Get available time slots for a date */ getAvailableTimeSlots(date: string, bookedSlots: string[] = []): string[] { const slots: string[] = []; const startHour = 9; // 9 AM const endHour = 17; // 5 PM const slotDuration = 30; // 30 minutes for (let hour = startHour; hour < endHour; hour++) { for (let minute = 0; minute < 60; minute += slotDuration) { const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`; if (!bookedSlots.includes(timeSlot)) { slots.push(timeSlot); } } } return slots; }, /** * Get days of the week for date picker */ getWeekDays(startDate: Date = new Date()): Array<{ date: Date; dayName: string; isToday: boolean }> { const days = []; const start = new Date(startDate); start.setDate(start.getDate() - start.getDay()); // Start from Sunday for (let i = 0; i < 7; i++) { const date = new Date(start); date.setDate(start.getDate() + i); days.push({ date, dayName: date.toLocaleDateString('en-US', { weekday: 'short' }), isToday: this.isToday(date), }); } return days; }, /** * Get next available appointment date (excluding weekends) */ getNextAvailableDate(): string { const date = new Date(); date.setDate(date.getDate() + 1); // Start from tomorrow // Skip weekends while (date.getDay() === 0 || date.getDay() === 6) { date.setDate(date.getDate() + 1); } return this.toISODate(date); }, /** * Validate if a date is valid for appointment booking */ isValidAppointmentDate(date: string): { isValid: boolean; message?: string } { const appointmentDate = new Date(date); const today = new Date(); const maxDate = new Date(); maxDate.setMonth(maxDate.getMonth() + 6); // 6 months in advance if (isNaN(appointmentDate.getTime())) { return { isValid: false, message: 'Invalid date format' }; } if (appointmentDate < today) { return { isValid: false, message: 'Cannot book appointments in the past' }; } if (appointmentDate > maxDate) { return { isValid: false, message: 'Cannot book appointments more than 6 months in advance' }; } // Check if it's a weekend if (appointmentDate.getDay() === 0 || appointmentDate.getDay() === 6) { return { isValid: false, message: 'Appointments are not available on weekends' }; } return { isValid: true }; }, }; 