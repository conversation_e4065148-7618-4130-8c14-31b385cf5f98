import React from 'react'; import { useTranslation } from 'react-i18next'; import { useSelector, useDispatch } from 'react-redux'; import type { RootState } from '../../store'; import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'; import LanguageSwitcher from '../ui/LanguageSwitcher'; import ThemeToggle from '../ui/ThemeToggle'; import { useTheme } from '../../hooks/useTheme'; import { setGlassmorphismLevel, setReducedMotion } from '../../store/slices/themeSlice'; import type { GlassmorphismLevel } from '../../store/slices/themeSlice'; import { User, Bell, Shield, Palette, Globe, Monitor, Smartphone, Mail, Lock, Eye, EyeOff, Accessibility } from 'lucide-react'; const Settings: React.FC = () => { const { t } = useTranslation(); const dispatch = useDispatch(); const { user } = useSelector((state: RootState) => state.auth); const { mode, effectiveTheme, glassmorphismLevel, reducedMotion } = useTheme(); const handleGlassmorphismChange = (level: GlassmorphismLevel) => { dispatch(setGlassmorphismLevel(level)); }; const handleReducedMotionToggle = () => { dispatch(setReducedMotion(!reducedMotion)); }; return ( <div className="space-y-6"> {/* Page Header */} <div className="mb-8"> <h1 className="text-3xl font-bold text-foreground">Settings</h1> <p className="text-muted-foreground mt-2"> Manage your account preferences and application settings </p> </div> <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Theme Settings */} <Card className="macos-card"> <CardHeader> <CardTitle className="flex items-center gap-3 text-foreground"> <Palette className="w-5 h-5 text-sky-700 dark:text-sky-400" /> Appearance & Theme </CardTitle> </CardHeader> <CardContent className="space-y-6"> <div> <label className="block text-sm font-medium text-foreground mb-3"> Theme Mode </label> <ThemeToggle variant="dropdown" /> <p className="text-xs text-muted-foreground mt-2"> Current: {mode} {mode === 'system' && `(${effectiveTheme})`} </p> </div> <div> <label className="block text-sm font-medium text-foreground mb-3"> Glassmorphism Level </label> <div className="grid grid-cols-4 gap-2"> {(['none', 'subtle', 'medium', 'strong'] as GlassmorphismLevel[]).map((level) => ( <button key={level} onClick={() => handleGlassmorphismChange(level)} className={` p-2 rounded-lg text-xs font-medium transition-all duration-200 ${glassmorphismLevel === level ? 'bg-primary text-primary-foreground shadow-md' : 'bg-secondary text-secondary-foreground hover:bg-accent' } `} > {level.charAt(0).toUpperCase() + level.slice(1)} </button> ))} </div> </div> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-foreground"> Reduced Motion </p> <p className="text-xs text-muted-foreground"> Minimize animations and transitions </p> </div> <button onClick={handleReducedMotionToggle} className={` relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${reducedMotion ? 'bg-primary' : 'bg-secondary'} `} > <span className={` inline-block h-4 w-4 transform rounded-full bg-background transition-transform ${reducedMotion ? 'translate-x-6' : 'translate-x-1'} `} /> </button> </div> </CardContent> </Card> {/* Language Settings */} <Card className="macos-card"> <CardHeader> <CardTitle className="flex items-center gap-3 text-foreground"> <Globe className="w-5 h-5 text-emerald-700 dark:text-emerald-400" /> Language & Region </CardTitle> </CardHeader> <CardContent className="space-y-6"> <div> <label className="block text-sm font-medium text-foreground mb-3"> Display Language </label> <LanguageSwitcher /> </div> <div className="pt-4 border-t border-border"> <p className="text-sm text-muted-foreground"> Select your preferred language for the application interface. </p> </div> </CardContent> </Card> {/* Profile Settings */} <Card className="macos-card"> <CardHeader> <CardTitle className="flex items-center gap-3 text-foreground"> <User className="w-5 h-5 text-purple-500" /> Profile Information </CardTitle> </CardHeader> <CardContent className="space-y-4"> <div className="flex items-center gap-4"> <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white"> <span className="text-xl font-medium"> {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)} </span> </div> <div> <h3 className="font-semibold text-foreground"> {user?.first_name} {user?.last_name} </h3> <p className="text-sm text-muted-foreground capitalize"> {user?.role} </p> <p className="text-sm text-muted-foreground"> {user?.email} </p> </div> </div> <div className="pt-4 border-t border-border"> <button className="text-sm text-primary hover:text-primary/80 transition-colors"> Edit Profile Information </button> </div> </CardContent> </Card> {/* Notification Settings */} <Card className="macos-card"> <CardHeader> <CardTitle className="flex items-center gap-3 text-foreground"> <Bell className="w-5 h-5 text-orange-500" /> Notifications </CardTitle> </CardHeader> <CardContent className="space-y-4"> <div className="space-y-3"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-foreground"> Email Notifications </p> <p className="text-xs text-muted-foreground"> Receive notifications via email </p> </div> <input type="checkbox" defaultChecked className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2" /> </div> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-foreground"> Push Notifications </p> <p className="text-xs text-muted-foreground"> Receive push notifications in browser </p> </div> <input type="checkbox" defaultChecked className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2" /> </div> </div> </CardContent> </Card> {/* Security Settings */} <Card className="macos-card"> <CardHeader> <CardTitle className="flex items-center gap-3 text-foreground"> <Shield className="w-5 h-5 text-red-500" /> Security & Privacy </CardTitle> </CardHeader> <CardContent className="space-y-4"> <div className="space-y-3"> <button className="w-full flex items-center justify-between p-3 text-left hover:bg-accent rounded-lg transition-colors"> <div className="flex items-center gap-3"> <Lock className="w-4 h-4 text-muted-foreground" /> <span className="text-sm text-foreground">Change Password</span> </div> <span className="text-xs text-muted-foreground">→</span> </button> <button className="w-full flex items-center justify-between p-3 text-left hover:bg-accent rounded-lg transition-colors"> <div className="flex items-center gap-3"> <Eye className="w-4 h-4 text-muted-foreground" /> <span className="text-sm text-foreground">Privacy Settings</span> </div> <span className="text-xs text-muted-foreground">→</span> </button> </div> </CardContent> </Card> {/* System Information */} <Card className="macos-card"> <CardHeader> <CardTitle className="flex items-center gap-3 text-foreground"> <Monitor className="w-5 h-5 text-indigo-500" /> System Information </CardTitle> </CardHeader> <CardContent className="space-y-3"> <div className="text-sm space-y-2"> <div className="flex justify-between"> <span className="text-muted-foreground">Version:</span> <span className="text-foreground">HMS AI v2.1.0</span> </div> <div className="flex justify-between"> <span className="text-muted-foreground">Last Updated:</span> <span className="text-foreground"> {new Date().toLocaleDateString()} </span> </div> <div className="flex justify-between"> <span className="text-muted-foreground">Environment:</span> <span className="text-foreground">Production</span> </div> <div className="flex justify-between"> <span className="text-muted-foreground">Theme:</span> <span className="text-foreground capitalize"> {mode} {mode === 'system' && `(${effectiveTheme})`} </span> </div> </div> </CardContent> </Card> </div> </div> ); }; export default Settings; 