import { User } from './auth'; import { Patient } from './patient'; import { Appointment } from './appointment'; export interface Invoice { id: number; invoice_id: string; patient: Patient; appointment?: Appointment; created_by: User; // Invoice details invoice_date: string; due_date: string; // Amounts (using number for frontend, backend handles decimal precision) subtotal: number; tax_amount: number; discount_amount: number; total_amount: number; paid_amount: number; // Status status: 'draft' | 'sent' | 'paid' | 'partial' | 'overdue' | 'cancelled'; // Additional info notes?: string; // Related items items: InvoiceItem[]; payments: Payment[]; insurance_claims: InsuranceClaim[]; // Computed properties balance_due: number; is_paid: boolean; created_at: string; updated_at: string; } export interface InvoiceItem { id: number; invoice: number; // Item details description: string; item_type: 'consultation' | 'procedure' | 'medication' | 'lab_test' | 'imaging' | 'room_charge' | 'other'; quantity: number; unit_price: number; total_price: number; created_at: string; } export interface Payment { id: number; payment_id: string; invoice: Invoice; patient: Patient; processed_by: User; // Payment details amount: number; payment_method: 'cash' | 'card' | 'check' | 'bank_transfer' | 'insurance' | 'other'; payment_date: string; reference_number?: string; notes?: string; // Status status: 'pending' | 'completed' | 'failed' | 'refunded'; created_at: string; updated_at: string; } export interface InsuranceClaim { id: number; claim_id: string; patient: Patient; invoice: Invoice; submitted_by: User; // Claim details insurance_provider: string; policy_number: string; claim_amount: number; approved_amount: number; // Dates submitted_date: string; processed_date?: string; // Status status: 'submitted' | 'under_review' | 'approved' | 'partially_approved' | 'denied' | 'paid'; denial_reason?: string; notes?: string; created_at: string; updated_at: string; } // API Request Types export interface InvoiceCreateRequest { patient: number; appointment?: number; due_date: string; items: InvoiceItemCreateRequest[]; tax_amount?: number; discount_amount?: number; notes?: string; } export interface InvoiceItemCreateRequest { description: string; item_type: string; quantity: number; unit_price: number; } export interface InvoiceUpdateRequest extends Partial<InvoiceCreateRequest> { status?: string; } export interface PaymentCreateRequest { invoice: number; patient: number; amount: number; payment_method: string; reference_number?: string; notes?: string; } export interface InsuranceClaimCreateRequest { patient: number; invoice: number; insurance_provider: string; policy_number: string; claim_amount: number; } export interface InsuranceClaimUpdateRequest extends Partial<InsuranceClaimCreateRequest> { status?: string; approved_amount?: number; denial_reason?: string; notes?: string; } // API Response Types export interface InvoiceListResponse { count: number; next?: string; previous?: string; results: Invoice[]; } export interface PaymentListResponse { count: number; next?: string; previous?: string; results: Payment[]; } export interface InsuranceClaimListResponse { count: number; next?: string; previous?: string; results: InsuranceClaim[]; } export interface BillingStatsResponse { total_invoices: number; total_revenue: number; pending_payments: number; overdue_invoices: number; paid_invoices: number; draft_invoices: number; cancelled_invoices: number; // Monthly stats monthly_revenue: Array<{ month: string; revenue: number; invoices: number; }>; // Payment method breakdown payment_methods: Record<string, { count: number; amount: number; }>; // Insurance claims stats insurance_claims: { total: number; approved: number; denied: number; pending: number; total_claimed: number; total_approved: number; }; // Top patients by revenue top_patients: Array<{ patient: Patient; total_amount: number; invoice_count: number; }>; } export interface PaymentStatsResponse { total_payments: number; total_amount: number; successful_payments: number; failed_payments: number; refunded_payments: number; // Payment trends daily_payments: Array<{ date: string; count: number; amount: number; }>; // Payment method stats payment_method_stats: Record<string, { count: number; amount: number; percentage: number; }>; } // Utility Types export interface BillingFilters { patient?: number; status?: string; date_from?: string; date_to?: string; payment_method?: string; insurance_provider?: string; amount_min?: number; amount_max?: number; } export interface BillingSortOptions { field: 'invoice_date' | 'due_date' | 'total_amount' | 'status' | 'patient'; direction: 'asc' | 'desc'; } 