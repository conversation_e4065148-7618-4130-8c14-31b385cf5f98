import React, { createContext, useContext, useEffect, ReactNode } from 'react'; import { useSelector, useDispatch } from 'react-redux'; import type { RootState } from '../store'; import { updateSystemTheme } from '../store/slices/themeSlice'; import type { ThemeMode, GlassmorphismLevel } from '../store/slices/themeSlice'; interface ThemeContextType { mode: ThemeMode; effectiveTheme: 'light' | 'dark'; systemTheme: 'light' | 'dark'; glassmorphismLevel: GlassmorphismLevel; reducedMotion: boolean; isDark: boolean; } const ThemeContext = createContext<ThemeContextType | undefined>(undefined); interface ThemeProviderProps { children: ReactNode; } export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => { const dispatch = useDispatch(); const theme = useSelector((state: RootState) => state.theme); // Listen for system theme changes useEffect(() => { if (typeof window !== 'undefined' && window.matchMedia) { const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)'); const handleChange = (e: MediaQueryListEvent) => { dispatch(updateSystemTheme(e.matches ? 'dark' : 'light')); }; mediaQuery.addEventListener('change', handleChange); return () => { mediaQuery.removeEventListener('change', handleChange); }; } }, [dispatch]); // Apply theme to document useEffect(() => { const root = document.documentElement; // Remove existing theme classes root.classList.remove('light', 'dark'); // Add current theme class root.classList.add(theme.effectiveTheme); // Apply glassmorphism level root.setAttribute('data-glassmorphism', theme.glassmorphismLevel); // Apply reduced motion preference if (theme.reducedMotion) { root.classList.add('reduce-motion'); } else { root.classList.remove('reduce-motion'); } }, [theme.effectiveTheme, theme.glassmorphismLevel, theme.reducedMotion]); const contextValue: ThemeContextType = { mode: theme.mode, effectiveTheme: theme.effectiveTheme, systemTheme: theme.systemTheme, glassmorphismLevel: theme.glassmorphismLevel, reducedMotion: theme.reducedMotion, isDark: theme.effectiveTheme === 'dark', }; return ( <ThemeContext.Provider value={contextValue}> {children} </ThemeContext.Provider> ); }; export const useTheme = (): ThemeContextType => { const context = useContext(ThemeContext); if (context === undefined) { throw new Error('useTheme must be used within a ThemeProvider'); } return context; }; 