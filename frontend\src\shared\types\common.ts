/** * Common Types for HMS Frontend * Shared type definitions used across the application */ import type { ReactNode, ComponentType } from 'react'; // Base entity types export interface BaseEntity { id: string | number; created_at?: string; updated_at?: string; } // User and authentication types export interface User extends BaseEntity { username: string; email: string; first_name: string; last_name: string; full_name: string; role: UserRole; is_active: boolean; phone_number?: string; avatar?: string; last_login?: string; } export type UserRole = 'admin' | 'doctor' | 'nurse' | 'patient' | 'receptionist' | 'staff'; // Status types export type Status = 'active' | 'inactive' | 'pending' | 'completed' | 'cancelled' | 'draft'; export type Priority = 'low' | 'medium' | 'high' | 'urgent'; export type Severity = 'info' | 'warning' | 'error' | 'success'; // UI component types export interface IconProps { size?: number; className?: string; color?: string; } export interface MetricData { label: string; value: string | number; change?: string; trend?: 'up' | 'down' | 'stable'; icon: ComponentType<IconProps>; color: string; description?: string; } export interface QuickAction { title: string; description: string; icon: ComponentType<IconProps>; action: () => void; disabled?: boolean; badge?: string | number; } export interface NavigationItem { id: string; label: string; icon: ComponentType<IconProps>; path: string; badge?: string | number; children?: NavigationItem[]; roles?: UserRole[]; } // Table and data display types export interface ColumnDefinition<T = any> { key: keyof T | string; title: string; sortable?: boolean; filterable?: boolean; width?: string | number; align?: 'left' | 'center' | 'right'; render?: (value: any, record: T, index: number) => ReactNode; className?: string; } export interface TableAction<T = any> { key: string; label: string; icon?: ComponentType<IconProps>; onClick: (record: T) => void; disabled?: (record: T) => boolean; variant?: 'primary' | 'secondary' | 'danger'; confirm?: { title: string; message: string; }; } export interface PaginationConfig { page: number; pageSize: number; total: number; showSizeChanger?: boolean; showQuickJumper?: boolean; pageSizeOptions?: number[]; } // Form types export interface FormFieldConfig { name: string; label: string; type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date' | 'checkbox' | 'radio'; required?: boolean; placeholder?: string; options?: Array<{ label: string; value: any }>; validation?: ValidationRule[]; disabled?: boolean; hidden?: boolean; icon?: ComponentType<IconProps>; description?: string; } export interface ValidationRule { type: 'required' | 'email' | 'min' | 'max' | 'pattern' | 'custom'; value?: any; message: string; validator?: (value: any) => boolean; } export interface FormSection { title: string; description?: string; fields: FormFieldConfig[]; collapsible?: boolean; defaultExpanded?: boolean; } // Modal and dialog types export interface ModalConfig { title: string; content: ReactNode; width?: string | number; height?: string | number; closable?: boolean; maskClosable?: boolean; footer?: ReactNode; onOk?: () => void; onCancel?: () => void; } export interface ConfirmDialogConfig { title: string; message: string; confirmText?: string; cancelText?: string; variant?: 'info' | 'warning' | 'error' | 'success'; onConfirm: () => void; onCancel?: () => void; } // Notification types export interface NotificationConfig { id?: string; title: string; message: string; type: 'info' | 'success' | 'warning' | 'error'; duration?: number; closable?: boolean; action?: { label: string; onClick: () => void; }; } // Theme types export interface ThemeConfig { mode: 'light' | 'dark' | 'system'; primaryColor: string; borderRadius: number; glassmorphism: { enabled: boolean; blur: number; opacity: number; }; } // Localization types export interface LocaleConfig { code: string; name: string; direction: 'ltr' | 'rtl'; flag?: string; } // Search and filter types export interface SearchConfig { placeholder: string; fields: string[]; debounceMs?: number; } export interface FilterOption { label: string; value: any; count?: number; } export interface FilterConfig { key: string; label: string; type: 'select' | 'multiselect' | 'date' | 'daterange' | 'number' | 'boolean'; options?: FilterOption[]; placeholder?: string; } // Chart and analytics types export interface ChartDataPoint { label: string; value: number; color?: string; } export interface ChartConfig { type: 'line' | 'bar' | 'pie' | 'doughnut' | 'area'; data: ChartDataPoint[]; title?: string; height?: number; colors?: string[]; responsive?: boolean; } // File upload types export interface FileUploadConfig { accept: string[]; maxSize: number; // in bytes maxFiles: number; multiple?: boolean; preview?: boolean; } export interface UploadedFile { id: string; name: string; size: number; type: string; url?: string; progress?: number; status: 'uploading' | 'success' | 'error'; error?: string; } // Dashboard types export interface DashboardWidget { id: string; title: string; type: 'metric' | 'chart' | 'table' | 'list' | 'custom'; size: 'small' | 'medium' | 'large'; position: { x: number; y: number }; data?: any; config?: any; refreshInterval?: number; } export interface DashboardLayout { id: string; name: string; widgets: DashboardWidget[]; isDefault?: boolean; } // Medical specific types (HMS context) export interface MedicalRecord extends BaseEntity { patient_id: string; record_type: string; title: string; description: string; date: string; doctor_id?: string; attachments?: string[]; tags?: string[]; } export interface Appointment extends BaseEntity { appointment_id: string; patient_id: string; doctor_id: string; appointment_date: string; appointment_time: string; duration_minutes: number; appointment_type: string; status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'; reason_for_visit: string; notes?: string; consultation_fee: number; } // Utility types export type DeepPartial<T> = { [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]; }; export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>; export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>; export type Nullable<T> = T | null; export type Optional<T> = T | undefined; // Event types export interface CustomEvent<T = any> { type: string; payload: T; timestamp: number; source?: string; } // API response wrapper export interface ApiResult<T = any> { data: T; success: boolean; message?: string; errors?: Record<string, string[]>; } // Component state types export interface ComponentState { loading: boolean; error: string | null; data: any; } // Export commonly used type combinations export type ID = string | number; export type Timestamp = string; export type Currency = number; export type Percentage = number; 