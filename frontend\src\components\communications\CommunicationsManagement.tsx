import React, { useState, useEffect } from 'react'; import { useTranslation } from 'react-i18next'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import { Button } from '../ui/Button'; import { Badge } from '../ui/badge'; import { Input } from '../ui/Input'; import DataTable from '../ui/DataTable'; import { MessageSquare, Bell, Megaphone, Mail, MessageCircle, Plus, Search, Filter, Eye, Edit, Send, Users, Clock, CheckCircle } from 'lucide-react'; import { communicationsService } from '../../services/communicationsService'; interface Notification { id: number; title: string; message: string; notification_type: string; is_read: boolean; is_urgent: boolean; created_at: string; recipient: { full_name: string; }; } interface Message { id: number; subject: string; body: string; is_read: boolean; is_urgent: boolean; sent_at: string; sender: { full_name: string; }; recipient: { full_name: string; }; } interface Announcement { id: number; title: string; content: string; is_urgent: boolean; is_active: boolean; publish_date: string; target_roles: string; created_by: { full_name: string; }; } const CommunicationsManagement: React.FC = () => { const { t } = useTranslation(); const [activeTab, setActiveTab] = useState<'notifications' | 'messages' | 'announcements' | 'templates'>('notifications'); const [notifications, setNotifications] = useState<Notification[]>([]); const [messages, setMessages] = useState<Message[]>([]); const [announcements, setAnnouncements] = useState<Announcement[]>([]); const [loading, setLoading] = useState(false); const [searchTerm, setSearchTerm] = useState(''); useEffect(() => { loadData(); }, [activeTab]); const loadData = async () => { setLoading(true); try { if (activeTab === 'notifications') { const response = await communicationsService.getNotifications(); setNotifications(Array.isArray(response.data) ? response.data : (response.data as any).results || []); } else if (activeTab === 'messages') { const response = await communicationsService.getMessages(); setMessages(Array.isArray(response.data) ? response.data : (response.data as any).results || []); } else if (activeTab === 'announcements') { const response = await communicationsService.getAnnouncements(); setAnnouncements(Array.isArray(response.data) ? response.data : (response.data as any).results || []); } } catch (error) { console.error('Error loading communications data:', error); } finally { setLoading(false); } }; const getNotificationTypeColor = (type: string) => { switch (type) { case 'appointment': return 'status-info'; case 'prescription': return 'status-success'; case 'lab_result': return 'bg-purple-50/80 text-purple-700 border-purple-200/50 dark:bg-purple-950/20 dark:text-purple-400 dark:border-purple-800/50'; case 'billing': return 'status-warning'; case 'alert': return 'status-error'; case 'reminder': return 'bg-orange-50/80 text-orange-700 border-orange-200/50 dark:bg-orange-950/20 dark:text-orange-400 dark:border-orange-800/50'; default: return 'bg-muted text-muted-foreground border-border'; } }; const notificationColumns = [ { key: 'title', title: 'Title', render: (value: string, record: Notification) => ( <div className="flex items-center gap-2"> {record.is_urgent && <Bell className="w-4 h-4 text-red-500" />} <span className={`font-medium ${!record.is_read ? 'font-bold' : ''}`}>{value}</span> </div> ) }, { key: 'notification_type', title: 'Type', render: (type: string) => ( <Badge className={`${getNotificationTypeColor(type)} rounded-full px-2 py-1 text-xs`}> {type.replace('_', ' ').toUpperCase()} </Badge> ) }, { key: 'recipient', title: 'Recipient', render: (recipient: any) => ( <span className="text-sm">{recipient.full_name}</span> ) }, { key: 'is_read', title: 'Status', render: (isRead: boolean) => ( <Badge className={`${isRead ? 'status-success ' : 'status-warning '} rounded-full px-2 py-1 text-xs`}> {isRead ? 'Read' : 'Unread'} </Badge> ) }, { key: 'created_at', title: 'Created', render: (time: string) => ( <span className="text-sm">{new Date(time).toLocaleString()}</span> ) } ]; const messageColumns = [ { key: 'subject', title: 'Subject', render: (value: string, record: Message) => ( <div className="flex items-center gap-2"> {record.is_urgent && <MessageCircle className="w-4 h-4 text-red-500" />} <span className={`font-medium ${!record.is_read ? 'font-bold' : ''}`}>{value}</span> </div> ) }, { key: 'sender', title: 'From', render: (sender: any) => ( <span className="text-sm">{sender.full_name}</span> ) }, { key: 'recipient', title: 'To', render: (recipient: any) => ( <span className="text-sm">{recipient.full_name}</span> ) }, { key: 'is_read', title: 'Status', render: (isRead: boolean) => ( <Badge className={`${isRead ? 'status-success ' : 'status-warning '} rounded-full px-2 py-1 text-xs`}> {isRead ? 'Read' : 'Unread'} </Badge> ) }, { key: 'sent_at', title: 'Sent', render: (time: string) => ( <span className="text-sm">{new Date(time).toLocaleString()}</span> ) } ]; const announcementColumns = [ { key: 'title', title: 'Title', render: (value: string, record: Announcement) => ( <div className="flex items-center gap-2"> {record.is_urgent && <Megaphone className="w-4 h-4 text-red-500" />} <span className="font-medium">{value}</span> </div> ) }, { key: 'target_roles', title: 'Target Roles', render: (roles: string) => ( <div className="flex flex-wrap gap-1"> {roles.split(',').map((role, index) => ( <Badge key={index} className="status-info rounded-full px-2 py-1 text-xs"> {role.trim()} </Badge> ))} </div> ) }, { key: 'created_by', title: 'Created By', render: (creator: any) => ( <span className="text-sm">{creator.full_name}</span> ) }, { key: 'is_active', title: 'Status', render: (isActive: boolean) => ( <Badge className={`${isActive ? 'status-success ' : 'status-error '} rounded-full px-2 py-1 text-xs`}> {isActive ? 'Active' : 'Inactive'} </Badge> ) }, { key: 'publish_date', title: 'Published', render: (time: string) => ( <span className="text-sm">{new Date(time).toLocaleString()}</span> ) } ]; const renderActions = (record: any) => ( <div className="flex items-center gap-2"> <Button variant="ghost" size="sm" onClick={() => { /* TODO: Handle action */ }} className="h-8 w-8 p-0" > <Eye className="h-4 w-4" /> </Button> <Button variant="ghost" size="sm" onClick={() => { /* TODO: Handle action */ }} className="h-8 w-8 p-0" > <Edit className="h-4 w-4" /> </Button> </div> ); const tabs = [ { id: 'notifications', label: 'Notifications', icon: Bell }, { id: 'messages', label: 'Messages', icon: MessageSquare }, { id: 'announcements', label: 'Announcements', icon: Megaphone }, { id: 'templates', label: 'Templates', icon: Mail } ]; return ( <div className="space-y-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */} <Card className="glass border-0 shadow-xl"> <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"> <MessageSquare className="w-6 h-6 text-white" /> </div> <div> <CardTitle className="text-2xl font-bold macos-text-primary"> Communications </CardTitle> <CardDescription className="macos-text-secondary"> Manage notifications, messages, and announcements </CardDescription> </div> </div> <Button className="bg-blue-600 hover:bg-blue-700 text-white"> <Plus className="w-4 h-4 mr-2" /> New Communication </Button> </div> </CardHeader> </Card> {/* Navigation Tabs */} <Card className="glass border-0 shadow-xl"> <CardContent className="p-6"> <div className="flex space-x-1 bg-muted dark:bg-gray-800 p-1 rounded-lg"> {tabs.map((tab) => { const Icon = tab.icon; return ( <button key={tab.id} onClick={() => setActiveTab(tab.id as any)} className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${ activeTab === tab.id ? 'bg-background dark:bg-gray-700 text-sky-700 dark:text-sky-400 shadow-sm' : 'text-muted-foreground dark:text-gray-400 hover:text-foreground dark:hover:text-gray-200' }`} > <Icon className="w-4 h-4" /> {tab.label} </button> ); })} </div> </CardContent> </Card> {/* Content */} <Card className="glass border-0 shadow-xl"> <CardHeader> <div className="flex items-center justify-between"> <CardTitle className="flex items-center gap-2"> {activeTab === 'notifications' && <><Bell className="w-5 h-5" /> Notifications</>} {activeTab === 'messages' && <><MessageSquare className="w-5 h-5" /> Messages</>} {activeTab === 'announcements' && <><Megaphone className="w-5 h-5" /> Announcements</>} {activeTab === 'templates' && <><Mail className="w-5 h-5" /> Templates</>} </CardTitle> <div className="flex items-center gap-2"> <div className="relative"> <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" /> <Input placeholder="Search..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10 w-64" /> </div> <Button variant="outline" size="sm"> <Filter className="w-4 h-4 mr-2" /> Filter </Button> </div> </div> </CardHeader> <CardContent> {activeTab === 'notifications' && ( <DataTable data={notifications} columns={notificationColumns} loading={loading} actions={{ title: 'Actions', render: renderActions, }} /> )} {activeTab === 'messages' && ( <DataTable data={messages} columns={messageColumns} loading={loading} actions={{ title: 'Actions', render: renderActions, }} /> )} {activeTab === 'announcements' && ( <DataTable data={announcements} columns={announcementColumns} loading={loading} actions={{ title: 'Actions', render: renderActions, }} /> )} {activeTab === 'templates' && ( <div className="text-center py-12"> <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4"> <Mail className="w-8 h-8 text-gray-400" /> </div> <h3 className="text-lg font-medium text-foreground mb-2"> Communication Templates </h3> <p className="text-muted-foreground mb-4"> Manage email and SMS templates for automated communications </p> <Button> <Plus className="w-4 h-4 mr-2" /> Create Template </Button> </div> )} </CardContent> </Card> </div> </div> ); }; export default CommunicationsManagement; 