/** * Arabic Localization Demo Component * Demonstrates the comprehensive Arabic language support implementation */ import React, { useState } from 'react'; import { useTranslation } from 'react-i18next'; import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'; import { Button } from '../ui/Button'; import { Badge } from '../ui/badge'; import LanguageSwitcher from '../ui/LanguageSwitcher'; import DataTable from '../../shared/components/data-display/DataTable'; import { MetricGrid } from '../../shared/components/data-display/MetricCard'; import { Globe, CheckCircle, Users, Calendar, FileText, Activity, ArrowRight, Languages } from 'lucide-react'; const ArabicLocalizationDemo: React.FC = () => { const { t, i18n } = useTranslation(); const [activeTab, setActiveTab] = useState<'overview' | 'components' | 'data'>('overview'); const isRTL = i18n.language === 'ar'; // Demo metrics data const demoMetrics = [ { id: 'total-keys', label: t('audit.translationKeys'), value: '1,191', icon: Languages, color: 'feature-blue', description: t('audit.coverage'), }, { id: 'components', label: t('common.components'), value: '15+', icon: FileText, color: 'feature-green', description: t('audit.rtlSupport'), }, { id: 'languages', label: t('common.languages'), value: '2', icon: Globe, color: 'feature-purple', description: 'English & Arabic', }, { id: 'coverage', label: t('audit.coverage'), value: '100%', icon: CheckCircle, color: 'feature-orange', description: t('dashboard.title'), }, ]; // Demo table data const demoTableData = [ { id: 1, component: 'AdminDashboard', status: t('common.completed'), keys: 25, rtl: true, }, { id: 2, component: 'DoctorDashboard', status: t('common.completed'), keys: 30, rtl: true, }, { id: 3, component: 'PatientDashboard', status: t('common.completed'), keys: 35, rtl: true, }, { id: 4, component: 'DataTable', status: t('common.completed'), keys: 15, rtl: true, }, ]; const tableColumns = [ { key: 'component', title: t('common.component'), sortable: true, }, { key: 'status', title: t('common.status'), render: (value: string) => ( <Badge variant="default" className="capitalize"> {value} </Badge> ), }, { key: 'keys', title: t('audit.translationKeys'), render: (value: number) => `${value} ${t('audit.keys')}`, }, { key: 'rtl', title: t('audit.rtlSupport'), render: (value: boolean) => ( <Badge variant={value ? 'default' : 'destructive'}> {value ? '✓' : '✗'} </Badge> ), }, ]; return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */} <Card className="glass border-0 shadow-lg"> <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="p-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"> <Globe className="w-8 h-8 text-white" /> </div> <div> <CardTitle className="text-2xl font-bold macos-text-primary"> {t('demo.arabicSupportDemo')} </CardTitle> <p className="macos-text-secondary"> {t('demo.completeArabicLocalization')} </p> </div> </div> <LanguageSwitcher /> </div> </CardHeader> </Card> {/* Language Info */} <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center gap-3 mb-4"> <Languages className="w-6 h-6 text-sky-700 dark:text-sky-400" /> <h3 className="text-lg font-semibold macos-text-primary"> {t('demo.currentLanguage')} </h3> </div> <div className="space-y-2"> <p className="macos-text-secondary"> <span className="font-medium">{t('common.language')}:</span> {i18n.language === 'ar' ? 'العربية' : 'English'} </p> <p className="macos-text-secondary"> <span className="font-medium">{t('demo.textDirection')}:</span> {isRTL ? t('demo.rtlRightToLeft') : t('demo.ltrLeftToRight')} </p> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center gap-3 mb-4"> <CheckCircle className="w-6 h-6 text-emerald-700 dark:text-emerald-400" /> <h3 className="text-lg font-semibold macos-text-primary"> {t('demo.arabicSupportFeatures')} </h3> </div> <div className="space-y-2"> <div className="flex items-center gap-2"> <CheckCircle className="w-4 h-4 text-green-500" /> <span className="text-sm macos-text-secondary">{t('demo.completeTranslation')}</span> </div> <div className="flex items-center gap-2"> <CheckCircle className="w-4 h-4 text-green-500" /> <span className="text-sm macos-text-secondary">{t('audit.rtlSupport')}</span> </div> <div className="flex items-center gap-2"> <CheckCircle className="w-4 h-4 text-green-500" /> <span className="text-sm macos-text-secondary">{t('common.dynamicSwitching')}</span> </div> </div> </CardContent> </Card> </div> {/* Tabs */} <Card className="glass border-0 shadow-lg"> <CardHeader> <div className="flex space-x-2"> {[ { key: 'overview', label: t('dashboard.overview') }, { key: 'components', label: t('common.components') }, { key: 'data', label: t('common.data') }, ].map((tab) => ( <Button key={tab.key} variant={activeTab === tab.key ? 'glass-primary' : 'outline'} size="sm" onClick={() => setActiveTab(tab.key as any)} > {tab.label} </Button> ))} </div> </CardHeader> <CardContent> {activeTab === 'overview' && ( <div className="space-y-6"> <MetricGrid metrics={demoMetrics} columns={4} /> <div className="text-center py-8"> <h3 className="text-xl font-semibold macos-text-primary mb-4"> {t('test.howToUseTestPage')} </h3> <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-4xl mx-auto"> <div className="flex items-start gap-3"> <ArrowRight className="w-5 h-5 text-blue-500 mt-0.5" /> <span className="text-sm macos-text-secondary">{t('test.switchBetweenLanguages')}</span> </div> <div className="flex items-start gap-3"> <ArrowRight className="w-5 h-5 text-blue-500 mt-0.5" /> <span className="text-sm macos-text-secondary">{t('test.checkTranslationsCorrect')}</span> </div> <div className="flex items-start gap-3"> <ArrowRight className="w-5 h-5 text-blue-500 mt-0.5" /> <span className="text-sm macos-text-secondary">{t('test.verifyRTLChanges')}</span> </div> <div className="flex items-start gap-3"> <ArrowRight className="w-5 h-5 text-blue-500 mt-0.5" /> <span className="text-sm macos-text-secondary">{t('test.testFormInteractions')}</span> </div> </div> </div> </div> )} {activeTab === 'components' && ( <div className="space-y-4"> <h3 className="text-lg font-semibold macos-text-primary"> {t('test.sampleUIComponents')} </h3> <div className="grid grid-cols-2 md:grid-cols-4 gap-4"> <Button variant="glass">{t('common.save')}</Button> <Button variant="outline">{t('common.cancel')}</Button> <Button variant="destructive">{t('common.delete')}</Button> <Button variant="default">{t('common.edit')}</Button> </div> <div className="flex flex-wrap gap-2 mt-4"> <Badge variant="default">{t('common.active')}</Badge> <Badge variant="secondary">{t('common.pending')}</Badge> <Badge variant="destructive">{t('common.cancelled')}</Badge> <Badge variant="outline">{t('common.completed')}</Badge> </div> </div> )} {activeTab === 'data' && ( <div className="space-y-4"> <h3 className="text-lg font-semibold macos-text-primary"> {t('audit.translationAuditReport')} </h3> <DataTable data={demoTableData} columns={tableColumns} searchable={true} variant="glass" emptyMessage={t('messages.noData')} /> </div> )} </CardContent> </Card> </div> </div> ); }; export default ArabicLocalizationDemo; 