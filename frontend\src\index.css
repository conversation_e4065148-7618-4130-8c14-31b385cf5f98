@tailwind base;
@tailwind components;
@tailwind utilities;

/* =================================
   HMS UNIFIED STYLE SYSTEM
   ================================= */

/* Glassmorphism Effects */
.glass {
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-light {
  backdrop-filter: blur(12px) saturate(160%);
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.glass-heavy {
  backdrop-filter: blur(20px) saturate(200%);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Dark Mode Glassmorphism */
.dark .glass {
  background: rgba(17, 24, 39, 0.85);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark .glass-light {
  background: rgba(17, 24, 39, 0.7);
  border: 1px solid rgba(75, 85, 99, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.dark .glass-heavy {
  background: rgba(17, 24, 39, 0.9);
  border: 1px solid rgba(75, 85, 99, 0.4);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* Sidebar styles */
#main-dashboard-sidebar {
  position: relative;
  z-index: 10;
}

.sidebar {
  width: 18rem;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(156, 163, 175, 0.3);
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.sidebar-nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  color: rgb(55, 65, 81);
}

.sidebar-nav-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.sidebar-nav-item--active {
  background: linear-gradient(135deg, rgb(59, 130, 246), rgb(99, 102, 241));
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Dark mode sidebar */
.dark .sidebar-nav-item {
  color: rgb(209, 213, 219);
}

.dark .sidebar-nav-item:hover {
  background: rgba(75, 85, 99, 0.5);
  color: rgb(147, 197, 253);
}

/* =================================
   BUTTON SYSTEM
   ================================= */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.btn-glass {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: rgb(55, 65, 81);
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.dark .btn-glass {
  background: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: rgb(209, 213, 219);
}

.dark .btn-glass:hover {
  background: rgba(17, 24, 39, 0.9);
}

/* =================================
   CARD SYSTEM
   ================================= */

.card {
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.dark .card {
  background: rgba(17, 24, 39, 0.85);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark .card:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* =================================
   STATUS SYSTEM
   ================================= */

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid;
}

.status-success {
  background: rgba(16, 185, 129, 0.1);
  color: rgb(5, 150, 105);
  border-color: rgba(16, 185, 129, 0.2);
}

.status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: rgb(217, 119, 6);
  border-color: rgba(245, 158, 11, 0.2);
}

.status-error {
  background: rgba(239, 68, 68, 0.1);
  color: rgb(220, 38, 38);
  border-color: rgba(239, 68, 68, 0.2);
}

.status-info {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(37, 99, 235);
  border-color: rgba(59, 130, 246, 0.2);
}

.status-neutral {
  background: rgba(107, 114, 128, 0.1);
  color: rgb(75, 85, 99);
  border-color: rgba(107, 114, 128, 0.2);
}

/* Dark mode status */
.dark .status-success {
  background: rgba(16, 185, 129, 0.2);
  color: rgb(52, 211, 153);
  border-color: rgba(16, 185, 129, 0.3);
}

.dark .status-warning {
  background: rgba(245, 158, 11, 0.2);
  color: rgb(251, 191, 36);
  border-color: rgba(245, 158, 11, 0.3);
}

.dark .status-error {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(248, 113, 113);
  border-color: rgba(239, 68, 68, 0.3);
}

.dark .status-info {
  background: rgba(59, 130, 246, 0.2);
  color: rgb(147, 197, 253);
  border-color: rgba(59, 130, 246, 0.3);
}

.dark .status-neutral {
  background: rgba(107, 114, 128, 0.2);
  color: rgb(156, 163, 175);
  border-color: rgba(107, 114, 128, 0.3);
}

/* =================================
   LEGACY COMPATIBILITY
   ================================= */

.macos-card {
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .macos-card {
  background: rgba(17, 24, 39, 0.85);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.macos-button {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: rgb(55, 65, 81);
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.macos-button:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.dark .macos-button {
  background: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: rgb(209, 213, 219);
}

.dark .macos-button:hover {
  background: rgba(17, 24, 39, 0.9);
}

.macos-input {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: rgb(17, 24, 39);
  transition: all 0.2s ease;
}

.macos-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .macos-input {
  background: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: rgb(255, 255, 255);
}

.dark .macos-input:focus {
  background: rgba(17, 24, 39, 0.9);
  border-color: rgba(147, 197, 253, 0.5);
  box-shadow: 0 0 0 3px rgba(147, 197, 253, 0.1);
}

/* Text colors */
.macos-text-primary {
  color: rgb(17, 24, 39);
}

.dark .macos-text-primary {
  color: rgb(255, 255, 255);
}

.macos-text-secondary {
  color: rgb(75, 85, 99);
}

.dark .macos-text-secondary {
  color: rgb(209, 213, 219);
}

.macos-text-tertiary {
  color: rgb(107, 114, 128);
}

.dark .macos-text-tertiary {
  color: rgb(156, 163, 175);
}

.macos-accent-text {
  color: rgb(59, 130, 246);
}

.dark .macos-accent-text {
  color: rgb(147, 197, 253);
}

/* =================================
   ANIMATIONS
   ================================= */

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-in {
  animation: slideInLeft 0.3s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}
