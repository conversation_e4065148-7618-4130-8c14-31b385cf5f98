#!/usr/bin/env python3
"""
Fix formatting issues in TypeScript files
"""

import os
import re
from pathlib import Path

def fix_file_formatting(file_path):
    """Fix formatting issues in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # If the file is all on one line, we need to format it
        if '\n' not in content or content.count('\n') < 5:
            # Add semicolons after import statements
            content = re.sub(r"import ([^;]+) from ([^;]+)", r"import \1 from \2;", content)
            
            # Add line breaks after semicolons in import statements
            content = re.sub(r"';(\s*)import", r"';\n import", content)
            
            # Add line breaks after other semicolons
            content = re.sub(r";(\s*)([a-zA-Z])", r";\n\n\1\2", content)
            
            # Add line breaks before function declarations
            content = re.sub(r"(\s*)(const|function|class)\s+", r"\n\n\1\2 ", content)
            
            # Add line breaks before return statements
            content = re.sub(r"(\s*)return\s*\(", r"\n\1return (", content)
            
            # Add line breaks for JSX
            content = re.sub(r">(\s*)<", r">\n\1<", content)
            
            # Clean up multiple line breaks
            content = re.sub(r'\n{3,}', '\n\n', content)
            
            # Write back the formatted content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content.strip() + '\n')
            
            return True
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False
    
    return False

def main():
    """Main function"""
    frontend_dir = Path('frontend/src')
    
    if not frontend_dir.exists():
        print("Frontend directory not found")
        return
    
    # Find all TypeScript files
    ts_files = list(frontend_dir.rglob('*.ts')) + list(frontend_dir.rglob('*.tsx'))
    
    fixed_files = []
    
    for file_path in ts_files:
        if fix_file_formatting(file_path):
            fixed_files.append(file_path.name)
    
    if fixed_files:
        print(f"Fixed formatting in {len(fixed_files)} files:")
        for file_name in fixed_files:
            print(f"  - {file_name}")
    else:
        print("No files needed formatting fixes")

if __name__ == '__main__':
    main()
