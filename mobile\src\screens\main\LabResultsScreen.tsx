import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl, Alert } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { RootState, AppDispatch } from '../../store'; import { fetchMyLabResults, fetchLabResultsByStatus } from '../../store/slices/labResultSlice'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; interface LabResult { id: number; test_name: string; test_type: string; result_value: string; reference_range: string; unit: string; status: 'normal' | 'abnormal' | 'critical' | 'pending'; test_date: string; doctor_name: string; lab_name: string; notes?: string; report_url?: string; } const LabResultsScreen: React.FC = () => { const navigation = useNavigation(); const dispatch = useDispatch<AppDispatch>(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const { labResults, isLoading, error } = useSelector((state: RootState) => state.labResult); const [refreshing, setRefreshing] = useState(false); const [filter, setFilter] = useState<'all' | 'recent' | 'abnormal'>('all'); // Load lab results on component mount useEffect(() => { loadLabResults(); }, [dispatch]); // Load lab results based on current filter useEffect(() => { if (filter === 'abnormal') { dispatch(fetchLabResultsByStatus('abnormal')); } else { dispatch(fetchMyLabResults({ page: 1, pageSize: 20 })); } }, [filter, dispatch]); const loadLabResults = () => { if (filter === 'abnormal') { dispatch(fetchLabResultsByStatus('abnormal')); } else { dispatch(fetchMyLabResults({ page: 1, pageSize: 20 })); } }; const onRefresh = async () => { setRefreshing(true); loadLabResults(); setRefreshing(false); }; // Show error alert if there's an error useEffect(() => { if (error) { Alert.alert(t('common.error'), error); } }, [error, t]); const getFilteredResults = () => { switch (filter) { case 'recent': return labResults.filter(result => { const testDate = new Date(result.test_date); const thirtyDaysAgo = new Date(); thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30); return testDate >= thirtyDaysAgo; }); case 'abnormal': return labResults.filter(result => result.status === 'abnormal' || result.status === 'critical'); default: return labResults; } }; const getStatusColor = (status: string) => { switch (status) { case 'normal': return 'status-success'; case 'abnormal': return 'status-warning'; case 'critical': return 'status-error'; case 'pending': return 'status-info'; default: return 'bg-muted text-foreground'; } }; const getStatusIcon = (status: string) => { switch (status) { case 'normal': return 'checkmark-circle'; case 'abnormal': return 'warning'; case 'critical': return 'alert-circle'; case 'pending': return 'time'; default: return 'help-circle'; } }; const handleResultPress = (result: LabResult) => { navigation.navigate('LabResultDetail' as never, { resultId: result.id }); }; const handleDownloadReport = (result: LabResult) => { if (result.report_url) { Alert.alert( t('labResults.downloadReport'), t('labResults.downloadConfirm'), [ { text: t('common.cancel'), style: 'cancel' }, { text: t('common.download'), onPress: () => { // TODO: Implement download functionality Alert.alert(t('common.success'), t('labResults.downloadStarted')); }} ] ); } }; if (isLoading) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <LoadingSpinner /> </SafeAreaView> ); } const filteredResults = getFilteredResults(); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('labResults.title')} </Text> <Text className={`text-sm mt-1 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('labResults.subtitle')} </Text> </View> {/* Filter Tabs */} <View className="px-6 mb-4"> <ScrollView horizontal showsHorizontalScrollIndicator={false}> <View className={`flex-row space-x-3 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> {[ { key: 'all', label: t('common.all') }, { key: 'recent', label: t('labResults.recent') }, { key: 'abnormal', label: t('labResults.abnormal') } ].map((tab) => ( <TouchableOpacity key={tab.key} onPress={() => setFilter(tab.key as any)} className={`px-4 py-2 rounded-full ${ filter === tab.key ? 'bg-primary-600' : isDark ? 'bg-dark-card border border-dark-border' : 'bg-background border border-border' }`} > <Text className={`font-medium ${ filter === tab.key ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {tab.label} </Text> </TouchableOpacity> ))} </View> </ScrollView> </View> {/* Results List */} <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false} refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } > {filteredResults.length === 0 ? ( <View className="flex-1 justify-center items-center py-12"> <Ionicons name="flask-outline" size={64} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-xl font-bold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('labResults.noResults')} </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('labResults.noResultsMessage')} </Text> </View> ) : ( <View className="space-y-4 pb-8"> {filteredResults.map((result) => ( <Card key={result.id} className="p-4"> <TouchableOpacity onPress={() => handleResultPress(result)}> <View className={`flex-row items-start justify-between ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <View className="flex-1"> <View className={`flex-row items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Ionicons name={getStatusIcon(result.status)} size={20} color={result.status === 'normal' ? '#10B981' : result.status === 'abnormal' ? '#F59E0B' : result.status === 'critical' ? '#EF4444' : '#3B82F6'} style={{ marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }} /> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {result.test_name} </Text> </View> <Text className={`text-sm mb-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {result.test_type} • {new Date(result.test_date).toLocaleDateString()} </Text> {result.status !== 'pending' && ( <View className={`flex-row items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {result.result_value} {result.unit} </Text> <Text className={`text-sm ml-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> ({t('labResults.reference')}: {result.reference_range}) </Text> </View> )} <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('labResults.doctor')}: {result.doctor_name} </Text> </View> <View className={`items-end ${isRTL ? 'items-start' : ''}`}> <Badge variant={result.status === 'normal' ? 'success' : result.status === 'abnormal' ? 'warning' : result.status === 'critical' ? 'destructive' : 'secondary'} className="mb-2" > {t(`labResults.status.${result.status}`)} </Badge> {result.report_url && ( <TouchableOpacity onPress={() => handleDownloadReport(result)} className="flex-row items-center" > <Ionicons name="download-outline" size={16} color="#007AFF" /> <Text className="text-primary-600 text-sm ml-1"> {t('common.download')} </Text> </TouchableOpacity> )} </View> </View> </TouchableOpacity> </Card> ))} </View> )} </ScrollView> </SafeAreaView> ); }; export default LabResultsScreen; 