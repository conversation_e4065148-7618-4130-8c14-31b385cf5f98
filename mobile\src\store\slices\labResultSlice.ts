import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { LabResult, LabResultDetail, labResultService } from '../../services/labResultService'; interface LabResultState { labResults: LabResult[]; currentLabResult: LabResultDetail | null; isLoading: boolean; error: string | null; pagination: { page: number; totalPages: number; totalCount: number; }; } const initialState: LabResultState = { labResults: [], currentLabResult: null, isLoading: false, error: null, pagination: { page: 1, totalPages: 1, totalCount: 0, }, }; // Async thunks export const fetchMyLabResults = createAsyncThunk( 'labResult/fetchMyLabResults', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await labResultService.getMyLabResults(page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch lab results'); } } ); export const fetchLabResultById = createAsyncThunk( 'labResult/fetchLabResultById', async (id: number, { rejectWithValue }) => { try { const response = await labResultService.getLabResultById(id); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch lab result details'); } } ); export const downloadLabReport = createAsyncThunk( 'labResult/downloadLabReport', async (id: number, { rejectWithValue }) => { try { const response = await labResultService.downloadLabReport(id); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to download lab report'); } } ); export const shareLabResult = createAsyncThunk( 'labResult/shareLabResult', async ({ id, doctorEmail, message }: { id: number; doctorEmail: string; message?: string }, { rejectWithValue }) => { try { const response = await labResultService.shareLabResult(id, doctorEmail, message); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to share lab result'); } } ); export const fetchLabResultsByStatus = createAsyncThunk( 'labResult/fetchLabResultsByStatus', async (status: string, { rejectWithValue }) => { try { const response = await labResultService.getLabResultsByStatus(status); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch lab results by status'); } } ); const labResultSlice = createSlice({ name: 'labResult', initialState, reducers: { clearError: (state) => { state.error = null; }, clearCurrentLabResult: (state) => { state.currentLabResult = null; }, setPage: (state, action: PayloadAction<number>) => { state.pagination.page = action.payload; }, }, extraReducers: (builder) => { builder // Fetch my lab results .addCase(fetchMyLabResults.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMyLabResults.fulfilled, (state, action) => { state.isLoading = false; state.labResults = action.payload.results; state.pagination = { page: action.payload.page || 1, totalPages: action.payload.total_pages || 1, totalCount: action.payload.count || 0, }; }) .addCase(fetchMyLabResults.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch lab result by ID .addCase(fetchLabResultById.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchLabResultById.fulfilled, (state, action) => { state.isLoading = false; state.currentLabResult = action.payload; }) .addCase(fetchLabResultById.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Download lab report .addCase(downloadLabReport.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(downloadLabReport.fulfilled, (state) => { state.isLoading = false; }) .addCase(downloadLabReport.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Share lab result .addCase(shareLabResult.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(shareLabResult.fulfilled, (state) => { state.isLoading = false; }) .addCase(shareLabResult.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch lab results by status .addCase(fetchLabResultsByStatus.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchLabResultsByStatus.fulfilled, (state, action) => { state.isLoading = false; state.labResults = action.payload; }) .addCase(fetchLabResultsByStatus.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); }, }); export const { clearError, clearCurrentLabResult, setPage } = labResultSlice.actions; export default labResultSlice.reducer; 