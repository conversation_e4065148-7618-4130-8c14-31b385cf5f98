import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { PatientState, Patient, MedicalRecord } from '../../types/patient'; import { patientService } from '../../services/patientService'; const initialState: PatientState = { patients: [], currentPatient: null, medicalRecords: [], isLoading: false, error: null, }; // Async thunks export const fetchPatients = createAsyncThunk( 'patient/fetchPatients', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await patientService.getPatients(page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch patients'); } } ); export const fetchPatient = createAsyncThunk( 'patient/fetchPatient', async (id: number, { rejectWithValue }) => { try { const patient = await patientService.getPatient(id); return patient; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch patient'); } } ); export const fetchMyProfile = createAsyncThunk( 'patient/fetchMyProfile', async (_, { rejectWithValue }) => { try { const patient = await patientService.getMyProfile(); return patient; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch profile'); } } ); export const updateMyProfile = createAsyncThunk( 'patient/updateMyProfile', async (data: Partial<Patient>, { rejectWithValue }) => { try { const patient = await patientService.updateMyProfile(data); return patient; } catch (error: any) { return rejectWithValue(error.message || 'Failed to update profile'); } } ); export const fetchMedicalRecords = createAsyncThunk( 'patient/fetchMedicalRecords', async ({ patientId, page = 1, pageSize = 20 }: { patientId: number; page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await patientService.getMedicalRecords(patientId, page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch medical records'); } } ); export const fetchMyMedicalRecords = createAsyncThunk( 'patient/fetchMyMedicalRecords', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await patientService.getMyMedicalRecords(page, pageSize); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch medical records'); } } ); export const createPatient = createAsyncThunk( 'patient/createPatient', async (data: Partial<Patient>, { rejectWithValue }) => { try { const patient = await patientService.createPatient(data); return patient; } catch (error: any) { return rejectWithValue(error.message || 'Failed to create patient'); } } ); export const updatePatient = createAsyncThunk( 'patient/updatePatient', async ({ id, data }: { id: number; data: Partial<Patient> }, { rejectWithValue }) => { try { const patient = await patientService.updatePatient(id, data); return patient; } catch (error: any) { return rejectWithValue(error.message || 'Failed to update patient'); } } ); const patientSlice = createSlice({ name: 'patient', initialState, reducers: { clearError: (state) => { state.error = null; }, setCurrentPatient: (state, action) => { state.currentPatient = action.payload; }, clearPatients: (state) => { state.patients = []; state.currentPatient = null; state.medicalRecords = []; }, }, extraReducers: (builder) => { // Fetch Patients builder .addCase(fetchPatients.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchPatients.fulfilled, (state, action) => { state.isLoading = false; state.patients = action.payload.results; state.error = null; }) .addCase(fetchPatients.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch Patient builder .addCase(fetchPatient.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchPatient.fulfilled, (state, action) => { state.isLoading = false; state.currentPatient = action.payload; state.error = null; }) .addCase(fetchPatient.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch My Profile builder .addCase(fetchMyProfile.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMyProfile.fulfilled, (state, action) => { state.isLoading = false; state.currentPatient = action.payload; state.error = null; }) .addCase(fetchMyProfile.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Update My Profile builder .addCase(updateMyProfile.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(updateMyProfile.fulfilled, (state, action) => { state.isLoading = false; state.currentPatient = action.payload; state.error = null; }) .addCase(updateMyProfile.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch Medical Records builder .addCase(fetchMedicalRecords.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMedicalRecords.fulfilled, (state, action) => { state.isLoading = false; state.medicalRecords = action.payload.results; state.error = null; }) .addCase(fetchMedicalRecords.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch My Medical Records builder .addCase(fetchMyMedicalRecords.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMyMedicalRecords.fulfilled, (state, action) => { state.isLoading = false; state.medicalRecords = action.payload.results; state.error = null; }) .addCase(fetchMyMedicalRecords.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Create Patient builder .addCase(createPatient.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(createPatient.fulfilled, (state, action) => { state.isLoading = false; state.patients.push(action.payload); state.error = null; }) .addCase(createPatient.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Update Patient builder .addCase(updatePatient.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(updatePatient.fulfilled, (state, action) => { state.isLoading = false; const index = state.patients.findIndex(p => p.id === action.payload.id); if (index !== -1) { state.patients[index] = action.payload; } if (state.currentPatient?.id === action.payload.id) { state.currentPatient = action.payload; } state.error = null; }) .addCase(updatePatient.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); }, }); export const { clearError, setCurrentPatient, clearPatients } = patientSlice.actions; export default patientSlice.reducer; 