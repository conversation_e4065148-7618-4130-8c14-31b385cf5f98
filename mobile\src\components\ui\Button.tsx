import React from 'react'; import { TouchableOpacity, Text, ActivityIndicator, View } from 'react-native'; import { useTheme } from '../../contexts/ThemeContext'; interface ButtonProps { title: string; onPress: () => void; variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'; size?: 'sm' | 'md' | 'lg'; disabled?: boolean; loading?: boolean; icon?: React.ReactNode; iconPosition?: 'left' | 'right'; fullWidth?: boolean; className?: string; } const Button: React.FC<ButtonProps> = ({ title, onPress, variant = 'primary', size = 'md', disabled = false, loading = false, icon, iconPosition = 'left', fullWidth = false, className = '', }) => { const { isDark } = useTheme(); const getVariantStyles = () => { const baseStyles = 'rounded-xl items-center justify-center flex-row'; switch (variant) { case 'primary': return `${baseStyles} bg-primary-600 ${disabled || loading ? 'opacity-50' : ''}`; case 'secondary': return `${baseStyles} ${isDark ? 'bg-dark-muted' : 'bg-muted'} ${disabled || loading ? 'opacity-50' : ''}`; case 'outline': return `${baseStyles} border-2 border-primary-600 ${disabled || loading ? 'opacity-50' : ''}`; case 'ghost': return `${baseStyles} ${disabled || loading ? 'opacity-50' : ''}`; case 'destructive': return `${baseStyles} bg-destructive ${disabled || loading ? 'opacity-50' : ''}`; default: return baseStyles; } }; const getSizeStyles = () => { switch (size) { case 'sm': return 'px-3 py-2'; case 'md': return 'px-4 py-3'; case 'lg': return 'px-6 py-4'; default: return 'px-4 py-3'; } }; const getTextStyles = () => { const baseTextStyles = 'font-semibold'; const sizeTextStyles = { sm: 'text-sm', md: 'text-base', lg: 'text-lg', }[size]; const variantTextStyles = { primary: 'text-white', secondary: isDark ? 'text-dark-foreground' : 'text-foreground', outline: 'text-primary-600', ghost: isDark ? 'text-dark-foreground' : 'text-foreground', destructive: 'text-white', }[variant]; return `${baseTextStyles} ${sizeTextStyles} ${variantTextStyles}`; }; const getSpinnerColor = () => { switch (variant) { case 'primary': case 'destructive': return '#FFFFFF'; case 'outline': return '#007AFF'; default: return isDark ? '#FFFFFF' : '#000000'; } }; return ( <TouchableOpacity onPress={onPress} disabled={disabled || loading} className={`${getVariantStyles()} ${getSizeStyles()} ${fullWidth ? 'w-full' : ''} ${className}`} activeOpacity={0.7} > {loading ? ( <ActivityIndicator size="small" color={getSpinnerColor()} /> ) : ( <View className="flex-row items-center"> {icon && iconPosition === 'left' && ( <View className="mr-2">{icon}</View> )} <Text className={getTextStyles()}>{title}</Text> {icon && iconPosition === 'right' && ( <View className="ml-2">{icon}</View> )} </View> )} </TouchableOpacity> ); }; export default Button; 