/** * Validation utilities for HMS Mobile * Provides consistent validation functions across the app */ export const ValidationUtils = { /** * Validate email format */ isValidEmail(email: string): boolean { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; return emailRegex.test(email.trim()); }, /** * Validate phone number format */ isValidPhoneNumber(phone: string): boolean { const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/; return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, '')); }, /** * Validate password strength */ isValidPassword(password: string): { isValid: boolean; message?: string } { if (password.length < 8) { return { isValid: false, message: 'Password must be at least 8 characters long' }; } if (!/(?=.*[a-z])/.test(password)) { return { isValid: false, message: 'Password must contain at least one lowercase letter' }; } if (!/(?=.*[A-Z])/.test(password)) { return { isValid: false, message: 'Password must contain at least one uppercase letter' }; } if (!/(?=.*\d)/.test(password)) { return { isValid: false, message: 'Password must contain at least one number' }; } return { isValid: true }; }, /** * Validate username format */ isValidUsername(username: string): boolean { const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/; return usernameRegex.test(username.trim()); }, /** * Validate patient ID format (P000XXX) */ isValidPatientId(patientId: string): boolean { const patientIdRegex = /^P\d{6}$/; return patientIdRegex.test(patientId.trim()); }, /** * Validate required field */ isRequired(value: string): boolean { return value.trim().length > 0; }, /** * Validate name format */ isValidName(name: string): boolean { const nameRegex = /^[a-zA-Z\s]{2,50}$/; return nameRegex.test(name.trim()); }, /** * Validate date format (YYYY-MM-DD) */ isValidDate(date: string): boolean { const dateRegex = /^\d{4}-\d{2}-\d{2}$/; if (!dateRegex.test(date)) return false; const parsedDate = new Date(date); return parsedDate instanceof Date && !isNaN(parsedDate.getTime()); }, /** * Validate time format (HH:MM) */ isValidTime(time: string): boolean { const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/; return timeRegex.test(time); }, /** * Validate appointment date (must be in the future) */ isValidAppointmentDate(date: string): { isValid: boolean; message?: string } { if (!this.isValidDate(date)) { return { isValid: false, message: 'Invalid date format' }; } const appointmentDate = new Date(date); const today = new Date(); today.setHours(0, 0, 0, 0); if (appointmentDate < today) { return { isValid: false, message: 'Appointment date must be in the future' }; } return { isValid: true }; }, /** * Validate form data */ validateForm(data: Record<string, any>, rules: Record<string, any>): { isValid: boolean; errors: Record<string, string> } { const errors: Record<string, string> = {}; for (const [field, rule] of Object.entries(rules)) { const value = data[field]; if (rule.required && !this.isRequired(value)) { errors[field] = `${field} is required`; continue; } if (value && rule.email && !this.isValidEmail(value)) { errors[field] = 'Invalid email format'; continue; } if (value && rule.phone && !this.isValidPhoneNumber(value)) { errors[field] = 'Invalid phone number format'; continue; } if (value && rule.password) { const passwordValidation = this.isValidPassword(value); if (!passwordValidation.isValid) { errors[field] = passwordValidation.message || 'Invalid password'; continue; } } if (value && rule.username && !this.isValidUsername(value)) { errors[field] = 'Username must be 3-20 characters and contain only letters, numbers, and underscores'; continue; } if (value && rule.name && !this.isValidName(value)) { errors[field] = 'Name must be 2-50 characters and contain only letters and spaces'; continue; } if (value && rule.minLength && value.length < rule.minLength) { errors[field] = `${field} must be at least ${rule.minLength} characters`; continue; } if (value && rule.maxLength && value.length > rule.maxLength) { errors[field] = `${field} must be no more than ${rule.maxLength} characters`; continue; } } return { isValid: Object.keys(errors).length === 0, errors, }; }, }; 