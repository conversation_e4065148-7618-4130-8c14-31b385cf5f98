#!/usr/bin/env python3
"""
HMS Theme Validation Script
Validates theme consistency and dark/light mode functionality
"""

import os
import json
from pathlib import Path
from typing import Dict, List

class ThemeValidator:
    """Validates HMS theme system consistency"""
    
    def __init__(self):
        self.frontend_dir = Path('frontend/src')
        self.issues = []
        self.successes = []
        
    def validate_css_variables(self) -> bool:
        """Validate CSS variables are properly defined"""
        css_file = self.frontend_dir / 'index.css'
        
        if not css_file.exists():
            self.issues.append("❌ index.css not found")
            return False
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_variables = [
            '--background', '--foreground', '--card', '--card-foreground',
            '--primary', '--primary-foreground', '--secondary', '--secondary-foreground',
            '--muted', '--muted-foreground', '--accent', '--accent-foreground',
            '--destructive', '--destructive-foreground', '--border', '--input', '--ring'
        ]
        
        missing_vars = []
        for var in required_variables:
            if var not in content:
                missing_vars.append(var)
        
        if missing_vars:
            self.issues.append(f"❌ Missing CSS variables: {', '.join(missing_vars)}")
            return False
        
        # Check dark mode variables
        if '.dark {' not in content:
            self.issues.append("❌ Dark mode CSS variables not found")
            return False
        
        self.successes.append("✅ CSS variables properly defined")
        return True
    
    def validate_unified_style_system(self) -> bool:
        """Validate unified style system exists and is complete"""
        style_file = self.frontend_dir / 'shared' / 'styles' / 'unifiedStyleSystem.ts'
        
        if not style_file.exists():
            self.issues.append("❌ Unified style system not found")
            return False
        
        with open(style_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_exports = [
            'BASE_COLORS', 'GLASS_EFFECTS', 'STATUS_COLORS', 'ROLE_COLORS',
            'PRIORITY_COLORS', 'TEXT_STYLES', 'BUTTON_VARIANTS', 'CARD_VARIANTS',
            'getStatusColor', 'getRoleColor', 'getPriorityColor'
        ]
        
        missing_exports = []
        for export in required_exports:
            if export not in content:
                missing_exports.append(export)
        
        if missing_exports:
            self.issues.append(f"❌ Missing style system exports: {', '.join(missing_exports)}")
            return False
        
        self.successes.append("✅ Unified style system is complete")
        return True
    
    def validate_tailwind_config(self) -> bool:
        """Validate Tailwind config uses CSS variables"""
        config_file = Path('frontend/tailwind.config.js')
        
        if not config_file.exists():
            self.issues.append("❌ Tailwind config not found")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if CSS variables are used
        if 'rgb(var(--' not in content:
            self.issues.append("❌ Tailwind config doesn't use CSS variables")
            return False
        
        required_colors = ['background', 'foreground', 'primary', 'secondary', 'muted', 'accent']
        missing_colors = []
        
        for color in required_colors:
            if f'var(--{color})' not in content:
                missing_colors.append(color)
        
        if missing_colors:
            self.issues.append(f"❌ Missing Tailwind colors: {', '.join(missing_colors)}")
            return False
        
        self.successes.append("✅ Tailwind config uses CSS variables")
        return True
    
    def validate_theme_context(self) -> bool:
        """Validate theme context and Redux integration"""
        context_file = self.frontend_dir / 'contexts' / 'ThemeContext.tsx'
        slice_file = self.frontend_dir / 'store' / 'slices' / 'themeSlice.ts'
        
        if not context_file.exists():
            self.issues.append("❌ ThemeContext not found")
            return False
        
        if not slice_file.exists():
            self.issues.append("❌ Theme slice not found")
            return False
        
        # Check context file
        with open(context_file, 'r', encoding='utf-8') as f:
            context_content = f.read()
        
        if 'effectiveTheme' not in context_content:
            self.issues.append("❌ ThemeContext missing effectiveTheme")
            return False
        
        # Check slice file
        with open(slice_file, 'r', encoding='utf-8') as f:
            slice_content = f.read()
        
        required_actions = ['setThemeMode', 'toggleTheme', 'updateSystemTheme']
        missing_actions = []
        
        for action in required_actions:
            if action not in slice_content:
                missing_actions.append(action)
        
        if missing_actions:
            self.issues.append(f"❌ Missing theme actions: {', '.join(missing_actions)}")
            return False
        
        self.successes.append("✅ Theme context and Redux integration working")
        return True
    
    def check_component_consistency(self) -> bool:
        """Check if components use consistent styling"""
        components_dir = self.frontend_dir / 'components'
        
        if not components_dir.exists():
            self.issues.append("❌ Components directory not found")
            return False
        
        # Find components that might still have hardcoded colors
        problematic_files = []
        tsx_files = list(components_dir.rglob('*.tsx'))
        
        hardcoded_patterns = [
            'bg-blue-', 'bg-green-', 'bg-red-', 'bg-yellow-', 'bg-purple-',
            'text-blue-', 'text-green-', 'text-red-', 'text-yellow-', 'text-purple-',
            'border-blue-', 'border-green-', 'border-red-', 'border-yellow-'
        ]
        
        for file_path in tsx_files[:20]:  # Check first 20 files
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in hardcoded_patterns:
                    if pattern in content:
                        problematic_files.append(file_path.name)
                        break
                        
            except Exception:
                continue
        
        if problematic_files:
            self.issues.append(f"⚠️ Components with hardcoded colors: {len(problematic_files)} files")
        else:
            self.successes.append("✅ No hardcoded colors found in sampled components")
        
        return len(problematic_files) == 0
    
    def validate_glassmorphism_consistency(self) -> bool:
        """Validate glassmorphism implementation consistency"""
        css_file = self.frontend_dir / 'index.css'
        
        if not css_file.exists():
            return False
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for glassmorphism classes
        glass_classes = ['.glass', '.glass-light', '.glass-heavy']
        missing_classes = []
        
        for glass_class in glass_classes:
            if glass_class not in content:
                missing_classes.append(glass_class)
        
        if missing_classes:
            self.issues.append(f"❌ Missing glassmorphism classes: {', '.join(missing_classes)}")
            return False
        
        self.successes.append("✅ Glassmorphism classes properly defined")
        return True
    
    def run_validation(self) -> Dict:
        """Run complete theme validation"""
        print("🎨 HMS THEME VALIDATION")
        print("=" * 50)
        
        validations = [
            ("CSS Variables", self.validate_css_variables),
            ("Unified Style System", self.validate_unified_style_system),
            ("Tailwind Config", self.validate_tailwind_config),
            ("Theme Context", self.validate_theme_context),
            ("Component Consistency", self.check_component_consistency),
            ("Glassmorphism", self.validate_glassmorphism_consistency),
        ]
        
        results = {}
        passed = 0
        total = len(validations)
        
        for name, validation_func in validations:
            print(f"\n🔍 Validating {name}...")
            try:
                result = validation_func()
                results[name] = result
                if result:
                    passed += 1
            except Exception as e:
                results[name] = False
                self.issues.append(f"❌ {name} validation failed: {e}")
        
        # Generate report
        print(f"\n📋 VALIDATION REPORT")
        print("=" * 50)
        
        print(f"✅ Passed: {passed}/{total}")
        print(f"❌ Failed: {total - passed}/{total}")
        
        if self.successes:
            print(f"\n✅ SUCCESSES ({len(self.successes)})")
            print("-" * 30)
            for success in self.successes:
                print(f"  {success}")
        
        if self.issues:
            print(f"\n❌ ISSUES ({len(self.issues)})")
            print("-" * 30)
            for issue in self.issues:
                print(f"  {issue}")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT")
        print("-" * 30)
        
        if passed == total:
            print("🎉 EXCELLENT! Theme system is fully consistent")
        elif passed >= total * 0.8:
            print("✅ GOOD! Theme system is mostly consistent with minor issues")
        elif passed >= total * 0.6:
            print("⚠️ FAIR! Theme system needs some improvements")
        else:
            print("❌ POOR! Theme system needs significant work")
        
        return {
            'passed': passed,
            'total': total,
            'success_rate': passed / total,
            'successes': self.successes,
            'issues': self.issues,
            'results': results
        }

def main():
    """Main entry point"""
    validator = ThemeValidator()
    results = validator.run_validation()
    
    # Save results to file
    with open('theme_validation_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Results saved to theme_validation_results.json")

if __name__ == '__main__':
    main()
