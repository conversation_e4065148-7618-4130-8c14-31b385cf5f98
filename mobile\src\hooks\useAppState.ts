import { useEffect, useRef } from 'react'; import { AppState, AppStateStatus } from 'react-native'; /** * Hook to handle app state changes (foreground/background) */ export function useAppState( onForeground?: () => void, onBackground?: () => void, onChange?: (state: AppStateStatus) => void ) { const appState = useRef(AppState.currentState); useEffect(() => { const handleAppStateChange = (nextAppState: AppStateStatus) => { if (onChange) { onChange(nextAppState); } if ( appState.current.match(/inactive|background/) && nextAppState === 'active' ) { // App has come to the foreground if (onForeground) { onForeground(); } } else if ( appState.current === 'active' && nextAppState.match(/inactive|background/) ) { // App has gone to the background if (onBackground) { onBackground(); } } appState.current = nextAppState; }; const subscription = AppState.addEventListener('change', handleAppStateChange); return () => subscription?.remove(); }, [onForeground, onBackground, onChange]); return appState.current; } /** * Hook to refresh data when app comes to foreground */ export function useRefreshOnFocus(refreshFunction: () => void | Promise<void>) { useAppState( () => { // Refresh when app comes to foreground refreshFunction(); } ); } 