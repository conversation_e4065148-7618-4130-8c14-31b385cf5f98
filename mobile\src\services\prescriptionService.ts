import { apiService } from './api'; import { PaginatedResponse } from '../types/api'; export interface Prescription { id: number; medication_name: string; dosage: string; frequency: string; duration: string; instructions: string; prescribed_date: string; start_date: string; end_date: string; doctor_name: string; status: 'active' | 'completed' | 'discontinued'; refills_remaining: number; total_refills: number; pharmacy_name?: string; side_effects?: string[]; notes?: string; created_at: string; updated_at: string; } export interface PrescriptionDetail extends Prescription { patient_id: string; doctor_id: number; pharmacy_id?: number; medication_id: number; drug_interactions: string[]; contraindications: string[]; monitoring_requirements: string[]; cost_estimate?: number; insurance_covered: boolean; } export interface RefillRequest { prescription_id: number; pharmacy_id?: number; pickup_date?: string; notes?: string; } class PrescriptionService { async getMyPrescriptions(page = 1, pageSize = 20): Promise<PaginatedResponse<Prescription>> { return apiService.getPaginated<Prescription>(`/prescriptions/my-prescriptions/?page=${page}&page_size=${pageSize}`); } async getPrescriptionById(id: number): Promise<PrescriptionDetail> { return apiService.get<PrescriptionDetail>(`/prescriptions/prescriptions/${id}/`); } async getActivePrescriptions(): Promise<Prescription[]> { return apiService.get<Prescription[]>(`/prescriptions/my-prescriptions/active/`); } async getPrescriptionHistory(): Promise<Prescription[]> { return apiService.get<Prescription[]>(`/prescriptions/my-prescriptions/history/`); } async requestRefill(refillData: RefillRequest): Promise<{ message: string; refill_id: number }> { return apiService.post<{ message: string; refill_id: number }>(`/prescriptions/refill-requests/`, refillData); } async getRefillRequests(): Promise<any[]> { return apiService.get<any[]>(`/prescriptions/my-refill-requests/`); } async cancelRefillRequest(refillId: number): Promise<{ message: string }> { return apiService.delete<{ message: string }>(`/prescriptions/refill-requests/${refillId}/`); } async reportSideEffect(prescriptionId: number, sideEffect: string, severity: string): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/prescriptions/prescriptions/${prescriptionId}/side-effects/`, { side_effect: sideEffect, severity }); } async setMedicationReminder(prescriptionId: number, reminderTimes: string[]): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/prescriptions/prescriptions/${prescriptionId}/reminders/`, { reminder_times: reminderTimes }); } async downloadPrescription(id: number): Promise<Blob> { return apiService.get<Blob>(`/prescriptions/prescriptions/${id}/download/`, { responseType: 'blob' }); } async checkDrugInteractions(medicationIds: number[]): Promise<any[]> { return apiService.post<any[]>(`/prescriptions/check-interactions/`, { medication_ids: medicationIds }); } } export const prescriptionService = new PrescriptionService(); 