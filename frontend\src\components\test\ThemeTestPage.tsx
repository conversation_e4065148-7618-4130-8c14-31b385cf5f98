import React from 'react';
import { useTheme } from '../../hooks/useTheme';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

const ThemeTestPage: React.FC = () => {
  const { mode, effectiveTheme, toggleTheme } = useTheme();

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">
            🎨 HMS Theme System Test
          </h1>
          <p className="text-lg text-muted-foreground">
            Testing our unified styling system and dark/light mode functionality
          </p>
          <div className="flex items-center justify-center gap-4">
            <span className="text-sm text-muted-foreground">
              Current mode: <strong>{mode}</strong> | Effective theme: <strong>{effectiveTheme}</strong>
            </span>
            <Button onClick={toggleTheme} variant="outline">
              Toggle Theme
            </Button>
          </div>
        </div>

        {/* Status Colors Test */}
        <Card className="glass">
          <CardHeader>
            <CardTitle>Status Colors</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="status-success p-4 rounded-lg text-center">
                <div className="font-medium">Success</div>
                <div className="text-sm">status-success</div>
              </div>
              <div className="status-warning p-4 rounded-lg text-center">
                <div className="font-medium">Warning</div>
                <div className="text-sm">status-warning</div>
              </div>
              <div className="status-error p-4 rounded-lg text-center">
                <div className="font-medium">Error</div>
                <div className="text-sm">status-error</div>
              </div>
              <div className="status-info p-4 rounded-lg text-center">
                <div className="font-medium">Info</div>
                <div className="text-sm">status-info</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Glassmorphism Test */}
        <Card className="glass">
          <CardHeader>
            <CardTitle>Glassmorphism Effects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="glass-light p-6 rounded-lg text-center">
                <h3 className="font-medium text-foreground">Light Glass</h3>
                <p className="text-sm text-muted-foreground">glass-light</p>
              </div>
              <div className="glass p-6 rounded-lg text-center">
                <h3 className="font-medium text-foreground">Medium Glass</h3>
                <p className="text-sm text-muted-foreground">glass</p>
              </div>
              <div className="glass-heavy p-6 rounded-lg text-center">
                <h3 className="font-medium text-foreground">Heavy Glass</h3>
                <p className="text-sm text-muted-foreground">glass-heavy</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Button Variants */}
        <Card className="glass">
          <CardHeader>
            <CardTitle>Button Variants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button variant="default">Default</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
          </CardContent>
        </Card>

        {/* Text Styles */}
        <Card className="glass">
          <CardHeader>
            <CardTitle>Text Styles</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h1 className="text-4xl font-bold text-foreground">Heading 1</h1>
                <h2 className="text-3xl font-semibold text-foreground">Heading 2</h2>
                <h3 className="text-2xl font-semibold text-foreground">Heading 3</h3>
              </div>
              <div>
                <p className="text-lg text-foreground">Large body text</p>
                <p className="text-base text-foreground">Default body text</p>
                <p className="text-sm text-foreground">Small body text</p>
              </div>
              <div>
                <p className="text-lg text-muted-foreground">Large muted text</p>
                <p className="text-base text-muted-foreground">Default muted text</p>
                <p className="text-sm text-muted-foreground">Small muted text</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Color System Test */}
        <Card className="glass">
          <CardHeader>
            <CardTitle>Color System</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-primary text-primary-foreground p-4 rounded-lg text-center">
                <div className="font-medium">Primary</div>
              </div>
              <div className="bg-secondary text-secondary-foreground p-4 rounded-lg text-center">
                <div className="font-medium">Secondary</div>
              </div>
              <div className="bg-accent text-accent-foreground p-4 rounded-lg text-center">
                <div className="font-medium">Accent</div>
              </div>
              <div className="bg-muted text-muted-foreground p-4 rounded-lg text-center">
                <div className="font-medium">Muted</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dark Mode Indicator */}
        <div className="text-center p-4 glass rounded-lg">
          <p className="text-muted-foreground">
            {effectiveTheme === 'dark' ? '🌙 Dark Mode Active' : '☀️ Light Mode Active'}
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            All colors and effects should adapt automatically
          </p>
        </div>
      </div>
    </div>
  );
};

export default ThemeTestPage;
