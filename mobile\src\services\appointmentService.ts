import { apiService } from './api'; import { userService } from './userService'; import { Appointment, AppointmentSlot, CreateAppointmentData } from '../types/appointment'; import { PaginatedResponse } from '../types/api'; class AppointmentService { async getAppointments(page = 1, pageSize = 20): Promise<PaginatedResponse<Appointment>> { return apiService.getPaginated<Appointment>(`/appointments/appointments/?page=${page}&page_size=${pageSize}`); } async getAppointment(id: number): Promise<Appointment> { return apiService.get<Appointment>(`/appointments/appointments/${id}/`); } async createAppointment(data: CreateAppointmentData): Promise<Appointment> { // Ensure the appointment is created for the current patient const currentPatient = await userService.getCurrentPatient(); const appointmentData = { ...data, patient: currentPatient.id }; return apiService.post<Appointment>('/appointments/appointments/', appointmentData); } async updateAppointment(id: number, data: Partial<Appointment>): Promise<Appointment> { return apiService.patch<Appointment>(`/appointments/appointments/${id}/`, data); } async cancelAppointment(id: number): Promise<Appointment> { return apiService.patch<Appointment>(`/appointments/appointments/${id}/`, { status: 'cancelled' }); } async deleteAppointment(id: number): Promise<void> { return apiService.delete(`/appointments/appointments/${id}/`); } // Patient-specific methods async getMyAppointments(page = 1, pageSize = 20): Promise<PaginatedResponse<Appointment>> { const currentPatient = await userService.getCurrentPatient(); return apiService.getPaginated<Appointment>(`/appointments/appointments/?patient=${currentPatient.id}&page=${page}&page_size=${pageSize}`); } async getUpcomingAppointments(page = 1, pageSize = 10): Promise<PaginatedResponse<Appointment>> { const currentPatient = await userService.getCurrentPatient(); const today = new Date().toISOString().split('T')[0]; return apiService.getPaginated<Appointment>(`/appointments/appointments/?patient=${currentPatient.id}&appointment_date__gte=${today}&status__in=scheduled,confirmed&page=${page}&page_size=${pageSize}`); } // Doctor availability async getAvailableSlots(doctorId: number, date: string): Promise<AppointmentSlot[]> { return apiService.get<AppointmentSlot[]>(`/appointments/available-slots/?doctor=${doctorId}&date=${date}`); } async getDoctorSchedule(doctorId: number, startDate: string, endDate: string): Promise<AppointmentSlot[]> { return apiService.get<AppointmentSlot[]>(`/appointments/doctor-schedule/?doctor=${doctorId}&start_date=${startDate}&end_date=${endDate}`); } // Appointment statistics async getAppointmentStats(): Promise<{ total: number; scheduled: number; completed: number; cancelled: number; upcoming: number; }> { return apiService.get('/appointments/stats/'); } // Reschedule appointment async rescheduleAppointment(id: number, newDate: string, newTime: string): Promise<Appointment> { return apiService.patch<Appointment>(`/appointments/appointments/${id}/reschedule/`, { appointment_date: newDate, appointment_time: newTime }); } // Confirm appointment async confirmAppointment(id: number): Promise<Appointment> { return apiService.patch<Appointment>(`/appointments/appointments/${id}/`, { status: 'confirmed' }); } } export const appointmentService = new AppointmentService(); 