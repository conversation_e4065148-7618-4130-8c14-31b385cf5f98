#!/usr/bin/env python3
"""
Unified HMS Bug Analyzer
Consolidates bug analysis, detection, and fixing capabilities
"""

import os
import sys
import django
import json
import time
import psutil
import traceback
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add parent directory to path for Django imports
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import connection, transaction
from django.core.cache import cache
from django.conf import settings
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

class UnifiedBugAnalyzer:
    """Unified bug analysis, detection, and fixing tool"""
    
    def __init__(self):
        self.client = APIClient()
        self.issues = []
        self.warnings = []
        self.fixes_applied = []
        self.performance_metrics = {}
        self.system_health = {}
        
    def run_complete_analysis(self, bug_description: Optional[str] = None):
        """Run comprehensive bug analysis"""
        print("🔍 UNIFIED HMS BUG ANALYSIS")
        print("=" * 60)
        
        if bug_description:
            print(f"Analyzing: {bug_description}")
            print("-" * 60)
        
        # Run all analysis modules
        self._check_integration_health()
        self._analyze_database_consistency()
        self._check_frontend_issues()
        self._analyze_performance()
        self._scan_system_health()
        self._check_styling_consistency()
        
        # Generate comprehensive report
        self._generate_unified_report()
        
        return {
            'issues': self.issues,
            'warnings': self.warnings,
            'fixes_applied': self.fixes_applied,
            'system_health': self.system_health,
            'performance_metrics': self.performance_metrics
        }
    
    def _check_integration_health(self):
        """Check HMS integration standards"""
        print("🔍 Checking Integration Health...")
        
        # Check token storage consistency
        frontend_files = [
            'frontend/src/utils/api.ts',
            'frontend/src/store/slices/authSlice.ts',
            'frontend/src/services/aiService.ts'
        ]
        
        for file_path in frontend_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "localStorage.getItem('access_token')" in content:
                        self.issues.append(f"❌ Inconsistent token storage in {file_path}")
                    if 'http://localhost:' in content or 'http://127.0.0.1:' in content:
                        self.warnings.append(f"⚠️ Hardcoded URLs in {file_path}")
        
        # Check API response format consistency
        try:
            response = self.client.get('/api/auth/user/')
            if response.status_code == 200:
                data = response.json()
                if 'results' not in data and 'data' not in data:
                    self.warnings.append("⚠️ API response format inconsistency")
        except Exception as e:
            self.issues.append(f"❌ API health check failed: {e}")
    
    def _analyze_database_consistency(self):
        """Analyze database for consistency issues"""
        print("🔍 Analyzing Database Consistency...")
        
        try:
            with connection.cursor() as cursor:
                # Check for orphaned records
                cursor.execute("""
                    SELECT COUNT(*) FROM accounts_patient 
                    WHERE user_id NOT IN (SELECT id FROM auth_user)
                """)
                orphaned_patients = cursor.fetchone()[0]
                
                if orphaned_patients > 0:
                    self.issues.append(f"❌ {orphaned_patients} orphaned patient records")
                
                # Check patient ID format
                cursor.execute("""
                    SELECT COUNT(*) FROM accounts_patient 
                    WHERE patient_id NOT LIKE 'P%'
                """)
                invalid_patient_ids = cursor.fetchone()[0]
                
                if invalid_patient_ids > 0:
                    self.issues.append(f"❌ {invalid_patient_ids} invalid patient ID formats")
                    
        except Exception as e:
            self.issues.append(f"❌ Database analysis failed: {e}")
    
    def _check_frontend_issues(self):
        """Check frontend code quality"""
        print("🔍 Checking Frontend Code Quality...")
        
        frontend_dir = Path('frontend/src')
        if not frontend_dir.exists():
            self.issues.append("❌ Frontend directory not found")
            return
        
        ts_files = list(frontend_dir.rglob('*.ts')) + list(frontend_dir.rglob('*.tsx'))
        
        for file_path in ts_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Check for any types
                    if ': any' in content or 'any[]' in content:
                        self.warnings.append(f"⚠️ {file_path.name} contains 'any' types")
                    
                    # Check for console.log
                    if 'console.log(' in content:
                        self.warnings.append(f"⚠️ {file_path.name} contains console.log")
                    
                    # Check for hardcoded colors
                    hardcoded_patterns = [
                        'bg-blue-', 'bg-green-', 'bg-red-', 'bg-yellow-', 'bg-purple-',
                        'text-blue-', 'text-green-', 'text-red-', 'text-yellow-', 'text-purple-',
                        '#[0-9a-fA-F]{6}', '#[0-9a-fA-F]{3}'
                    ]
                    
                    for pattern in hardcoded_patterns:
                        if pattern in content:
                            self.warnings.append(f"⚠️ {file_path.name} contains hardcoded colors")
                            break
                            
            except Exception as e:
                self.issues.append(f"❌ Error reading {file_path}: {e}")
    
    def _analyze_performance(self):
        """Analyze system performance"""
        print("🔍 Analyzing Performance...")
        
        # Memory usage
        memory = psutil.virtual_memory()
        self.performance_metrics['memory_usage'] = memory.percent
        
        if memory.percent > 80:
            self.warnings.append(f"⚠️ High memory usage: {memory.percent}%")
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.performance_metrics['cpu_usage'] = cpu_percent
        
        if cpu_percent > 80:
            self.warnings.append(f"⚠️ High CPU usage: {cpu_percent}%")
    
    def _scan_system_health(self):
        """Scan overall system health"""
        print("🔍 Scanning System Health...")
        
        self.system_health = {
            'database_connected': self._test_database_connection(),
            'cache_working': self._test_cache_connection(),
            'api_responsive': self._test_api_health(),
            'frontend_accessible': os.path.exists('frontend/dist') or os.path.exists('frontend/build')
        }
    
    def _check_styling_consistency(self):
        """Check for styling consistency issues"""
        print("🔍 Checking Styling Consistency...")
        
        # Check for duplicate glassmorphism implementations
        style_files = [
            'frontend/src/index.css',
            'frontend/src/utils/styleConverter.ts',
            'frontend/src/shared/utils/constants.ts'
        ]
        
        glassmorphism_implementations = 0
        for file_path in style_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'glassmorphism' in content.lower() or 'backdrop-blur' in content:
                        glassmorphism_implementations += 1
        
        if glassmorphism_implementations > 1:
            self.warnings.append(f"⚠️ Multiple glassmorphism implementations found")
    
    def _test_database_connection(self) -> bool:
        """Test database connection"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            return True
        except Exception:
            return False
    
    def _test_cache_connection(self) -> bool:
        """Test cache connection"""
        try:
            cache.set('health_check', 'ok', 1)
            return cache.get('health_check') == 'ok'
        except Exception:
            return False
    
    def _test_api_health(self) -> bool:
        """Test API health"""
        try:
            response = self.client.get('/api/health/')
            return response.status_code in [200, 404]  # 404 is ok if endpoint doesn't exist
        except Exception:
            return False
    
    def _generate_unified_report(self):
        """Generate comprehensive unified report"""
        print("\n📋 UNIFIED BUG ANALYSIS REPORT")
        print("=" * 60)
        
        # Summary
        total_issues = len(self.issues)
        total_warnings = len(self.warnings)
        
        print(f"🚨 Issues Found: {total_issues}")
        print(f"⚠️ Warnings: {total_warnings}")
        print(f"🔧 Fixes Applied: {len(self.fixes_applied)}")
        
        # Issues
        if self.issues:
            print(f"\n🚨 CRITICAL ISSUES ({len(self.issues)})")
            print("-" * 40)
            for issue in self.issues:
                print(f"  {issue}")
        
        # Warnings
        if self.warnings:
            print(f"\n⚠️ WARNINGS ({len(self.warnings)})")
            print("-" * 40)
            for warning in self.warnings:
                print(f"  {warning}")
        
        # System Health
        print(f"\n💚 SYSTEM HEALTH")
        print("-" * 40)
        for component, status in self.system_health.items():
            icon = "✅" if status else "❌"
            print(f"  {icon} {component.replace('_', ' ').title()}")
        
        # Performance
        if self.performance_metrics:
            print(f"\n📊 PERFORMANCE METRICS")
            print("-" * 40)
            for metric, value in self.performance_metrics.items():
                print(f"  {metric.replace('_', ' ').title()}: {value}%")
        
        # Recommendations
        print(f"\n🔧 RECOMMENDATIONS")
        print("-" * 40)
        
        if total_issues == 0 and total_warnings == 0:
            print("  ✅ All systems healthy - ready for development!")
        else:
            if total_issues > 0:
                print("  1. Address critical issues immediately")
            if total_warnings > 0:
                print("  2. Review and fix warnings for better code quality")
            if self.performance_metrics.get('memory_usage', 0) > 70:
                print("  3. Consider memory optimization")
            if self.performance_metrics.get('cpu_usage', 0) > 70:
                print("  4. Consider CPU optimization")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Unified HMS Bug Analyzer')
    parser.add_argument('--bug', help='Specific bug description to analyze')
    parser.add_argument('--json', action='store_true', help='Output results as JSON')
    
    args = parser.parse_args()
    
    analyzer = UnifiedBugAnalyzer()
    results = analyzer.run_complete_analysis(args.bug)
    
    if args.json:
        print(json.dumps(results, indent=2, default=str))

if __name__ == '__main__':
    main()
