import React, { useEffect } from 'react'; import { createNativeStackNavigator } from '@react-navigation/native-stack'; import { useSelector, useDispatch } from 'react-redux'; import { RootState, AppDispatch } from '../store'; import { checkAuthStatus } from '../store/slices/authSlice'; import AuthNavigator from './AuthNavigator'; import MainNavigator from './MainNavigator'; import LoadingScreen from '../screens/LoadingScreen'; export type RootStackParamList = { Auth: undefined; Main: undefined; Loading: undefined; }; const Stack = createNativeStackNavigator(); const AppNavigator: React.FC = () => { const dispatch = useDispatch(); const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth); useEffect(() => { // Check authentication status on app start dispatch(checkAuthStatus()); }, [dispatch]); if (isLoading) { return ( <Stack.Navigator screenOptions={{ headerShown: false }}> <Stack.Screen name="Loading" component={LoadingScreen} /> </Stack.Navigator> ); } return ( <Stack.Navigator screenOptions={{ headerShown: false }}> {isAuthenticated ? ( <Stack.Screen name="Main" component={MainNavigator} /> ) : ( <Stack.Screen name="Auth" component={AuthNavigator} /> )} </Stack.Navigator> ); }; export default AppNavigator; 