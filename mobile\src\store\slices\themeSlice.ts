import { createSlice } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { Appearance } from 'react-native'; export type ThemeMode = 'light' | 'dark' | 'system'; export type GlassmorphismLevel = 'none' | 'subtle' | 'medium' | 'strong'; interface ThemeState { mode: ThemeMode; glassmorphismLevel: GlassmorphismLevel; reducedMotion: boolean; systemTheme: 'light' | 'dark'; effectiveTheme: 'light' | 'dark'; } const getSystemTheme = (): 'light' | 'dark' => { const colorScheme = Appearance.getColorScheme(); return colorScheme === 'dark' ? 'dark' : 'light'; }; const getEffectiveTheme = (mode: ThemeMode, systemTheme: 'light' | 'dark'): 'light' | 'dark' => { if (mode === 'system') { return systemTheme; } return mode; }; const initialState: ThemeState = { mode: 'system', glassmorphismLevel: 'medium', reducedMotion: false, systemTheme: getSystemTheme(), effectiveTheme: getEffectiveTheme('system', getSystemTheme()), }; const themeSlice = createSlice({ name: 'theme', initialState, reducers: { setThemeMode: (state, action) => { state.mode = action.payload; state.effectiveTheme = getEffectiveTheme(action.payload, state.systemTheme); }, setGlassmorphismLevel: (state, action) => { state.glassmorphismLevel = action.payload; }, setReducedMotion: (state, action) => { state.reducedMotion = action.payload; }, updateSystemTheme: (state, action) => { state.systemTheme = action.payload; state.effectiveTheme = getEffectiveTheme(state.mode, action.payload); }, toggleTheme: (state) => { if (state.mode === 'light') { state.mode = 'dark'; } else if (state.mode === 'dark') { state.mode = 'system'; } else { state.mode = 'light'; } state.effectiveTheme = getEffectiveTheme(state.mode, state.systemTheme); }, }, }); export const { setThemeMode, setGlassmorphismLevel, setReducedMotion, updateSystemTheme, toggleTheme, } = themeSlice.actions; export default themeSlice.reducer; 