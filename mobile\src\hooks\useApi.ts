import { useState, useEffect, useCallback } from 'react'; import { ApiError } from '../types/api'; interface UseApiState<T> { data: T | null; loading: boolean; error: string | null; } interface UseApiOptions { immediate?: boolean; onSuccess?: (data: any) => void; onError?: (error: string) => void; } /** * Custom hook for API calls with loading, error, and data state management */ // Common error message extraction utility const extractErrorMessage = (error: any): string => { if (error instanceof ApiError) { return error.message; } if (error instanceof Error) { return error.message; } return 'An unexpected error occurred'; }; export function useApi<T>( apiFunction: (...args: any[]) => Promise<T>, options: UseApiOptions = {} ) { const { immediate = false, onSuccess, onError } = options; const [state, setState] = useState<UseApiState<T>>({ data: null, loading: false, error: null, }); const execute = useCallback(async (...args: any[]) => { setState(prev => ({ ...prev, loading: true, error: null })); try { const result = await apiFunction(...args); setState({ data: result, loading: false, error: null }); if (onSuccess) { onSuccess(result); } return result; } catch (error) { const errorMessage = extractErrorMessage(error); setState({ data: null, loading: false, error: errorMessage }); if (onError) { onError(errorMessage); } throw error; } }, [apiFunction, onSuccess, onError]); const reset = useCallback(() => { setState({ data: null, loading: false, error: null }); }, []); useEffect(() => { if (immediate) { execute(); } }, [immediate, execute]); return { ...state, execute, reset, }; } /** * Hook for paginated API calls */ export function usePaginatedApi<T>( apiFunction: (page: number, pageSize: number) => Promise<{ results: T[]; count: number; next: string | null; previous: string | null }>, pageSize: number = 20 ) { const [state, setState] = useState({ data: [] as T[], loading: false, error: null as string | null, page: 1, hasMore: true, totalCount: 0, refreshing: false, }); const loadPage = useCallback(async (page: number, append: boolean = false) => { setState(prev => ({ ...prev, loading: !append, refreshing: append && page === 1, error: null })); try { const result = await apiFunction(page, pageSize); setState(prev => ({ ...prev, data: append ? [...prev.data, ...result.results] : result.results, loading: false, refreshing: false, page, hasMore: !!result.next, totalCount: result.count, error: null, })); return result; } catch (error) { const errorMessage = extractErrorMessage(error); setState(prev => ({ ...prev, loading: false, refreshing: false, error: errorMessage, })); throw error; } }, [apiFunction, pageSize]); const loadMore = useCallback(() => { if (!state.loading && state.hasMore) { return loadPage(state.page + 1, true); } }, [loadPage, state.loading, state.hasMore, state.page]); const refresh = useCallback(() => { return loadPage(1, false); }, [loadPage]); const reset = useCallback(() => { setState({ data: [], loading: false, error: null, page: 1, hasMore: true, totalCount: 0, refreshing: false, }); }, []); useEffect(() => { loadPage(1); }, [loadPage]); return { ...state, loadMore, refresh, reset, }; } /** * Hook for form submission with API calls */ export function useFormSubmit<T, U>( submitFunction: (data: T) => Promise<U>, options: UseApiOptions = {} ) { const [state, setState] = useState({ loading: false, error: null as string | null, success: false, }); const submit = useCallback(async (data: T) => { setState({ loading: true, error: null, success: false }); try { const result = await submitFunction(data); setState({ loading: false, error: null, success: true }); if (options.onSuccess) { options.onSuccess(result); } return result; } catch (error) { const errorMessage = extractErrorMessage(error); setState({ loading: false, error: errorMessage, success: false }); if (options.onError) { options.onError(errorMessage); } throw error; } }, [submitFunction, options]); const reset = useCallback(() => { setState({ loading: false, error: null, success: false }); }, []); return { ...state, submit, reset, }; } 