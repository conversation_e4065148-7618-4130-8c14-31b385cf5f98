/** * NurseDashboard Component * Uses shared components for consistent layout and functionality */ import React from 'react'; import { useTranslation } from 'react-i18next'; import { Users, Heart, Clock, Pill, AlertCircle, Thermometer, UserCheck, ClipboardList, Stethoscope } from 'lucide-react'; // Shared components import { DashboardSection, QuickActions } from '../../shared/components/layouts/DashboardLayout'; import { Badge } from '../ui/badge'; import { MetricGrid } from '../../shared/components/data-display/MetricCard'; const NurseDashboard: React.FC = () => { const { t } = useTranslation(); // Nurse-specific metrics const nurseMetrics = [ { id: 'patients-assigned', label: t('nurse.patientsAssigned'), value: '8', icon: Users, color: 'feature-blue', description: 'Currently assigned', }, { id: 'medications-due', label: t('nurse.medicationsDue'), value: '12', icon: Pill, color: 'feature-orange', change: '+3', trend: 'up' as const, description: 'Next 2 hours', }, { id: 'vitals-pending', label: t('nurse.vitalsPending'), value: '5', icon: Thermometer, color: 'feature-purple', description: 'Awaiting collection', }, { id: 'shift-progress', label: t('nurse.shiftHours'), value: '6/12', icon: Clock, color: 'feature-green', description: 'Hours completed', }, ]; // Quick actions for nurses const quickActions = [ { id: 'patient-rounds', title: 'Patient Rounds', description: 'Check on assigned patients', icon: UserCheck, onClick: () => console.log('Patient rounds'), }, { id: 'administer-meds', title: 'Administer Meds', description: 'Medication administration', icon: Pill, onClick: () => console.log('Administer medications'), }, { id: 'record-vitals', title: 'Record Vitals', description: 'Take vital signs', icon: Thermometer, onClick: () => console.log('Record vitals'), }, { id: 'update-notes', title: 'Update Notes', description: 'Update patient notes', icon: ClipboardList, onClick: () => console.log('Update notes'), }, ]; // Patient alerts data const patientAlerts = [ { id: 1, patient: 'Room 101 - Sarah Johnson', alert: 'High blood pressure reading', priority: 'high', time: '15 min ago' }, { id: 2, patient: 'Room 203 - Michael Brown', alert: 'Medication due in 30 minutes', priority: 'medium', time: '25 min ago' }, { id: 3, patient: 'Room 105 - Emily Davis', alert: 'Requested assistance', priority: 'low', time: '45 min ago' } ]; // Patient assignments const patientAssignments = [ { room: '101', patient: 'Sarah Johnson', condition: 'Post-surgery', status: 'stable' }, { room: '102', patient: 'Michael Brown', condition: 'Pneumonia', status: 'improving' }, { room: '103', patient: 'Emily Davis', condition: 'Diabetes', status: 'stable' }, { room: '104', patient: 'Robert Wilson', condition: 'Heart condition', status: 'monitoring' } ]; const getPriorityColor = (priority: string) => { switch (priority) { case 'high': return 'status-error '; case 'medium': return 'status-warning '; default: return 'status-info '; } }; const getStatusColor = (status: string) => { switch (status) { case 'stable': return 'status-success '; case 'improving': return 'status-info '; case 'monitoring': return 'status-warning '; default: return 'bg-muted text-foreground dark:bg-gray-900/20 dark:text-gray-400'; } }; return ( <div className="space-y-6"> {/* Page Header */} <div className="flex items-center justify-between mb-8"> <div className="flex items-center gap-4"> <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center shadow-lg"> <Stethoscope className="w-6 h-6 text-white" /> </div> <div> <h1 className="text-2xl font-bold text-foreground dark:text-white"> {t('nurse.title')} </h1> <p className="text-muted-foreground dark:text-gray-300"> {t('nurse.welcome')} </p> <div className="flex items-center gap-2 mt-1"> <Badge variant="outline" className="flex items-center gap-1"> <Heart className="w-3 h-3" /> {t('nurse.onDuty')} </Badge> </div> </div> </div> </div> {/* Nurse Statistics */} <DashboardSection title="Shift Overview"> <MetricGrid metrics={nurseMetrics} columns={4} /> </DashboardSection> {/* Quick Actions */} <DashboardSection title={t('dashboard.quickActions')} description={t('nurse.quickActionsDescription')} variant="glass" > <QuickActions actions={quickActions} columns={4} /> </DashboardSection> {/* Patient Alerts and Shift Info */} <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Patient Alerts */} <DashboardSection title={t('nurse.patientAlerts')} variant="glass" > <div className="space-y-4"> {patientAlerts.map((alert) => ( <div key={alert.id} className="flex items-center justify-between p-4 glass rounded-lg hover:glass-hover transition-all duration-200"> <div className="flex-1"> <div className="flex items-center justify-between mb-2"> <p className="font-medium macos-text-primary"> {alert.patient} </p> <Badge className={getPriorityColor(alert.priority)}> {alert.priority} </Badge> </div> <p className="text-sm macos-text-secondary"> {alert.alert} </p> <p className="text-xs macos-text-tertiary mt-1"> {alert.time} </p> </div> </div> ))} </div> </DashboardSection> {/* Shift Information */} <DashboardSection title="Shift Information" variant="glass" > <div className="space-y-4"> <div className="flex justify-between items-center p-3 glass rounded-lg"> <span className="text-sm font-medium macos-text-primary">Shift Start</span> <span className="text-sm macos-text-secondary">7:00 AM</span> </div> <div className="flex justify-between items-center p-3 glass rounded-lg"> <span className="text-sm font-medium macos-text-primary">Shift End</span> <span className="text-sm macos-text-secondary">7:00 PM</span> </div> <div className="flex justify-between items-center p-3 glass rounded-lg"> <span className="text-sm font-medium macos-text-primary">Break Time</span> <span className="text-sm macos-text-secondary">1:00 PM - 2:00 PM</span> </div> <div className="flex justify-between items-center p-3 glass rounded-lg"> <span className="text-sm font-medium macos-text-primary">Status</span> <Badge className="status-success "> Active </Badge> </div> </div> </DashboardSection> </div> {/* Patient Assignment */} <DashboardSection title="Patient Assignment" variant="glass" > <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {patientAssignments.map((assignment, index) => ( <div key={index} className="p-4 glass rounded-lg hover:glass-hover transition-all duration-200"> <div className="flex justify-between items-start"> <div> <p className="font-medium macos-text-primary">Room {assignment.room}</p> <p className="text-sm macos-text-secondary">{assignment.patient}</p> <p className="text-xs macos-text-tertiary">{assignment.condition}</p> </div> <Badge className={getStatusColor(assignment.status)}> {assignment.status} </Badge> </div> </div> ))} </div> </DashboardSection> </div> ); }; export default NurseDashboard; 