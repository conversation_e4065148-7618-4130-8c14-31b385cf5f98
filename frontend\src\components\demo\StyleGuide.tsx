import React from 'react'; import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card'; import { Button } from '../ui/button'; import { Badge } from '../ui/badge'; import { getStatusColorClass, getAccentBackgroundClass, getTextClasses } from '../../utils/styleConverter'; const StyleGuide: React.FC = () => { const textClasses = getTextClasses(); return ( <div className="space-y-8 p-6"> {/* Header */} <div className="text-center space-y-4"> <h1 className="text-4xl font-bold text-foreground"> 🎨 HMS Unified Style Guide - Fixed! </h1> <p className="text-lg text-muted-foreground max-w-2xl mx-auto"> This guide shows our fixed unified theme system with proper CSS variables and no duplicate styles. </p> </div> {/* CSS Variables Test */} <Card> <CardHeader> <CardTitle>CSS Variables Test</CardTitle> </CardHeader> <CardContent> <div className="grid grid-cols-2 md:grid-cols-4 gap-4"> <div className="p-4 rounded-lg bg-background border border-border"> <div className="text-sm font-medium text-foreground">Background</div> <div className="text-xs text-muted-foreground">bg-background</div> </div> <div className="p-4 rounded-lg bg-card border border-border"> <div className="text-sm font-medium text-card-foreground">Card</div> <div className="text-xs text-muted-foreground">bg-card</div> </div> <div className="p-4 rounded-lg bg-primary border border-border"> <div className="text-sm font-medium text-primary-foreground">Primary</div> <div className="text-xs text-primary-foreground/70">bg-primary</div> </div> <div className="p-4 rounded-lg bg-secondary border border-border"> <div className="text-sm font-medium text-secondary-foreground">Secondary</div> <div className="text-xs text-secondary-foreground/70">bg-secondary</div> </div> <div className="p-4 rounded-lg bg-muted border border-border"> <div className="text-sm font-medium text-muted-foreground">Muted</div> <div className="text-xs text-muted-foreground/70">bg-muted</div> </div> <div className="p-4 rounded-lg bg-accent border border-border"> <div className="text-sm font-medium text-accent-foreground">Accent</div> <div className="text-xs text-accent-foreground/70">bg-accent</div> </div> <div className="p-4 rounded-lg bg-destructive border border-border"> <div className="text-sm font-medium text-destructive-foreground">Destructive</div> <div className="text-xs text-destructive-foreground/70">bg-destructive</div> </div> <div className="p-4 rounded-lg border-2 border-ring"> <div className="text-sm font-medium text-foreground">Ring Color</div> <div className="text-xs text-muted-foreground">border-ring</div> </div> </div> </CardContent> </Card> {/* Color System */} <Card> <CardHeader> <CardTitle>Color System</CardTitle> </CardHeader> <CardContent className="space-y-6"> {/* Background Colors */} <div> <h3 className="text-lg font-semibold text-foreground mb-3">Background Colors</h3> <div className="grid grid-cols-2 md:grid-cols-4 gap-4"> <div className="space-y-2"> <div className="w-full h-16 bg-background border border-border rounded-lg"></div> <p className="text-sm font-medium text-foreground">bg-background</p> <p className="text-xs text-muted-foreground">Main background</p> </div> <div className="space-y-2"> <div className="w-full h-16 bg-card border border-border rounded-lg"></div> <p className="text-sm font-medium text-foreground">bg-card</p> <p className="text-xs text-muted-foreground">Card backgrounds</p> </div> <div className="space-y-2"> <div className="w-full h-16 bg-accent border border-border rounded-lg"></div> <p className="text-sm font-medium text-foreground">bg-accent</p> <p className="text-xs text-muted-foreground">List items, subtle highlights</p> </div> <div className="space-y-2"> <div className="w-full h-16 bg-muted border border-border rounded-lg"></div> <p className="text-sm font-medium text-foreground">bg-muted</p> <p className="text-xs text-muted-foreground">Disabled, secondary areas</p> </div> </div> </div> {/* Text Colors */} <div> <h3 className="text-lg font-semibold text-foreground mb-3">Text Colors</h3> <div className="space-y-2"> <p className="text-foreground">text-foreground - Primary text</p> <p className="text-muted-foreground">text-muted-foreground - Secondary text</p> <p className="text-primary">text-primary - Accent text</p> <p className="text-destructive">text-destructive - Error text</p> </div> </div> </CardContent> </Card> {/* Status Colors */} <Card> <CardHeader> <CardTitle>Status Colors</CardTitle> </CardHeader> <CardContent> <div className="grid grid-cols-2 md:grid-cols-4 gap-4"> <Badge className={getStatusColorClass('success')}>Success</Badge> <Badge className={getStatusColorClass('warning')}>Warning</Badge> <Badge className={getStatusColorClass('error')}>Error</Badge> <Badge className={getStatusColorClass('info')}>Info</Badge> </div> </CardContent> </Card> {/* Correct List Item Pattern */} <Card> <CardHeader> <CardTitle>✅ Correct List Item Pattern</CardTitle> </CardHeader> <CardContent> <div className="space-y-3"> <div className={`flex items-center justify-between p-3 ${getAccentBackgroundClass()} rounded-lg`}> <div className="flex-1"> <div className="flex items-center justify-between"> <p className="text-sm font-medium text-foreground"> Primary Information </p> <Badge className={getStatusColorClass('active')}> Active </Badge> </div> <p className="text-sm text-muted-foreground"> Secondary information </p> <p className="text-xs text-muted-foreground"> Tertiary details </p> </div> </div> </div> </CardContent> </Card> {/* Wrong vs Right Examples */} <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Wrong Example */} <Card className="border-rose-200/50 dark:border-rose-800/50"> <CardHeader> <CardTitle className="text-rose-700 dark:text-rose-400">❌ Wrong - Hardcoded Colors</CardTitle> </CardHeader> <CardContent> <div className="space-y-3"> <div className="flex items-center justify-between p-3 bg-muted rounded-lg"> <div className="flex-1"> <p className="text-sm font-medium text-foreground"> Hardcoded gray colors </p> <p className="text-sm text-muted-foreground"> Won't adapt to dark mode </p> </div> </div> <code className="text-xs bg-rose-50/80 dark:bg-rose-950/20 p-2 rounded block"> className="bg-muted text-foreground" </code> </div> </CardContent> </Card> {/* Right Example */} <Card className="border-emerald-200/50 dark:border-emerald-800/50"> <CardHeader> <CardTitle className="text-emerald-700 dark:text-emerald-400">✅ Right - Theme Variables</CardTitle> </CardHeader> <CardContent> <div className="space-y-3"> <div className="flex items-center justify-between p-3 bg-accent rounded-lg"> <div className="flex-1"> <p className="text-sm font-medium text-foreground"> Theme-aware colors </p> <p className="text-sm text-muted-foreground"> Adapts to dark/light mode </p> </div> </div> <code className="text-xs bg-emerald-50/80 dark:bg-emerald-950/20 p-2 rounded block"> className="bg-accent text-foreground" </code> </div> </CardContent> </Card> </div> {/* Component Examples */} <Card> <CardHeader> <CardTitle>Component Examples</CardTitle> </CardHeader> <CardContent className="space-y-6"> {/* Buttons */} <div> <h3 className="text-lg font-semibold text-foreground mb-3">Buttons</h3> <div className="flex flex-wrap gap-3"> <Button variant="default">Primary</Button> <Button variant="secondary">Secondary</Button> <Button variant="outline">Outline</Button> <Button variant="ghost">Ghost</Button> <Button variant="destructive">Destructive</Button> </div> </div> {/* Cards */} <div> <h3 className="text-lg font-semibold text-foreground mb-3">Cards</h3> <div className="grid grid-cols-1 md:grid-cols-3 gap-4"> <Card variant="default"> <CardContent className="p-4"> <p className="text-foreground">Default Card</p> </CardContent> </Card> <Card variant="glass"> <CardContent className="p-4"> <p className="text-foreground">Glass Card</p> </CardContent> </Card> <Card variant="elevated"> <CardContent className="p-4"> <p className="text-foreground">Elevated Card</p> </CardContent> </Card> </div> </div> </CardContent> </Card> {/* Usage Guidelines */} <Card> <CardHeader> <CardTitle>Usage Guidelines</CardTitle> </CardHeader> <CardContent> <div className="space-y-4"> <div> <h4 className="font-semibold text-foreground">✅ Do:</h4> <ul className="list-disc list-inside text-muted-foreground space-y-1 mt-2"> <li>Use theme variables: bg-background, text-foreground, border-border</li> <li>Use bg-accent for list items and subtle highlights</li> <li>Use text-muted-foreground for secondary text</li> <li>Use the getStatusColorClass() utility for status badges</li> <li>Test in both light and dark modes</li> </ul> </div> <div> <h4 className="font-semibold text-foreground">❌ Don't:</h4> <ul className="list-disc list-inside text-muted-foreground space-y-1 mt-2"> <li>Use hardcoded colors: bg-muted, text-foreground</li> <li>Use dark: prefixes (theme handles this automatically)</li> <li>Mix hardcoded and theme colors in the same component</li> <li>Forget to test dark mode appearance</li> </ul> </div> </div> </CardContent> </Card> </div> ); }; export default StyleGuide; 