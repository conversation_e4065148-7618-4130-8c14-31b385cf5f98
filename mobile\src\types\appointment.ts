export interface Appointment { id: number; appointment_id: string; patient: number; patient_name: string; doctor: number; doctor_name: string; appointment_date: string; appointment_time: string; duration: number; appointment_type: 'consultation' | 'follow_up' | 'emergency' | 'routine_checkup'; status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'; reason: string; notes?: string; created_at: string; updated_at: string; } export interface AppointmentSlot { date: string; time: string; available: boolean; doctor_id: number; doctor_name: string; } export interface AppointmentState { appointments: Appointment[]; availableSlots: AppointmentSlot[]; currentAppointment: Appointment | null; isLoading: boolean; error: string | null; } export interface CreateAppointmentData { patient: number; doctor: number; appointment_date: string; appointment_time: string; appointment_type: 'consultation' | 'follow_up' | 'emergency' | 'routine_checkup'; reason: string; notes?: string; } 