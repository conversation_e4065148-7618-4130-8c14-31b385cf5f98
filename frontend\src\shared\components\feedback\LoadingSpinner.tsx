/** * LoadingSpinner Component * Reusable loading component with multiple variants and consistent styling */ import React from 'react'; import { Loader2, RefreshCw } from 'lucide-react'; import { cn } from '../../../lib/utils'; import type { LoadingProps } from '../../types/api'; interface LoadingSpinnerProps extends LoadingProps { text?: string; overlay?: boolean; fullScreen?: boolean; color?: 'primary' | 'secondary' | 'accent' | 'muted'; speed?: 'slow' | 'normal' | 'fast'; } const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md', variant = 'spinner', text, overlay = false, fullScreen = false, color = 'primary', speed = 'normal', className, children, 'data-testid': testId, }) => { // Get size classes const getSizeClasses = () => { switch (size) { case 'sm': return { spinner: 'w-4 h-4', text: 'text-sm', container: 'gap-2', }; case 'lg': return { spinner: 'w-8 h-8', text: 'text-lg', container: 'gap-4', }; default: return { spinner: 'w-6 h-6', text: 'text-base', container: 'gap-3', }; } }; // Get color classes using theme variables const getColorClasses = () => { switch (color) { case 'secondary': return 'text-muted-foreground'; case 'accent': return 'text-primary'; case 'muted': return 'text-muted-foreground/70'; default: return 'text-primary'; } }; // Get speed classes const getSpeedClasses = () => { switch (speed) { case 'slow': return 'animate-spin [animation-duration:2s]'; case 'fast': return 'animate-spin [animation-duration:0.5s]'; default: return 'animate-spin'; } }; const sizeClasses = getSizeClasses(); const colorClasses = getColorClasses(); const speedClasses = getSpeedClasses(); // Render spinner based on variant const renderSpinner = () => { switch (variant) { case 'pulse': return ( <div className={cn( 'rounded-full bg-current animate-pulse', sizeClasses.spinner, colorClasses )} /> ); case 'skeleton': return ( <div className="space-y-3 w-full"> <div className="h-4 bg-accent dark:bg-gray-700 rounded animate-pulse"></div> <div className="h-4 bg-accent dark:bg-gray-700 rounded animate-pulse w-3/4"></div> <div className="h-4 bg-accent dark:bg-gray-700 rounded animate-pulse w-1/2"></div> </div> ); default: return ( <Loader2 className={cn( sizeClasses.spinner, speedClasses, colorClasses )} /> ); } }; // Container content const content = ( <div className={cn( 'flex items-center justify-center', sizeClasses.container, variant === 'skeleton' ? 'flex-col w-full' : 'flex-row', className )} data-testid={testId} role="status" aria-label={text || 'Loading'} > {renderSpinner()} {text && variant !== 'skeleton' && ( <span className={cn(sizeClasses.text, 'macos-text-secondary')}> {text} </span> )} {children} </div> ); // Full screen overlay if (fullScreen) { return ( <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 dark:bg-gray-900/80 backdrop-blur-sm"> {content} </div> ); } // Overlay if (overlay) { return ( <div className="absolute inset-0 z-10 flex items-center justify-center bg-background/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-lg"> {content} </div> ); } return content; }; export default LoadingSpinner; // Preset spinner variants export const SmallSpinner: React.FC<Omit<LoadingSpinnerProps, 'size'>> = (props) => ( <LoadingSpinner {...props} size="sm" /> ); export const LargeSpinner: React.FC<Omit<LoadingSpinnerProps, 'size'>> = (props) => ( <LoadingSpinner {...props} size="lg" /> ); export const PulseLoader: React.FC<Omit<LoadingSpinnerProps, 'variant'>> = (props) => ( <LoadingSpinner {...props} variant="pulse" /> ); export const SkeletonLoader: React.FC<Omit<LoadingSpinnerProps, 'variant'>> = (props) => ( <LoadingSpinner {...props} variant="skeleton" /> ); // Loading overlay component interface LoadingOverlayProps { isLoading: boolean; children: React.ReactNode; text?: string; className?: string; } export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ isLoading, children, text = 'Loading...', className, }) => { return ( <div className={cn('relative', className)}> {children} {isLoading && ( <LoadingSpinner overlay text={text} variant="spinner" size="md" /> )} </div> ); }; // Loading button component interface LoadingButtonProps { isLoading: boolean; children: React.ReactNode; loadingText?: string; disabled?: boolean; onClick?: () => void; className?: string; variant?: 'default' | 'glass' | 'outline'; } export const LoadingButton: React.FC<LoadingButtonProps> = ({ isLoading, children, loadingText = 'Loading...', disabled, onClick, className, variant = 'default', }) => { return ( <button onClick={onClick} disabled={disabled || isLoading} className={cn( 'inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors', 'disabled:opacity-50 disabled:cursor-not-allowed', variant === 'glass' && 'glass hover:glass-hover', variant === 'outline' && 'border border-border hover:bg-muted dark:border-gray-600 dark:hover:bg-gray-800', variant === 'default' && 'bg-blue-600 text-white hover:bg-blue-700', className )} > {isLoading && ( <Loader2 className="w-4 h-4 animate-spin" /> )} {isLoading ? loadingText : children} </button> ); }; // Loading card component interface LoadingCardProps { isLoading: boolean; children: React.ReactNode; lines?: number; className?: string; } export const LoadingCard: React.FC<LoadingCardProps> = ({ isLoading, children, lines = 3, className, }) => { if (isLoading) { return ( <div className={cn('p-6 glass rounded-lg', className)}> <div className="animate-pulse space-y-4"> <div className="h-4 bg-accent dark:bg-gray-700 rounded w-1/4"></div> {[...Array(lines)].map((_, i) => ( <div key={i} className={cn( 'h-4 bg-accent dark:bg-gray-700 rounded', i === lines - 1 && 'w-3/4', i === lines - 2 && 'w-5/6' )} ></div> ))} </div> </div> ); } return <>{children}</>; }; // Loading dots component export const LoadingDots: React.FC<{ size?: 'sm' | 'md' | 'lg'; color?: string; className?: string; }> = ({ size = 'md', color = 'bg-blue-600', className, }) => { const dotSize = { sm: 'w-1 h-1', md: 'w-2 h-2', lg: 'w-3 h-3', }[size]; return ( <div className={cn('flex space-x-1', className)}> {[0, 1, 2].map((i) => ( <div key={i} className={cn( 'rounded-full animate-pulse', dotSize, color )} style={{ animationDelay: `${i * 0.2}s`, animationDuration: '1s', }} /> ))} </div> ); }; // Progress bar component interface ProgressBarProps { progress: number; text?: string; showPercentage?: boolean; className?: string; color?: string; } export const ProgressBar: React.FC<ProgressBarProps> = ({ progress, text, showPercentage = true, className, color = 'bg-blue-600', }) => { const clampedProgress = Math.min(Math.max(progress, 0), 100); return ( <div className={cn('w-full', className)}> {(text || showPercentage) && ( <div className="flex justify-between items-center mb-2"> {text && <span className="text-sm macos-text-secondary">{text}</span>} {showPercentage && ( <span className="text-sm macos-text-secondary">{clampedProgress}%</span> )} </div> )} <div className="w-full bg-accent dark:bg-gray-700 rounded-full h-2"> <div className={cn('h-2 rounded-full transition-all duration-300', color)} style={{ width: `${clampedProgress}%` }} /> </div> </div> ); }; 