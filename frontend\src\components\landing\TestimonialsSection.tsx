import React from 'react'; import { useTranslation } from 'react-i18next'; import { Star, Quote } from 'lucide-react'; const TestimonialsSection: React.FC = () => { const { t } = useTranslation(); const testimonials = [ { name: t('testimonials.testimonial1.name'), role: t('testimonials.testimonial1.role'), hospital: t('testimonials.testimonial1.hospital'), content: t('testimonials.testimonial1.content'), rating: 5, image: "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face" }, { name: t('testimonials.testimonial2.name'), role: t('testimonials.testimonial2.role'), hospital: t('testimonials.testimonial2.hospital'), content: t('testimonials.testimonial2.content'), rating: 5, image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face" }, { name: t('testimonials.testimonial3.name'), role: t('testimonials.testimonial3.role'), hospital: t('testimonials.testimonial3.hospital'), content: t('testimonials.testimonial3.content'), rating: 5, image: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face" } ]; const stats = [ { number: "500+", label: t('testimonials.stats.hospitals'), description: t('testimonials.stats.hospitalsDesc') }, { number: "1M+", label: t('testimonials.stats.patients'), description: t('testimonials.stats.patientsDesc') }, { number: "99.9%", label: t('testimonials.stats.uptime'), description: t('testimonials.stats.uptimeDesc') }, { number: "24/7", label: t('testimonials.stats.support'), description: t('testimonials.stats.supportDesc') } ]; return ( <section className="py-20 bg-background"> <div className="container mx-auto px-6"> {/* Section Header */} <div className="text-center mb-16"> <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6"> {t('testimonials.title')} </h2> <p className="text-xl text-muted-foreground max-w-3xl mx-auto"> {t('testimonials.subtitle')} </p> </div> {/* Testimonials Grid */} <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20"> {testimonials.map((testimonial, index) => ( <div key={index} className="bg-muted rounded-2xl p-8 relative hover:shadow-lg transition-shadow duration-300" > {/* Quote Icon */} <div className="absolute top-6 right-6"> <Quote className="w-8 h-8 text-blue-200" /> </div> {/* Rating */} <div className="flex items-center mb-4"> {[...Array(testimonial.rating)].map((_, i) => ( <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" /> ))} </div> {/* Content */} <p className="text-foreground mb-6 leading-relaxed"> "{testimonial.content}" </p> {/* Author */} <div className="flex items-center"> <img src={testimonial.image} alt={testimonial.name} className="w-12 h-12 rounded-full object-cover mr-4" /> <div> <h4 className="font-semibold text-foreground">{testimonial.name}</h4> <p className="text-sm text-muted-foreground">{testimonial.role}</p> <p className="text-sm text-sky-700 dark:text-sky-400">{testimonial.hospital}</p> </div> </div> </div> ))} </div> {/* Stats Section */} <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white"> <div className="text-center mb-12"> <h3 className="text-3xl font-bold mb-4"> {t('testimonials.statsTitle')} </h3> <p className="text-xl opacity-90"> {t('testimonials.statsSubtitle')} </p> </div> <div className="grid grid-cols-2 md:grid-cols-4 gap-8"> {stats.map((stat, index) => ( <div key={index} className="text-center"> <div className="text-4xl lg:text-5xl font-bold mb-2"> {stat.number} </div> <div className="text-lg font-semibold mb-2"> {stat.label} </div> <div className="text-sm opacity-80"> {stat.description} </div> </div> ))} </div> </div> {/* CTA Section */} <div className="text-center mt-16"> <h3 className="text-3xl font-bold text-foreground mb-4"> {t('testimonials.ctaTitle')} </h3> <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto"> {t('testimonials.ctaDescription')} </p> <div className="flex flex-col sm:flex-row gap-4 justify-center"> <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors"> {t('testimonials.startTrial')} </button> <button className="border-2 border-blue-600 text-sky-700 dark:text-sky-400 hover:bg-blue-600 hover:text-white px-8 py-4 rounded-xl font-semibold transition-colors"> {t('testimonials.scheduleDemo')} </button> </div> </div> </div> </section> ); }; export default TestimonialsSection; 