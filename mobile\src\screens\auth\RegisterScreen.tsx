import React, { useState } from 'react'; import { View, Text, TextInput, TouchableOpacity, SafeAreaView, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { NativeStackNavigationProp } from '@react-navigation/native-stack'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { AuthStackParamList } from '../../navigation/AuthNavigator'; import { RootState, AppDispatch } from '../../store'; import { register, clearError } from '../../store/slices/authSlice'; import { useTranslation } from '../../hooks/useTranslation'; type RegisterScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Register'>; const RegisterScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { isLoading, error } = useSelector((state: RootState) => state.auth); const [formData, setFormData] = useState({ username: '', email: '', password: '', password_confirm: '', first_name: '', last_name: '', role: 'patient' as const, phone_number: '', }); const [showPassword, setShowPassword] = useState(false); const [showConfirmPassword, setShowConfirmPassword] = useState(false); const handleInputChange = (field: string, value: string) => { setFormData(prev => ({ ...prev, [field]: value })); if (error) { dispatch(clearError()); } }; const validateForm = () => { if (!formData.username.trim()) { Alert.alert(t('common.error'), t('auth.usernameRequired')); return false; } if (!formData.email.trim()) { Alert.alert(t('common.error'), t('auth.emailRequired')); return false; } if (!formData.password.trim()) { Alert.alert(t('common.error'), t('auth.passwordRequired')); return false; } if (formData.password !== formData.password_confirm) { Alert.alert(t('common.error'), t('auth.passwordsNotMatch')); return false; } if (!formData.first_name.trim()) { Alert.alert(t('common.error'), t('auth.firstNameRequired')); return false; } if (!formData.last_name.trim()) { Alert.alert(t('common.error'), t('auth.lastNameRequired')); return false; } return true; }; const handleRegister = async () => { if (!validateForm()) return; try { await dispatch(register(formData)).unwrap(); // Navigation will be handled automatically by AppNavigator } catch (error: any) { Alert.alert(t('auth.registrationFailed'), error || t('auth.failedToCreateAccount')); } }; return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1" > <ScrollView className="flex-1" showsVerticalScrollIndicator={false}> <View className="flex-1 px-8 pt-16"> {/* Header */} <View className="items-center mb-8"> <TouchableOpacity onPress={() => navigation.goBack()} className="absolute left-0 top-0" > <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} /> </TouchableOpacity> <Text className={`text-3xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('auth.createAccount')} </Text> <Text className={`text-base mt-2 ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {t('auth.joinHMSToday')} </Text> </View> {/* Form */} <View className="space-y-4"> {/* Name Fields */} <View className="flex-row space-x-4"> <View className="flex-1"> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('auth.firstName')} </Text> <TextInput value={formData.first_name} onChangeText={(value) => handleInputChange('first_name', value)} placeholder={t('auth.firstName')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} autoCapitalize="words" /> </View> <View className="flex-1"> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('auth.lastName')} </Text> <TextInput value={formData.last_name} onChangeText={(value) => handleInputChange('last_name', value)} placeholder={t('auth.lastName')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} autoCapitalize="words" /> </View> </View> {/* Username Field */} <View> <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Username </Text> <TextInput value={formData.username} onChangeText={(value) => handleInputChange('username', value)} placeholder="Choose a username" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} autoCapitalize="none" autoCorrect={false} /> </View> {/* Email Field */} <View> <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Email </Text> <TextInput value={formData.email} onChangeText={(value) => handleInputChange('email', value)} placeholder="Enter your email" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} keyboardType="email-address" autoCapitalize="none" autoCorrect={false} /> </View> {/* Phone Field */} <View> <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Phone Number (Optional) </Text> <TextInput value={formData.phone_number} onChangeText={(value) => handleInputChange('phone_number', value)} placeholder="Enter your phone number" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} keyboardType="phone-pad" /> </View> {/* Password Fields */} <View> <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Password </Text> <View className={`flex-row items-center border rounded-xl px-4 py-4 ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' } ${isRTL ? 'flex-row-reverse' : ''}`}> <TextInput value={formData.password} onChangeText={(value) => handleInputChange('password', value)} placeholder="Create a password" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`flex-1 text-base ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`} secureTextEntry={!showPassword} autoCapitalize="none" autoCorrect={false} /> <TouchableOpacity onPress={() => setShowPassword(!showPassword)} className="ml-2" > <Ionicons name={showPassword ? 'eye-off-outline' : 'eye-outline'} size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> </TouchableOpacity> </View> </View> <View> <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> Confirm Password </Text> <View className={`flex-row items-center border rounded-xl px-4 py-4 ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' } ${isRTL ? 'flex-row-reverse' : ''}`}> <TextInput value={formData.password_confirm} onChangeText={(value) => handleInputChange('password_confirm', value)} placeholder="Confirm your password" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`flex-1 text-base ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`} secureTextEntry={!showConfirmPassword} autoCapitalize="none" autoCorrect={false} /> <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)} className="ml-2" > <Ionicons name={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'} size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> </TouchableOpacity> </View> </View> {/* Error Message */} {error && ( <View className="bg-destructive/10 border border-destructive/20 rounded-xl p-4"> <Text className="text-destructive text-sm text-center"> {error} </Text> </View> )} {/* Register Button */} <TouchableOpacity onPress={handleRegister} disabled={isLoading} className={`py-4 px-8 rounded-xl shadow-lg mt-6 ${ isLoading ? 'bg-gray-400' : 'bg-primary-600' }`} > <Text className="text-white text-lg font-semibold text-center"> {isLoading ? 'Creating Account...' : 'Create Account'} </Text> </TouchableOpacity> {/* Login Link */} <View className="flex-row justify-center items-center mt-6 mb-8"> <Text className={`text-base ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> Already have an account?{' '} </Text> <TouchableOpacity onPress={() => navigation.navigate('Login')}> <Text className="text-primary-600 text-base font-semibold"> Sign In </Text> </TouchableOpacity> </View> </View> </View> </ScrollView> </KeyboardAvoidingView> </SafeAreaView> ); }; export default RegisterScreen; 