import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { NativeStackNavigationProp } from '@react-navigation/native-stack'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { MainStackParamList } from '../../navigation/MainNavigator'; import { RootState, AppDispatch } from '../../store'; import { fetchMyMedicalRecords } from '../../store/slices/patientSlice'; import { DateTimeUtils } from '../../utils/dateTime'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; import { useTranslation } from '../../hooks/useTranslation'; type MedicalRecordsScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'MedicalRecords'>; const MedicalRecordsScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { medicalRecords, isLoading } = useSelector((state: RootState) => state.patient); const [refreshing, setRefreshing] = useState(false); const [filter, setFilter] = useState<'all' | 'recent' | 'prescriptions'>('all'); useEffect(() => { loadMedicalRecords(); }, [dispatch]); const loadMedicalRecords = async () => { try { await dispatch(fetchMyMedicalRecords({ page: 1, pageSize: 50 })); } catch (error) { console.error('Failed to load medical records:', error); } }; const onRefresh = async () => { setRefreshing(true); await loadMedicalRecords(); setRefreshing(false); }; const getFilteredRecords = () => { switch (filter) { case 'recent': return medicalRecords.filter(record => { const recordDate = new Date(record.visit_date); const thirtyDaysAgo = new Date(); thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30); return recordDate >= thirtyDaysAgo; }); case 'prescriptions': return medicalRecords.filter(record => record.prescription && record.prescription.trim() !== ''); default: return medicalRecords; } }; const filteredRecords = getFilteredRecords(); const renderFilterTabs = () => ( <View className="px-6 mb-4"> <View className={`flex-row rounded-lg p-1 ${isDark ? 'bg-dark-card' : 'bg-muted'} ${isRTL ? 'flex-row-reverse' : ''}`}> {(['all', 'recent', 'prescriptions'] as const).map((filterOption) => ( <TouchableOpacity key={filterOption} onPress={() => setFilter(filterOption)} className={`flex-1 py-2 px-4 rounded-md ${ filter === filterOption ? 'bg-primary-600' : 'bg-transparent' }`} > <Text className={`text-center font-medium ${ filter === filterOption ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {filterOption === 'all' ? 'All Records' : filterOption === 'recent' ? 'Recent' : 'Prescriptions'} </Text> </TouchableOpacity> ))} </View> </View> ); const renderMedicalRecord = (record: any) => ( <TouchableOpacity key={record.id} onPress={() => navigation.navigate('MedicalRecordDetail', { recordId: record.id })} > <Card className="mb-4"> <View className="flex-row justify-between items-start mb-3"> <View className="flex-1"> <Text className={`font-semibold text-lg ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {record.diagnosis} </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Dr. {record.doctor_name} </Text> </View> <Badge variant="info" size="sm"> {DateTimeUtils.formatDate(record.visit_date, 'short')} </Badge> </View> {record.symptoms && ( <View className="mb-3"> <Text className={`text-sm font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Symptoms: </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {record.symptoms.length > 100 ? `${record.symptoms.substring(0, 100)}...` : record.symptoms} </Text> </View> )} {record.treatment && ( <View className="mb-3"> <Text className={`text-sm font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Treatment: </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {record.treatment.length > 100 ? `${record.treatment.substring(0, 100)}...` : record.treatment} </Text> </View> )} {record.prescription && ( <View className="mb-3"> <View className="flex-row items-center"> <Ionicons name="medical" size={16} color="#007AFF" style={{ marginRight: 8 }} /> <Text className={`text-sm font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Prescription Available </Text> </View> </View> )} {record.follow_up_date && ( <View className="flex-row items-center"> <Ionicons name="calendar-outline" size={16} color="#FF9500" style={{ marginRight: 8 }} /> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Follow-up: {DateTimeUtils.formatDate(record.follow_up_date)} </Text> </View> )} <View className="flex-row justify-end mt-3"> <Ionicons name="chevron-forward" size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> </View> </Card> </TouchableOpacity> ); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <View className="flex-row justify-between items-center"> <View className="flex-1"> <Text className={`text-2xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Medical Records </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Your complete medical history </Text> </View> <TouchableOpacity onPress={() => navigation.navigate('AddMedicalRecord' as never)} className="bg-primary-600 w-12 h-12 rounded-full items-center justify-center" > <Ionicons name="add" size={24} color="white" /> </TouchableOpacity> </View> </View> {renderFilterTabs()} {/* Records List */} <ScrollView className="flex-1 px-6" refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } showsVerticalScrollIndicator={false} > {isLoading && !refreshing ? ( <LoadingSpinner message="Loading medical records..." /> ) : filteredRecords.length > 0 ? ( <View className="pb-6"> {filteredRecords.map(renderMedicalRecord)} </View> ) : ( <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm`}> <View className="items-center"> <Ionicons name="document-text-outline" size={48} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-lg font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> No {filter === 'all' ? '' : filter} records found </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {filter === 'recent' ? "You don't have any medical records from the last 30 days." : filter === 'prescriptions' ? "You don't have any records with prescriptions." : "Your medical records will appear here after your first visit." } </Text> </View> </View> )} </ScrollView> </SafeAreaView> ); }; export default MedicalRecordsScreen; 