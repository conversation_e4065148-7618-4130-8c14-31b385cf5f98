import React, { useState, useEffect } from 'react'; import { useTranslation } from 'react-i18next'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import { Button } from '../ui/Button'; import { Badge } from '../ui/badge'; import { Input } from '../ui/Input'; import { useTheme } from '../../hooks/useTheme'; import CRUDModal from '../ui/CRUDModal'; import { crudService } from '../../services/crudService'; import UserRegistration from './UserRegistration'; import { Users, Plus, Search, Shield, UserCheck, UserX, Eye, Edit, Trash2, Filter, Mail, Phone, Calendar, Loader2, AlertTriangle, UserPlus, List } from 'lucide-react'; import { getRoleClass, getStatusClass } from '../../utils/styleUtils'; interface User { id: string; username: string; email: string; firstName: string; lastName: string; role: 'admin' | 'doctor' | 'nurse' | 'patient' | 'receptionist'; status: 'active' | 'inactive' | 'suspended'; lastLogin?: string; dateJoined: string; phoneNumber?: string; department?: string; } const UserManagement: React.FC = () => { const { t } = useTranslation(); const { isDark } = useTheme(); const [activeTab, setActiveTab] = useState<'list' | 'register'>('list'); const [searchTerm, setSearchTerm] = useState(''); const [selectedRole, setSelectedRole] = useState('all'); const [selectedStatus, setSelectedStatus] = useState('all'); const [showModal, setShowModal] = useState(false); const [modalMode, setModalMode] = useState<'create' | 'edit'>('create'); const [selectedUser, setSelectedUser] = useState<User | null>(null); const [users, setUsers] = useState<User[]>([]); const [loading, setLoading] = useState(true); const [actionLoading, setActionLoading] = useState<string | null>(null); // Load users from API useEffect(() => { loadUsers(); }, []); const loadUsers = async () => { setLoading(true); try { const response = await crudService.getUsers(); setUsers(response.results || []); } catch (error: any) { console.error('Failed to load users:', error); // Check if it's an authentication error if (error.message === 'Session expired. Please login again.' || error.message === 'Not authenticated. Please login first.') { alert('Your session has expired. Please login again.'); return; // Don't load mock data for auth errors } // Fallback to mock data for other errors setUsers([ { id: 'U001', username: 'admin', email: '<EMAIL>', firstName: 'John', lastName: 'Admin', role: 'admin', status: 'active', lastLogin: '2024-12-12 09:30', dateJoined: '2024-01-15', phoneNumber: '+1234567890', department: 'Administration' }, { id: 'U002', username: 'dr.smith', email: '<EMAIL>', firstName: 'Sarah', lastName: 'Smith', role: 'doctor', status: 'active', lastLogin: '2024-12-12 08:15', dateJoined: '2024-02-20', phoneNumber: '+1234567891', department: 'Cardiology' } ]); } finally { setLoading(false); } }; const filteredUsers = users.filter(user => { // Safe access to properties with fallbacks const firstName = user.firstName || user.first_name || ''; const lastName = user.lastName || user.last_name || ''; const email = user.email || ''; const username = user.username || ''; const role = user.role || ''; const status = user.status || user.is_active ? 'active' : 'inactive'; const matchesSearch = firstName.toLowerCase().includes(searchTerm.toLowerCase()) || lastName.toLowerCase().includes(searchTerm.toLowerCase()) || email.toLowerCase().includes(searchTerm.toLowerCase()) || username.toLowerCase().includes(searchTerm.toLowerCase()); const matchesRole = selectedRole === 'all' || role === selectedRole; const matchesStatus = selectedStatus === 'all' || status === selectedStatus; return matchesSearch && matchesRole && matchesStatus; }); // CRUD Handlers const handleCreateUser = () => { setSelectedUser(null); setModalMode('create'); setShowModal(true); }; const handleEditUser = (user: User) => { setSelectedUser(user); setModalMode('edit'); setShowModal(true); }; const handleDeleteUser = async (userId: string) => { if (!confirm('Are you sure you want to delete this user?')) return; setActionLoading(userId); try { await crudService.deleteUser(userId); await loadUsers(); } catch (error: any) { console.error('Failed to delete user:', error); if (error.message === 'Session expired. Please login again.' || error.message === 'Not authenticated. Please login first.') { alert('Your session has expired. Please login again.'); } else { alert('Failed to delete user. Please try again.'); } } finally { setActionLoading(null); } }; const handleToggleUserStatus = async (user: User) => { setActionLoading(user.id); try { const isCurrentlyActive = user.status === 'active' || user.is_active; if (isCurrentlyActive) { await crudService.deactivateUser(user.id); } else { await crudService.activateUser(user.id); } await loadUsers(); } catch (error: any) { console.error('Failed to toggle user status:', error); if (error.message === 'Session expired. Please login again.' || error.message === 'Not authenticated. Please login first.') { alert('Your session has expired. Please login again.'); } else { alert('Failed to update user status. Please try again.'); } } finally { setActionLoading(null); } }; const handleSubmitUser = async (userData: any) => { try { if (modalMode === 'create') { await crudService.createUser(userData); } else if (selectedUser) { await crudService.updateUser(selectedUser.id, userData); } await loadUsers(); } catch (error: any) { console.error('Failed to save user:', error); if (error.message === 'Session expired. Please login again.' || error.message === 'Not authenticated. Please login first.') { throw new Error('Your session has expired. Please login again.'); } throw error; } }; // Form fields for user modal const userFormFields = [ { key: 'username', label: 'Username', type: 'text' as const, required: true }, { key: 'email', label: 'Email', type: 'email' as const, required: true }, { key: 'first_name', label: 'First Name', type: 'text' as const, required: true }, { key: 'last_name', label: 'Last Name', type: 'text' as const, required: true }, { key: 'role', label: 'Role', type: 'select' as const, required: true, options: [ { value: 'admin', label: 'Admin' }, { value: 'doctor', label: 'Doctor' }, { value: 'nurse', label: 'Nurse' }, { value: 'patient', label: 'Patient' }, { value: 'receptionist', label: 'Receptionist' } ] }, { key: 'phone_number', label: 'Phone Number', type: 'text' as const }, { key: 'date_of_birth', label: 'Date of Birth', type: 'date' as const }, { key: 'address', label: 'Address', type: 'textarea' as const }, ...(modalMode === 'create' ? [ { key: 'password', label: 'Password', type: 'password' as const, required: true } ] : []) ]; const getRoleIcon = (role: string) => { switch (role) { case 'admin': return <Shield className="w-4 h-4" />; case 'doctor': return <UserCheck className="w-4 h-4" />; case 'nurse': return <UserCheck className="w-4 h-4" />; case 'patient': return <Users className="w-4 h-4" />; case 'receptionist': return <UserCheck className="w-4 h-4" />; default: return <Users className="w-4 h-4" />; } }; return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */} <Card className="glass border-0 shadow-xl"> <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg"> <Users className="w-6 h-6 text-white" /> </div> <div> <CardTitle className="text-2xl font-bold text-heading"> {t('users.title')} </CardTitle> <CardDescription className="text-subheading"> {t('users.subtitle')} </CardDescription> </div> </div> <div className="flex items-center gap-2"> <Button variant={activeTab === 'list' ? 'default' : 'outline'} onClick={() => setActiveTab('list')} className="flex items-center gap-2" > <List className="w-4 h-4" /> User List </Button> <Button variant={activeTab === 'register' ? 'default' : 'outline'} onClick={() => setActiveTab('register')} className="flex items-center gap-2" > <UserPlus className="w-4 h-4" /> Register User </Button> </div> </div> </CardHeader> </Card> {/* Conditional Content */} {activeTab === 'register' ? ( <UserRegistration /> ) : ( <> {/* Filters and Search */} <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex flex-col md:flex-row gap-4"> <div className="flex-1"> <div className="relative"> <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" /> <Input variant="glass" placeholder="Search by name, email, or username..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10" /> </div> </div> <div className="flex items-center gap-4"> <div className="flex items-center gap-2"> <Filter className="w-4 h-4 macos-text-secondary" /> <select value={selectedRole} onChange={(e) => setSelectedRole(e.target.value)} className="glass border-0 rounded-xl px-4 py-2 text-sm macos-text-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" > <option value="all">All Roles</option> <option value="admin">Admin</option> <option value="doctor">Doctor</option> <option value="nurse">Nurse</option> <option value="patient">Patient</option> <option value="receptionist">Receptionist</option> </select> </div> <div className="flex items-center gap-2"> <select value={selectedStatus} onChange={(e) => setSelectedStatus(e.target.value)} className="glass border-0 rounded-xl px-4 py-2 text-sm macos-text-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" > <option value="all">All Status</option> <option value="active">Active</option> <option value="inactive">Inactive</option> <option value="suspended">Suspended</option> </select> </div> </div> </div> </CardContent> </Card> {/* Statistics */} <div className="grid grid-cols-1 md:grid-cols-4 gap-6"> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Total Users</p> <p className="text-2xl font-bold macos-text-primary">{users.length}</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"> <Users className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Active Users</p> <p className="text-2xl font-bold macos-text-primary"> {users.filter(u => u.status === 'active' || u.is_active).length} </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg"> <UserCheck className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Staff Members</p> <p className="text-2xl font-bold macos-text-primary"> {users.filter(u => ['admin', 'doctor', 'nurse', 'receptionist'].includes(u.role)).length} </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg"> <Shield className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Patients</p> <p className="text-2xl font-bold macos-text-primary"> {users.filter(u => u.role === 'patient').length} </p> </div> <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg"> <Users className="w-6 h-6 text-white" /> </div> </div> </CardContent> </Card> </div> {/* Users List */} {loading ? ( <Card className="glass border-0 shadow-lg"> <CardContent className="p-12 text-center"> <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 macos-accent-text" /> <p className="macos-text-secondary">Loading users...</p> </CardContent> </Card> ) : ( <div className="space-y-4"> {filteredUsers.map((user) => ( <Card key={user.id} className="glass border-0 shadow-lg hover:glass-hover macos-transition"> <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center gap-4 mb-4"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg"> <span className="text-white text-sm font-semibold"> {((user.firstName || user.first_name || '').charAt(0))} {((user.lastName || user.last_name || '').charAt(0))} </span> </div> <div> <h3 className="text-lg font-semibold macos-text-primary"> {(user.firstName || user.first_name || '')} {(user.lastName || user.last_name || '')} </h3> <p className="text-sm macos-text-secondary">@{user.username || ''}</p> </div> <Badge className={`${getRoleClass(user.role)} rounded-full px-3 py-1 flex items-center gap-1`}> {getRoleIcon(user.role)} <span className="capitalize">{user.role}</span> </Badge> <Badge className={`${getStatusClass(user.status || (user.is_active ? 'active' : 'inactive'))} rounded-full px-3 py-1 flex items-center gap-1`}> {(user.status === 'active' || user.is_active) ? <UserCheck className="w-3 h-3" /> : <UserX className="w-3 h-3" />} {user.status || (user.is_active ? 'active' : 'inactive')} </Badge> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"> <div className="flex items-center gap-2"> <Mail className="w-4 h-4 macos-text-tertiary" /> <span className="text-sm macos-text-secondary">{user.email || ''}</span> </div> {(user.phoneNumber || user.phone_number) && ( <div className="flex items-center gap-2"> <Phone className="w-4 h-4 macos-text-tertiary" /> <span className="text-sm macos-text-secondary">{user.phoneNumber || user.phone_number}</span> </div> )} <div className="flex items-center gap-2"> <Calendar className="w-4 h-4 macos-text-tertiary" /> <span className="text-sm macos-text-secondary">Joined: {user.dateJoined || user.date_joined || ''}</span> </div> </div> <div className="flex items-center gap-6 text-sm macos-text-secondary"> {user.department && ( <span>Department: <span className="macos-text-primary font-medium">{user.department}</span></span> )} {user.lastLogin && ( <span>Last login: <span className="macos-text-primary font-medium">{user.lastLogin}</span></span> )} </div> </div> <div className="flex flex-col gap-2 ml-6"> <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleEditUser(user)} > <Eye className="w-4 h-4" /> View </Button> <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleEditUser(user)} > <Edit className="w-4 h-4" /> Edit </Button> <Button variant="ghost" size="sm" className="flex items-center gap-2" onClick={() => handleToggleUserStatus(user)} disabled={actionLoading === user.id} > {actionLoading === user.id ? ( <Loader2 className="w-4 h-4 animate-spin" /> ) : (user.status === 'active' || user.is_active) ? ( <UserX className="w-4 h-4" /> ) : ( <UserCheck className="w-4 h-4" /> )} {(user.status === 'active' || user.is_active) ? 'Deactivate' : 'Activate'} </Button> <Button variant="destructive" size="sm" className="flex items-center gap-2" onClick={() => handleDeleteUser(user.id)} disabled={actionLoading === user.id} > {actionLoading === user.id ? ( <Loader2 className="w-4 h-4 animate-spin" /> ) : ( <Trash2 className="w-4 h-4" /> )} Delete </Button> </div> </div> </CardContent> </Card> ))} </div> )} {!loading && filteredUsers.length === 0 && ( <Card className="glass border-0 shadow-lg"> <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg"> <Users className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2">No users found</h3> <p className="macos-text-secondary"> {searchTerm ? 'Try adjusting your search criteria' : 'Start by adding a new user'} </p> </CardContent> </Card> )} {/* CRUD Modal */} <CRUDModal isOpen={showModal} onClose={() => setShowModal(false)} onSubmit={handleSubmitUser} title={modalMode === 'create' ? 'Add New User' : 'Edit User'} fields={userFormFields} initialData={selectedUser || {}} mode={modalMode} /> </> )} </div> </div> ); }; export default UserManagement; 