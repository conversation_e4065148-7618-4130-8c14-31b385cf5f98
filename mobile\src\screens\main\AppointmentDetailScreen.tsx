import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, Alert } from 'react-native'; import { useRoute, RouteProp, useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { MainStackParamList } from '../../navigation/MainNavigator'; import { RootState, AppDispatch } from '../../store'; import { fetchAppointment, cancelAppointment, confirmAppointment, rescheduleAppointment } from '../../store/slices/appointmentSlice'; import { DateTimeUtils } from '../../utils/dateTime'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import Button from '../../components/ui/Button'; import LoadingSpinner from '../../components/common/LoadingSpinner'; import { useTranslation } from '../../hooks/useTranslation'; type AppointmentDetailRouteProp = RouteProp<MainStackParamList, 'AppointmentDetail'>; const AppointmentDetailScreen: React.FC = () => { const route = useRoute(); const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { appointmentId } = route.params; const { appointments, isLoading } = useSelector((state: RootState) => state.appointment); const [appointment, setAppointment] = useState<any>(null); const [loading, setLoading] = useState(true); useEffect(() => { loadAppointment(); }, [appointmentId]); const loadAppointment = async () => { try { // First try to find in existing appointments const existingAppointment = appointments.find(a => a.id === appointmentId); if (existingAppointment) { setAppointment(existingAppointment); setLoading(false); return; } // If not found, fetch from API const result = await dispatch(fetchAppointment(appointmentId)).unwrap(); setAppointment(result); } catch (error) { console.error('Failed to load appointment:', error); Alert.alert('Error', 'Failed to load appointment details.'); } finally { setLoading(false); } }; const handleCancelAppointment = () => { Alert.alert( 'Cancel Appointment', 'Are you sure you want to cancel this appointment? This action cannot be undone.', [ { text: 'No', style: 'cancel' }, { text: 'Yes, Cancel', style: 'destructive', onPress: async () => { try { await dispatch(cancelAppointment(appointmentId)).unwrap(); Alert.alert('Success', 'Appointment has been cancelled.', [ { text: 'OK', onPress: () => navigation.goBack() } ]); } catch (error: any) { Alert.alert('Error', error || 'Failed to cancel appointment.'); } } } ] ); }; const handleConfirmAppointment = async () => { try { await dispatch(confirmAppointment(appointmentId)).unwrap(); Alert.alert('Success', 'Appointment has been confirmed.'); loadAppointment(); // Refresh appointment data } catch (error: any) { Alert.alert('Error', error || 'Failed to confirm appointment.'); } }; const handleRescheduleAppointment = () => { Alert.alert( 'Reschedule Appointment', 'Rescheduling functionality will be available in the next update.', [{ text: 'OK' }] ); }; const getStatusColor = (status: string) => { switch (status) { case 'confirmed': return 'success'; case 'scheduled': return 'info'; case 'completed': return 'default'; case 'cancelled': return 'error'; case 'in_progress': return 'warning'; default: return 'default'; } }; const getStatusIcon = (status: string): keyof typeof Ionicons.glyphMap => { switch (status) { case 'confirmed': return 'checkmark-circle'; case 'scheduled': return 'time'; case 'completed': return 'checkmark-done-circle'; case 'cancelled': return 'close-circle'; case 'in_progress': return 'play-circle'; default: return 'help-circle'; } }; if (loading) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <LoadingSpinner message="Loading appointment details..." /> </SafeAreaView> ); } if (!appointment) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <View className="flex-1 justify-center items-center px-6"> <Ionicons name="calendar-outline" size={64} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-xl font-bold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointment Not Found </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> The requested appointment could not be found. </Text> </View> </SafeAreaView> ); } const appointmentDateTime = DateTimeUtils.parseAppointmentDateTime( appointment.appointment_date, appointment.appointment_time ); const isPastAppointment = DateTimeUtils.isPast(appointmentDateTime); const canModify = !isPastAppointment && !['cancelled', 'completed'].includes(appointment.status); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <ScrollView className="flex-1 px-6 pt-6" showsVerticalScrollIndicator={false}> {/* Header */} <View className="mb-6"> <Text className={`text-2xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointment Details </Text> <View className="flex-row items-center mt-2"> <Badge variant={getStatusColor(appointment.status) as any} size="sm"> {appointment.status.toUpperCase()} </Badge> <Text className={`ml-3 text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> ID: {appointment.appointment_id} </Text> </View> </View> {/* Status Card */} <Card className="mb-4"> <View className="flex-row items-center"> <Ionicons name={getStatusIcon(appointment.status)} size={24} color="#007AFF" style={{ marginRight: 12 }} /> <View className="flex-1"> <Text className={`font-semibold text-lg ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {appointment.status === 'scheduled' ? 'Appointment Scheduled' : appointment.status === 'confirmed' ? 'Appointment Confirmed' : appointment.status === 'completed' ? 'Appointment Completed' : appointment.status === 'cancelled' ? 'Appointment Cancelled' : appointment.status === 'in_progress' ? 'Appointment In Progress' : 'Appointment Status'} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {DateTimeUtils.formatDateTime(appointment.appointment_date, appointment.appointment_time)} </Text> </View> </View> </Card> {/* Doctor Information */} <Card className="mb-4"> <View className="flex-row items-center"> <View className={`w-16 h-16 rounded-full mr-4 items-center justify-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name="person" size={32} color="#007AFF" /> </View> <View className="flex-1"> <Text className={`font-semibold text-xl ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Dr. {appointment.doctor_name} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Attending Physician </Text> <View className="flex-row items-center mt-1"> <Ionicons name="star" size={16} color="#FFD700" /> <Text className={`text-sm ml-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> 4.8 (124 reviews) </Text> </View> </View> </View> </Card> {/* Appointment Information */} <Card className="mb-4"> <Text className={`font-semibold text-lg mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointment Information </Text> <View className="space-y-4"> <View className="flex-row items-center"> <Ionicons name="calendar" size={20} color="#007AFF" style={{ marginRight: 12 }} /> <View className="flex-1"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Date </Text> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {DateTimeUtils.formatDate(appointment.appointment_date, 'long')} </Text> </View> </View> <View className="flex-row items-center"> <Ionicons name="time" size={20} color="#007AFF" style={{ marginRight: 12 }} /> <View className="flex-1"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Time </Text> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {DateTimeUtils.formatTime(appointment.appointment_time)} </Text> </View> </View> <View className="flex-row items-center"> <Ionicons name="hourglass" size={20} color="#007AFF" style={{ marginRight: 12 }} /> <View className="flex-1"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Duration </Text> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {appointment.duration || 30} minutes </Text> </View> </View> <View className="flex-row items-center"> <Ionicons name="medical" size={20} color="#007AFF" style={{ marginRight: 12 }} /> <View className="flex-1"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Type </Text> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {appointment.appointment_type.replace('_', ' ').toUpperCase()} </Text> </View> </View> <View className="flex-row items-center"> <Ionicons name="location" size={20} color="#007AFF" style={{ marginRight: 12 }} /> <View className="flex-1"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Location </Text> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> HMS Medical Center, Room 205 </Text> </View> </View> </View> </Card> {/* Reason */} {appointment.reason && ( <Card className="mb-4"> <Text className={`font-semibold text-lg mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Reason for Visit </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {appointment.reason} </Text> </Card> )} {/* Notes */} {appointment.notes && ( <Card className="mb-4"> <Text className={`font-semibold text-lg mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Additional Notes </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {appointment.notes} </Text> </Card> )} {/* Appointment Timeline */} <Card className="mb-6"> <Text className={`font-semibold text-lg mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Appointment Timeline </Text> <View className="space-y-3"> <View className="flex-row justify-between"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Created </Text> <Text className={`text-sm ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {DateTimeUtils.formatDate(appointment.created_at)} </Text> </View> <View className="flex-row justify-between"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Last Updated </Text> <Text className={`text-sm ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {DateTimeUtils.formatDate(appointment.updated_at)} </Text> </View> </View> </Card> {/* Action Buttons */} {canModify && ( <View className="space-y-3 mb-8"> {appointment.status === 'scheduled' && ( <Button title="Confirm Appointment" onPress={handleConfirmAppointment} loading={isLoading} icon={<Ionicons name="checkmark" size={20} color="white" />} /> )} <View className="flex-row space-x-3"> <Button title="Reschedule" variant="outline" onPress={handleRescheduleAppointment} className="flex-1" icon={<Ionicons name="calendar-outline" size={20} color="#007AFF" />} /> <Button title={t('common.cancel')} variant="destructive" onPress={handleCancelAppointment} className="flex-1" loading={isLoading} icon={<Ionicons name="close" size={20} color="white" />} /> </View> </View> )} {/* Contact Information */} <Card className="mb-8"> <Text className={`font-semibold text-lg mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Need Help? </Text> <View className="space-y-3"> <TouchableOpacity className="flex-row items-center"> <Ionicons name="call" size={20} color="#007AFF" style={{ marginRight: 12 }} /> <Text className="text-primary-600 font-medium">Call HMS Reception: +1 (555) 123-4567</Text> </TouchableOpacity> <TouchableOpacity className="flex-row items-center"> <Ionicons name="mail" size={20} color="#007AFF" style={{ marginRight: 12 }} /> <Text className="text-primary-600 font-medium">Email: <EMAIL></Text> </TouchableOpacity> </View> </Card> </ScrollView> </SafeAreaView> ); }; export default AppointmentDetailScreen; 