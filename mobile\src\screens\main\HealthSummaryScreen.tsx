import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl, Dimensions, Alert } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { RootState, AppDispatch } from '../../store'; import { fetchHealthMetrics, fetchVitalSigns, fetchHealthAlerts, markAlertAsRead } from '../../store/slices/healthSummarySlice'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; interface HealthMetric { id: number; name: string; value: string; unit: string; status: 'normal' | 'warning' | 'critical'; lastUpdated: string; trend: 'up' | 'down' | 'stable'; icon: keyof typeof Ionicons.glyphMap; } interface VitalSign { id: number; type: string; value: string; unit: string; recordedDate: string; recordedBy: string; status: 'normal' | 'abnormal'; } interface HealthAlert { id: number; type: 'medication' | 'appointment' | 'test' | 'checkup'; title: string; message: string; priority: 'low' | 'medium' | 'high'; dueDate: string; icon: keyof typeof Ionicons.glyphMap; } const HealthSummaryScreen: React.FC = () => { const navigation = useNavigation(); const dispatch = useDispatch<AppDispatch>(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const { healthMetrics, vitalSigns, healthAlerts, isLoading, error } = useSelector((state: RootState) => state.healthSummary); const [refreshing, setRefreshing] = useState(false); // Load health data on component mount useEffect(() => { loadHealthData(); }, [dispatch]); const loadHealthData = () => { dispatch(fetchHealthMetrics()); dispatch(fetchVitalSigns()); dispatch(fetchHealthAlerts()); }; const onRefresh = async () => { setRefreshing(true); loadHealthData(); setRefreshing(false); }; // Show error alert if there's an error useEffect(() => { if (error) { Alert.alert(t('common.error'), error); } }, [error, t]); const getMetricStatusColor = (status: string) => { switch (status) { case 'normal': return '#10B981'; case 'warning': return '#F59E0B'; case 'critical': return '#EF4444'; default: return '#6B7280'; } }; const getTrendIcon = (trend: string) => { switch (trend) { case 'up': return 'trending-up'; case 'down': return 'trending-down'; case 'stable': return 'remove'; default: return 'help'; } }; const getTrendColor = (trend: string, status: string) => { if (status === 'critical') return '#EF4444'; switch (trend) { case 'up': return status === 'warning' ? '#F59E0B' : '#10B981'; case 'down': return status === 'warning' ? '#F59E0B' : '#10B981'; case 'stable': return '#6B7280'; default: return '#6B7280'; } }; const getPriorityColor = (priority: string) => { switch (priority) { case 'high': return 'destructive'; case 'medium': return 'warning'; case 'low': return 'secondary'; default: return 'secondary'; } }; const renderHealthMetrics = () => ( <View className="mb-6"> <Text className={`text-lg font-semibold mb-4 px-6 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('healthSummary.keyMetrics')} </Text> <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-6"> <View className={`flex-row space-x-4 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> {healthMetrics.map((metric) => ( <Card key={metric.id} className="w-40 p-4"> <View className="items-center"> <View className="w-12 h-12 rounded-full justify-center items-center mb-3" style={{ backgroundColor: `${getMetricStatusColor(metric.status)}20` }} > <Ionicons name={metric.icon} size={24} color={getMetricStatusColor(metric.status)} /> </View> <Text className={`text-sm text-center mb-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {metric.name} </Text> <View className="flex-row items-center mb-2"> <Text className={`text-xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {metric.value} </Text> <Text className={`text-sm ml-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {metric.unit} </Text> </View> <View className="flex-row items-center"> <Ionicons name={getTrendIcon(metric.trend)} size={16} color={getTrendColor(metric.trend, metric.status)} /> <Badge variant={metric.status === 'normal' ? 'success' : metric.status === 'warning' ? 'warning' : 'destructive'} className="ml-2" > {t(`healthSummary.status.${metric.status}`)} </Badge> </View> </View> </Card> ))} </View> </ScrollView> </View> ); const renderVitalSigns = () => ( <View className="mb-6 px-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('healthSummary.latestVitals')} </Text> <Card className="p-4"> <View className="space-y-3"> {vitalSigns.map((vital, index) => ( <View key={vital.id}> <View className={`flex-row items-center justify-between ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {vital.type} </Text> <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`font-bold mr-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {vital.value} {vital.unit} </Text> <Badge variant={vital.status === 'normal' ? 'success' : 'warning'}> {t(`healthSummary.status.${vital.status}`)} </Badge> </View> </View> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {new Date(vital.recordedDate).toLocaleDateString()} • {vital.recordedBy} </Text> {index < vitalSigns.length - 1 && ( <View className={`h-px mt-3 ${isDark ? 'bg-dark-border' : 'bg-border'}`} /> )} </View> ))} </View> </Card> </View> ); const renderHealthAlerts = () => ( <View className="mb-6 px-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('healthSummary.healthAlerts')} </Text> <View className="space-y-3"> {healthAlerts.map((alert) => ( <Card key={alert.id} className="p-4"> <View className={`flex-row items-start ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <View className="w-10 h-10 rounded-full justify-center items-center mr-3" style={{ backgroundColor: alert.priority === 'high' ? '#FEE2E2' : alert.priority === 'medium' ? '#FEF3C7' : '#F3F4F6' }} > <Ionicons name={alert.icon} size={20} color={alert.priority === 'high' ? '#EF4444' : alert.priority === 'medium' ? '#F59E0B' : '#6B7280'} /> </View> <View className="flex-1"> <View className={`flex-row items-center justify-between ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {alert.title} </Text> <Badge variant={getPriorityColor(alert.priority)}> {t(`healthSummary.priority.${alert.priority}`)} </Badge> </View> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {alert.message} </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('healthSummary.due')}: {new Date(alert.dueDate).toLocaleString()} </Text> </View> </View> </Card> ))} </View> </View> ); const renderQuickActions = () => ( <View className="px-6 mb-6"> <Text className={`text-lg font-semibold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('healthSummary.quickActions')} </Text> <View className="flex-row flex-wrap justify-between"> {[ { key: 'addVitals', icon: 'add-circle', label: t('healthSummary.addVitals'), screen: 'AddVitals' }, { key: 'viewTrends', icon: 'trending-up', label: t('healthSummary.viewTrends'), screen: 'HealthTrends' }, { key: 'setGoals', icon: 'flag', label: t('healthSummary.setGoals'), screen: 'HealthGoals' }, { key: 'exportData', icon: 'download', label: t('healthSummary.exportData'), screen: 'ExportHealth' } ].map((action) => ( <TouchableOpacity key={action.key} onPress={() => navigation.navigate(action.screen as never)} className={`w-[48%] p-4 rounded-xl mb-3 ${isDark ? 'bg-dark-card border border-dark-border' : 'bg-background border border-border'}`} > <Ionicons name={action.icon as keyof typeof Ionicons.glyphMap} size={24} color="#007AFF" style={{ marginBottom: 8 }} /> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {action.label} </Text> </TouchableOpacity> ))} </View> </View> ); if (isLoading) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <LoadingSpinner /> </SafeAreaView> ); } return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('healthSummary.title')} </Text> <Text className={`text-sm mt-1 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('healthSummary.subtitle')} </Text> </View> <ScrollView className="flex-1" showsVerticalScrollIndicator={false} refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } > {renderHealthMetrics()} {renderVitalSigns()} {renderHealthAlerts()} {renderQuickActions()} </ScrollView> </SafeAreaView> ); }; export default HealthSummaryScreen; 