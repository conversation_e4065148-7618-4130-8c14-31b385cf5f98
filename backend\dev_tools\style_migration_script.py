#!/usr/bin/env python3
"""
HMS Style Migration Script
Automatically migrates hardcoded colors to unified style system
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple

class StyleMigrator:
    """Migrates hardcoded colors to unified style system"""
    
    def __init__(self):
        self.frontend_dir = Path('frontend/src')
        self.mobile_dir = Path('mobile/src')
        self.files_processed = 0
        self.replacements_made = 0
        
        # Color mapping patterns
        self.color_mappings = {
            # Status colors
            'bg-green-100 text-green-800': 'status-success',
            'bg-green-50 text-green-700': 'status-success',
            'bg-emerald-100 text-emerald-800': 'status-success',
            'bg-yellow-100 text-yellow-800': 'status-warning',
            'bg-amber-100 text-amber-800': 'status-warning',
            'bg-red-100 text-red-800': 'status-error',
            'bg-rose-100 text-rose-800': 'status-error',
            'bg-blue-100 text-blue-800': 'status-info',
            'bg-sky-100 text-sky-800': 'status-info',
            
            # Dark mode variants
            'dark:bg-green-900/20 dark:text-green-400': '',
            'dark:bg-green-900/30 dark:text-green-400': '',
            'dark:bg-yellow-900/20 dark:text-yellow-400': '',
            'dark:bg-yellow-900/30 dark:text-yellow-400': '',
            'dark:bg-red-900/20 dark:text-red-400': '',
            'dark:bg-red-900/30 dark:text-red-400': '',
            'dark:bg-blue-900/20 dark:text-blue-400': '',
            'dark:bg-blue-900/30 dark:text-blue-400': '',
            
            # Border colors
            'border-green-200': '',
            'border-yellow-200': '',
            'border-red-200': '',
            'border-blue-200': '',
            'dark:border-green-800': '',
            'dark:border-yellow-800': '',
            'dark:border-red-800': '',
            'dark:border-blue-800': '',
            
            # Text colors
            'text-green-600': 'text-emerald-700 dark:text-emerald-400',
            'text-green-700': 'text-emerald-700 dark:text-emerald-400',
            'text-green-800': 'text-emerald-700 dark:text-emerald-400',
            'text-yellow-600': 'text-amber-700 dark:text-amber-400',
            'text-yellow-700': 'text-amber-700 dark:text-amber-400',
            'text-yellow-800': 'text-amber-700 dark:text-amber-400',
            'text-red-600': 'text-rose-700 dark:text-rose-400',
            'text-red-700': 'text-rose-700 dark:text-rose-400',
            'text-red-800': 'text-rose-700 dark:text-rose-400',
            'text-blue-600': 'text-sky-700 dark:text-sky-400',
            'text-blue-700': 'text-sky-700 dark:text-sky-400',
            'text-blue-800': 'text-sky-700 dark:text-sky-400',
            
            # Background colors
            'bg-gray-50': 'bg-muted',
            'bg-gray-100': 'bg-muted',
            'bg-gray-200': 'bg-accent',
            'bg-white': 'bg-background',
            'bg-black': 'bg-background',
            
            # Common patterns
            'text-gray-600': 'text-muted-foreground',
            'text-gray-700': 'text-foreground',
            'text-gray-800': 'text-foreground',
            'text-gray-900': 'text-foreground',
            'border-gray-200': 'border-border',
            'border-gray-300': 'border-border',
        }
        
        # Complex pattern replacements
        self.pattern_replacements = [
            # Status badge patterns
            (r'bg-(\w+)-100/80 text-\1-800 border border-\1-200/50 dark:bg-\1-900/20 dark:text-\1-400 dark:border-\1-700/50', 
             lambda m: self._get_status_class(m.group(1))),
            
            # Simple color patterns
            (r'bg-(\w+)-(\d+)', lambda m: self._replace_bg_color(m.group(1), m.group(2))),
            (r'text-(\w+)-(\d+)', lambda m: self._replace_text_color(m.group(1), m.group(2))),
            (r'border-(\w+)-(\d+)', lambda m: self._replace_border_color(m.group(1), m.group(2))),
        ]
    
    def _get_status_class(self, color: str) -> str:
        """Get status class for color"""
        status_map = {
            'green': 'status-success',
            'emerald': 'status-success',
            'yellow': 'status-warning',
            'amber': 'status-warning',
            'red': 'status-error',
            'rose': 'status-error',
            'blue': 'status-info',
            'sky': 'status-info',
        }
        return status_map.get(color, f'bg-{color}-100/80 text-{color}-800')
    
    def _replace_bg_color(self, color: str, shade: str) -> str:
        """Replace background color with theme equivalent"""
        if color in ['gray', 'slate'] and shade in ['50', '100']:
            return 'bg-muted'
        elif color in ['gray', 'slate'] and shade in ['200', '300']:
            return 'bg-accent'
        elif color == 'white':
            return 'bg-background'
        elif color == 'black':
            return 'bg-background'
        return f'bg-{color}-{shade}'
    
    def _replace_text_color(self, color: str, shade: str) -> str:
        """Replace text color with theme equivalent"""
        if color in ['gray', 'slate'] and shade in ['600', '700']:
            return 'text-muted-foreground'
        elif color in ['gray', 'slate'] and shade in ['800', '900']:
            return 'text-foreground'
        elif color == 'white':
            return 'text-background'
        elif color == 'black':
            return 'text-foreground'
        return f'text-{color}-{shade}'
    
    def _replace_border_color(self, color: str, shade: str) -> str:
        """Replace border color with theme equivalent"""
        if color in ['gray', 'slate'] and shade in ['200', '300']:
            return 'border-border'
        return f'border-{color}-{shade}'
    
    def migrate_file(self, file_path: Path) -> int:
        """Migrate a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            replacements = 0
            
            # Apply direct mappings
            for old_pattern, new_pattern in self.color_mappings.items():
                if old_pattern in content:
                    content = content.replace(old_pattern, new_pattern)
                    replacements += content.count(new_pattern) - original_content.count(new_pattern)
            
            # Apply regex patterns
            for pattern, replacement_func in self.pattern_replacements:
                matches = re.finditer(pattern, content)
                for match in matches:
                    old_text = match.group(0)
                    new_text = replacement_func(match)
                    if new_text != old_text:
                        content = content.replace(old_text, new_text)
                        replacements += 1
            
            # Clean up multiple spaces and empty classes
            content = re.sub(r'\s+', ' ', content)
            content = re.sub(r'className="(\s*)"', 'className=""', content)
            content = re.sub(r'className="\s+([^"]*)"', r'className="\1"', content)
            
            # Write back if changes were made
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return replacements
            
            return 0
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return 0
    
    def migrate_directory(self, directory: Path) -> None:
        """Migrate all files in a directory"""
        if not directory.exists():
            print(f"Directory {directory} does not exist")
            return
        
        # Find all TypeScript/TSX files
        file_patterns = ['*.ts', '*.tsx']
        files_to_process = []
        
        for pattern in file_patterns:
            files_to_process.extend(directory.rglob(pattern))
        
        print(f"Processing {len(files_to_process)} files in {directory}")
        
        for file_path in files_to_process:
            # Skip node_modules and build directories
            if 'node_modules' in str(file_path) or 'build' in str(file_path) or 'dist' in str(file_path):
                continue
            
            replacements = self.migrate_file(file_path)
            if replacements > 0:
                print(f"  ✅ {file_path.name}: {replacements} replacements")
                self.replacements_made += replacements
            
            self.files_processed += 1
    
    def run_migration(self) -> None:
        """Run the complete migration"""
        print("🎨 HMS STYLE MIGRATION SCRIPT")
        print("=" * 50)
        
        # Migrate frontend
        if self.frontend_dir.exists():
            print(f"\n📁 Migrating Frontend ({self.frontend_dir})")
            self.migrate_directory(self.frontend_dir)
        
        # Migrate mobile
        if self.mobile_dir.exists():
            print(f"\n📱 Migrating Mobile ({self.mobile_dir})")
            self.migrate_directory(self.mobile_dir)
        
        # Summary
        print(f"\n📊 MIGRATION SUMMARY")
        print("=" * 50)
        print(f"Files processed: {self.files_processed}")
        print(f"Replacements made: {self.replacements_made}")
        
        if self.replacements_made > 0:
            print(f"\n✅ Migration completed successfully!")
            print(f"🔧 Run the unified bug analyzer to verify improvements")
        else:
            print(f"\n✅ No hardcoded colors found - system is already clean!")

def main():
    """Main entry point"""
    migrator = StyleMigrator()
    migrator.run_migration()

if __name__ == '__main__':
    main()
