import { apiService } from './api'; import { userService } from './userService'; import { PaginatedResponse } from '../types/api'; export interface Notification { id: number; title: string; message: string; type: 'appointment' | 'reminder' | 'result' | 'general' | 'emergency'; read: boolean; timestamp: string; actionable?: boolean; actionText?: string; data?: any; user: number; } export interface CreateNotificationData { title: string; message: string; type: 'appointment' | 'reminder' | 'result' | 'general' | 'emergency'; actionable?: boolean; actionText?: string; data?: any; scheduled_for?: string; // For scheduled notifications } class NotificationService { /** * Get all notifications for current user */ async getMyNotifications(page = 1, pageSize = 20): Promise<PaginatedResponse<Notification>> { const currentUser = await userService.getCurrentUser(); return apiService.getPaginated<Notification>(`/notifications/notifications/?user=${currentUser.id}&page=${page}&page_size=${pageSize}`); } /** * Get unread notifications count */ async getUnreadCount(): Promise<{ count: number }> { const currentUser = await userService.getCurrentUser(); return apiService.get(`/notifications/notifications/unread-count/?user=${currentUser.id}`); } /** * Mark notification as read */ async markAsRead(id: number): Promise<Notification> { return apiService.patch<Notification>(`/notifications/notifications/${id}/`, { read: true }); } /** * Mark all notifications as read */ async markAllAsRead(): Promise<void> { const currentUser = await userService.getCurrentUser(); return apiService.post(`/notifications/notifications/mark-all-read/`, { user: currentUser.id }); } /** * Delete a notification */ async deleteNotification(id: number): Promise<void> { return apiService.delete(`/notifications/notifications/${id}/`); } /** * Delete all read notifications */ async deleteAllRead(): Promise<void> { const currentUser = await userService.getCurrentUser(); return apiService.post(`/notifications/notifications/delete-all-read/`, { user: currentUser.id }); } /** * Create a notification (for admin/system use) */ async createNotification(data: CreateNotificationData): Promise<Notification> { const currentUser = await userService.getCurrentUser(); const notificationData = { ...data, user: currentUser.id }; return apiService.post<Notification>('/notifications/notifications/', notificationData); } /** * Update notification preferences */ async updateNotificationPreferences(preferences: { appointment_reminders: boolean; medication_reminders: boolean; lab_results: boolean; general_updates: boolean; emergency_alerts: boolean; email_notifications: boolean; push_notifications: boolean; }): Promise<any> { const currentUser = await userService.getCurrentUser(); return apiService.patch(`/notifications/preferences/${currentUser.id}/`, preferences); } /** * Get notification preferences */ async getNotificationPreferences(): Promise<{ appointment_reminders: boolean; medication_reminders: boolean; lab_results: boolean; general_updates: boolean; emergency_alerts: boolean; email_notifications: boolean; push_notifications: boolean; }> { const currentUser = await userService.getCurrentUser(); return apiService.get(`/notifications/preferences/${currentUser.id}/`); } /** * Schedule a reminder notification */ async scheduleReminder(data: { title: string; message: string; scheduled_for: string; type: 'medication' | 'appointment' | 'general'; recurring?: boolean; recurring_pattern?: 'daily' | 'weekly' | 'monthly'; }): Promise<Notification> { const currentUser = await userService.getCurrentUser(); const reminderData = { ...data, user: currentUser.id, type: 'reminder' }; return apiService.post<Notification>('/notifications/reminders/', reminderData); } /** * Get scheduled reminders */ async getScheduledReminders(): Promise<PaginatedResponse<Notification>> { const currentUser = await userService.getCurrentUser(); return apiService.getPaginated<Notification>(`/notifications/reminders/?user=${currentUser.id}`); } /** * Cancel a scheduled reminder */ async cancelReminder(id: number): Promise<void> { return apiService.delete(`/notifications/reminders/${id}/`); } /** * Get notifications by type */ async getNotificationsByType(type: string, page = 1, pageSize = 20): Promise<PaginatedResponse<Notification>> { const currentUser = await userService.getCurrentUser(); return apiService.getPaginated<Notification>(`/notifications/notifications/?user=${currentUser.id}&type=${type}&page=${page}&page_size=${pageSize}`); } /** * Search notifications */ async searchNotifications(query: string, page = 1, pageSize = 20): Promise<PaginatedResponse<Notification>> { const currentUser = await userService.getCurrentUser(); return apiService.getPaginated<Notification>(`/notifications/notifications/?user=${currentUser.id}&search=${encodeURIComponent(query)}&page=${page}&page_size=${pageSize}`); } /** * Register device for push notifications */ async registerDevice(deviceToken: string, platform: 'ios' | 'android'): Promise<void> { const currentUser = await userService.getCurrentUser(); return apiService.post('/notifications/devices/', { user: currentUser.id, device_token: deviceToken, platform: platform, active: true }); } /** * Unregister device from push notifications */ async unregisterDevice(deviceToken: string): Promise<void> { return apiService.delete(`/notifications/devices/${deviceToken}/`); } /** * Test notification (for debugging) */ async sendTestNotification(): Promise<void> { const currentUser = await userService.getCurrentUser(); return apiService.post('/notifications/test/', { user: currentUser.id, title: 'Test Notification', message: 'This is a test notification from HMS Mobile App' }); } /** * Get notification statistics */ async getNotificationStats(): Promise<{ total: number; unread: number; today: number; thisWeek: number; byType: Record<string, number>; }> { const currentUser = await userService.getCurrentUser(); return apiService.get(`/notifications/notifications/stats/?user=${currentUser.id}`); } } export const notificationService = new NotificationService(); 