/** * Unified Style Utilities for HMS Application * Replaces duplicate color functions and hardcoded styles with macOS-style classes */ export type StatusType = 'completed' | 'pending' | 'cancelled' | 'active' | 'inactive' | 'scheduled' | 'in-progress' | 'confirmed' | 'urgent' | 'follow-up' | 'discontinued' | 'waiting' | 'ordered' | 'sample-collected' | 'paid' | 'partial' | 'overdue'; export type PriorityType = 'high' | 'medium' | 'low' | 'urgent' | 'normal'; // Status color mapping using unified macOS-style classes export const getStatusClass = (status: string): string => { const statusMap: Record<string, string> = { // Success statuses 'active': 'status-success-bg rounded-full px-3 py-1 text-xs font-medium', 'completed': 'status-success-bg rounded-full px-3 py-1 text-xs font-medium', 'confirmed': 'status-success-bg rounded-full px-3 py-1 text-xs font-medium', 'paid': 'status-success-bg rounded-full px-3 py-1 text-xs font-medium', // Warning statuses 'pending': 'status-warning-bg rounded-full px-3 py-1 text-xs font-medium', 'waiting': 'status-warning-bg rounded-full px-3 py-1 text-xs font-medium', 'partial': 'status-warning-bg rounded-full px-3 py-1 text-xs font-medium', 'sample-collected': 'status-warning-bg rounded-full px-3 py-1 text-xs font-medium', // Info statuses 'scheduled': 'status-info-bg rounded-full px-3 py-1 text-xs font-medium', 'in-progress': 'status-info-bg rounded-full px-3 py-1 text-xs font-medium', 'ordered': 'status-info-bg rounded-full px-3 py-1 text-xs font-medium', 'follow-up': 'status-info-bg rounded-full px-3 py-1 text-xs font-medium', // Error statuses 'cancelled': 'status-error-bg rounded-full px-3 py-1 text-xs font-medium', 'inactive': 'status-error-bg rounded-full px-3 py-1 text-xs font-medium', 'discontinued': 'status-error-bg rounded-full px-3 py-1 text-xs font-medium', 'overdue': 'status-error-bg rounded-full px-3 py-1 text-xs font-medium', 'urgent': 'status-error-bg rounded-full px-3 py-1 text-xs font-medium', }; return statusMap[status.toLowerCase()] || 'bg-muted text-muted-foreground rounded-full px-3 py-1 text-xs font-medium'; }; // Priority color mapping using unified macOS-style classes export const getPriorityClass = (priority: string): string => { const priorityMap: Record<string, string> = { 'high': 'priority-high-bg rounded-full px-3 py-1 text-xs font-medium', 'urgent': 'priority-high-bg rounded-full px-3 py-1 text-xs font-medium', 'medium': 'priority-medium-bg rounded-full px-3 py-1 text-xs font-medium', 'normal': 'priority-medium-bg rounded-full px-3 py-1 text-xs font-medium', 'low': 'priority-low-bg rounded-full px-3 py-1 text-xs font-medium', }; return priorityMap[priority.toLowerCase()] || 'priority-medium-bg rounded-full px-3 py-1 text-xs font-medium'; }; // Role color mapping export const getRoleClass = (role: string): string => { const roleMap: Record<string, string> = { 'admin': 'role-admin', 'doctor': 'role-doctor', 'nurse': 'role-nurse', 'patient': 'role-patient', 'receptionist': 'role-receptionist', }; return roleMap[role.toLowerCase()] || 'bg-muted text-muted-foreground'; }; // Severity color mapping export const getSeverityClass = (severity: string): string => { const severityMap: Record<string, string> = { 'high': 'severity-high', 'medium': 'severity-medium', 'low': 'severity-low', }; return severityMap[severity.toLowerCase()] || 'bg-muted text-muted-foreground'; }; // Feature color mapping for landing page export const getFeatureClass = (color: string): string => { const featureMap: Record<string, string> = { 'blue': 'feature-blue', 'red': 'feature-red', 'green': 'feature-green', 'purple': 'feature-purple', 'orange': 'feature-orange', 'pink': 'feature-pink', 'indigo': 'feature-indigo', 'gray': 'feature-gray', }; return featureMap[color.toLowerCase()] || 'feature-blue'; }; // Triage level color mapping for emergency export const getTriageClass = (level: string): string => { const triageMap: Record<string, string> = { 'level_1': 'status-error-bg', // Immediate - Red 'level_2': 'status-warning-bg', // Urgent - Yellow 'level_3': 'status-info-bg', // Less Urgent - Blue 'level_4': 'status-success-bg', // Non-urgent - Green 'level_5': 'bg-muted text-muted-foreground', // Clinic Care - Gray }; return triageMap[level] || 'bg-muted text-muted-foreground'; }; // Badge variant mapping export const getBadgeClass = (variant: string): string => { const badgeMap: Record<string, string> = { 'default': 'badge-default', 'primary': 'badge-primary', 'success': 'badge-success', 'warning': 'badge-warning', 'error': 'badge-error', 'info': 'badge-info', }; return badgeMap[variant.toLowerCase()] || 'badge-default'; }; // Button variant mapping using macOS-style classes export const getButtonClass = ( variant: 'default' | 'glass' | 'accent' | 'ghost' | 'destructive' = 'default', size: 'sm' | 'md' | 'lg' = 'md' ): string => { const baseClass = 'inline-flex items-center justify-center rounded-md font-medium macos-transition macos-focus-ring'; const variants = { default: 'bg-primary text-primary-foreground hover:bg-primary/90', glass: 'macos-button', accent: 'macos-button macos-accent-bg text-white', ghost: 'hover:bg-accent hover:text-accent-foreground', destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90', }; const sizes = { sm: 'h-8 px-3 text-sm', md: 'h-10 px-4 py-2', lg: 'h-12 px-6 text-lg', }; return `${baseClass} ${variants[variant]} ${sizes[size]}`; }; // Card variant mapping using macOS-style classes export const getCardClass = (variant: 'default' | 'glass' | 'elevated' = 'default'): string => { const cardMap: Record<string, string> = { 'default': 'bg-card text-card-foreground border border-border rounded-lg shadow-sm', 'glass': 'macos-card', 'elevated': 'macos-card shadow-elevated', }; return cardMap[variant] || cardMap.default; }; // Text variant mapping using macOS-style classes export const getTextClass = (variant: 'primary' | 'secondary' | 'tertiary' | 'accent' = 'primary'): string => { const textMap: Record<string, string> = { 'primary': 'macos-text-primary', 'secondary': 'macos-text-secondary', 'tertiary': 'macos-text-tertiary', 'accent': 'macos-accent-text', }; return textMap[variant] || textMap.primary; }; // Avatar size mapping export const getAvatarClass = (size: string, gradient: boolean = false): string => { const sizeMap: Record<string, string> = { 'sm': 'avatar-sm', 'md': 'avatar-md', 'lg': 'avatar-lg', 'xl': 'avatar-xl', }; const baseClass = sizeMap[size.toLowerCase()] || 'avatar-md'; return gradient ? `${baseClass} avatar-gradient` : baseClass; }; // Navigation item state mapping export const getNavClass = (isActive: boolean): string => { return isActive ? 'nav-item-active' : 'nav-item-inactive'; }; // Sidebar navigation (for glass backgrounds) export const getSidebarNavClass = (isActive: boolean): string => { return isActive ? 'sidebar-nav-active' : 'sidebar-nav-inactive'; }; // Tab item state mapping export const getTabClass = (isActive: boolean): string => { return isActive ? 'tab-item-active' : 'tab-item-inactive'; }; // Selection state mapping export const getSelectableClass = (isSelected: boolean): string => { return isSelected ? 'selectable-active' : 'selectable-inactive'; }; // Form element classes export const getFormClasses = () => ({ group: 'form-group', label: 'form-label', description: 'form-description', error: 'form-error', input: 'input-base', inputError: 'input-error', }); // Layout utility classes export const getLayoutClasses = () => ({ containerPadding: 'container-padding', sectionSpacing: 'section-spacing', gridResponsive: 'grid-responsive', interactive: 'interactive', }); // Notification classes export const getNotificationClass = (type: string): string => { const notificationMap: Record<string, string> = { 'success': 'notification-success', 'warning': 'notification-warning', 'error': 'notification-error', 'info': 'notification-info', }; return notificationMap[type.toLowerCase()] || 'notification-info'; }; 