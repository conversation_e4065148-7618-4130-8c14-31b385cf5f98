import { createSlice } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; export type ThemeMode = 'light' | 'dark' | 'system'; export type GlassmorphismLevel = 'none' | 'subtle' | 'medium' | 'strong'; interface ThemeState { mode: ThemeMode; glassmorphismLevel: GlassmorphismLevel; reducedMotion: boolean; systemTheme: 'light' | 'dark'; effectiveTheme: 'light' | 'dark'; } const getSystemTheme = (): 'light' | 'dark' => { if (typeof window !== 'undefined' && window.matchMedia) { return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'; } return 'light'; }; const getEffectiveTheme = (mode: ThemeMode, systemTheme: 'light' | 'dark'): 'light' | 'dark' => { if (mode === 'system') { return systemTheme; } return mode; }; const initialState: ThemeState = { mode: 'system', glassmorphismLevel: 'medium', reducedMotion: false, systemTheme: getSystemTheme(), effectiveTheme: getEffectiveTheme('system', getSystemTheme()), }; const themeSlice = createSlice({ name: 'theme', initialState, reducers: { setThemeMode: (state, action: PayloadAction<ThemeMode>) => { state.mode = action.payload; state.effectiveTheme = getEffectiveTheme(action.payload, state.systemTheme); }, setGlassmorphismLevel: (state, action: PayloadAction<GlassmorphismLevel>) => { state.glassmorphismLevel = action.payload; }, setReducedMotion: (state, action: PayloadAction<boolean>) => { state.reducedMotion = action.payload; }, updateSystemTheme: (state, action: PayloadAction<'light' | 'dark'>) => { state.systemTheme = action.payload; state.effectiveTheme = getEffectiveTheme(state.mode, action.payload); }, toggleTheme: (state) => { if (state.mode === 'light') { state.mode = 'dark'; } else if (state.mode === 'dark') { state.mode = 'system'; } else { state.mode = 'light'; } state.effectiveTheme = getEffectiveTheme(state.mode, state.systemTheme); }, }, }); export const { setThemeMode, setGlassmorphismLevel, setReducedMotion, updateSystemTheme, toggleTheme, } = themeSlice.actions; export default themeSlice.reducer; 