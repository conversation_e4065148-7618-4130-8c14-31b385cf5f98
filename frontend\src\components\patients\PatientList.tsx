import React, { useState, useEffect } from 'react'; import { useTranslation } from 'react-i18next'; import { Button } from '../ui/Button'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import { useTheme } from '../../hooks/useTheme'; import DataTable from '../ui/DataTable'; import { Search, Plus, Filter, Users, Download, Eye, UserPlus, Calendar, AlertTriangle, Edit, Trash2, Loader2 } from 'lucide-react'; import { patientAPI } from '../../utils/api'; import CRUDModal from '../ui/CRUDModal'; import { crudService } from '../../services/crudService'; interface Patient { id: number; patient_id: string; user: { id: number; full_name: string; email: string; phone_number?: string; }; blood_group?: string; insurance_provider?: string; created_at: string; } const PatientList: React.FC = () => { const { t } = useTranslation(); const { isDark } = useTheme(); const [patients, setPatients] = useState<Patient[]>([]); const [loading, setLoading] = useState(false); const [selectedPatients, setSelectedPatients] = useState<string[]>([]); const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0, }); // Modal state const [showModal, setShowModal] = useState(false); const [modalMode, setModalMode] = useState<'create' | 'edit'>('create'); const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null); const [actionLoading, setActionLoading] = useState<number | null>(null); // Fetch patients from API const fetchPatients = async (params?: any) => { setLoading(true); try { const response = await patientAPI.getPatients({ page: pagination.current, page_size: pagination.pageSize, ...params }); // Ensure we always have a valid array let patientsData: Patient[] = []; if (response && typeof response === 'object') { if (Array.isArray(response.results)) { patientsData = response.results; } else if (Array.isArray(response)) { patientsData = response; } } setPatients(patientsData); setPagination(prev => ({ ...prev, total: (response && response.count) || patientsData.length || 0 })); } catch (error) { console.error('Failed to fetch patients:', error); // Fallback to mock data if API fails const mockPatients: Patient[] = [ { id: 1, patient_id: 'P000001', user: { id: 1, full_name: 'John Doe', email: '<EMAIL>', phone_number: '+**********', }, blood_group: 'A+', insurance_provider: 'Blue Cross', created_at: '2024-01-15T10:30:00Z', }, { id: 2, patient_id: 'P000002', user: { id: 2, full_name: 'Jane Smith', email: '<EMAIL>', phone_number: '+**********', }, blood_group: 'O-', insurance_provider: 'Aetna', created_at: '2024-01-16T14:20:00Z', }, ]; setPatients(mockPatients); setPagination(prev => ({ ...prev, total: mockPatients.length })); } finally { setLoading(false); } }; useEffect(() => { fetchPatients(); }, [pagination.current, pagination.pageSize]); const columns = [ { key: 'patient_id', title: 'Patient ID', sortable: true, render: (value: string, record: Patient) => ( <div className="font-medium text-sky-700 dark:text-sky-400 dark:text-blue-400">{value}</div> ), }, { key: 'user.full_name', title: 'Full Name', sortable: true, render: (value: string, record: Patient) => ( <div> <div className="font-medium macos-text-primary">{value}</div> <div className="text-sm macos-text-secondary">{record.user.email}</div> </div> ), }, { key: 'user.phone_number', title: 'Phone', render: (value: string) => value || 'N/A', }, { key: 'blood_group', title: 'Blood Group', render: (value: string) => ( <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${ value ? 'status-error ' : 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300' }`}> {value || 'Unknown'} </span> ), }, { key: 'insurance_provider', title: 'Insurance', render: (value: string) => value || 'None', }, { key: 'created_at', title: 'Registered', sortable: true, render: (value: string) => new Date(value).toLocaleDateString(), }, ]; const handleSearch = (searchTerm: string) => { fetchPatients({ search: searchTerm }); }; const handleSort = (key: string, direction: 'asc' | 'desc') => { const ordering = direction === 'desc' ? `-${key}` : key; fetchPatients({ ordering }); }; const handlePaginationChange = (page: number, pageSize: number) => { setPagination(prev => ({ ...prev, current: page, pageSize })); }; // CRUD Handlers const handleCreatePatient = () => { setSelectedPatient(null); setModalMode('create'); setShowModal(true); }; const handleViewPatient = (patient: Patient) => { setSelectedPatient(patient); setModalMode('edit'); setShowModal(true); }; const handleEditPatient = (patient: Patient) => { setSelectedPatient(patient); setModalMode('edit'); setShowModal(true); }; const handleSubmitPatient = async (patientData: any) => { try { if (modalMode === 'create') { await crudService.createPatient(patientData); } else if (selectedPatient) { await crudService.updatePatient(selectedPatient.id, patientData); } await fetchPatients(); } catch (error: any) { console.error('Failed to save patient:', error); if (error.message === 'Session expired. Please login again.' || error.message === 'Not authenticated. Please login first.') { throw new Error('Your session has expired. Please login again.'); } throw error; } }; const handleDeletePatient = async (patient: Patient) => { if (window.confirm(`Are you sure you want to delete patient ${patient.user.full_name}?`)) { setActionLoading(patient.id); try { await crudService.deletePatient(patient.id); await fetchPatients(); } catch (error: any) { console.error('Failed to delete patient:', error); if (error.message === 'Session expired. Please login again.' || error.message === 'Not authenticated. Please login first.') { alert('Your session has expired. Please login again.'); } else { alert('Failed to delete patient. Please try again.'); } } finally { setActionLoading(null); } } }; // Form fields for patient modal const patientFormFields = [ { key: 'full_name', label: 'Full Name', type: 'text' as const, required: true }, { key: 'email', label: 'Email', type: 'email' as const, required: true }, { key: 'phone_number', label: 'Phone Number', type: 'text' as const }, { key: 'date_of_birth', label: 'Date of Birth', type: 'date' as const }, { key: 'blood_group', label: 'Blood Group', type: 'select' as const, options: [ { value: 'A+', label: 'A+' }, { value: 'A-', label: 'A-' }, { value: 'B+', label: 'B+' }, { value: 'B-', label: 'B-' }, { value: 'AB+', label: 'AB+' }, { value: 'AB-', label: 'AB-' }, { value: 'O+', label: 'O+' }, { value: 'O-', label: 'O-' } ] }, { key: 'insurance_provider', label: 'Insurance Provider', type: 'text' as const }, { key: 'emergency_contact', label: 'Emergency Contact', type: 'text' as const }, { key: 'address', label: 'Address', type: 'textarea' as const }, { key: 'medical_history', label: 'Medical History', type: 'textarea' as const }, { key: 'allergies', label: 'Allergies', type: 'textarea' as const } ]; const renderActions = (record: Patient) => ( <div className="flex items-center space-x-2"> <Button size="sm" variant="glass" onClick={() => handleViewPatient(record)} > <Eye className="w-4 h-4 mr-1" /> {t('common.view')} </Button> <Button size="sm" variant="glass" onClick={() => handleEditPatient(record)} > <Edit className="w-4 h-4 mr-1" /> {t('common.edit')} </Button> <Button size="sm" variant="destructive" onClick={() => handleDeletePatient(record)} disabled={actionLoading === record.id} > {actionLoading === record.id ? ( <Loader2 className="w-4 h-4 mr-1 animate-spin" /> ) : ( <Trash2 className="w-4 h-4 mr-1" /> )} {t('common.delete')} </Button> </div> ); const handleBulkAction = (action: string) => { // TODO: Handle action // TODO: Implement bulk actions }; return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */} <Card className="glass border-0 shadow-xl"> <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-3"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg"> <Users className="w-6 h-6 text-white" /> </div> <div> <CardTitle className="text-2xl font-bold macos-text-primary"> {t('patients.title')} </CardTitle> <CardDescription className="macos-text-secondary"> {t('patients.subtitle')} </CardDescription> </div> </div> <div className="flex items-center space-x-3"> {selectedPatients.length > 0 && ( <div className="flex items-center space-x-2"> <Button variant="glass" size="sm" onClick={() => handleBulkAction('export')} className="flex items-center gap-2" > <Download className="w-4 h-4" /> {t('patients.exportSelected')} </Button> <Button variant="destructive" size="sm" onClick={() => handleBulkAction('delete')} > {t('patients.deleteSelected')} </Button> </div> )} <Button variant="glass" onClick={handleCreatePatient} className="flex items-center gap-2" > <UserPlus className="w-4 h-4" /> {t('patients.addNewPatient')} </Button> </div> </div> </CardHeader> </Card> {/* Statistics Cards */} <div className="grid grid-cols-1 md:grid-cols-4 gap-6"> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"> <Users className="w-6 h-6 text-white" /> </div> <div className="ml-4"> <p className="text-sm font-medium macos-text-secondary">{t('patients.totalPatients')}</p> <p className="text-2xl font-bold macos-text-primary">{pagination.total}</p> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center"> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg"> <Calendar className="w-6 h-6 text-white" /> </div> <div className="ml-4"> <p className="text-sm font-medium macos-text-secondary">{t('patients.activeToday')}</p> <p className="text-2xl font-bold macos-text-primary">12</p> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center"> <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg"> <Calendar className="w-6 h-6 text-white" /> </div> <div className="ml-4"> <p className="text-sm font-medium macos-text-secondary">{t('patients.pendingAppointments')}</p> <p className="text-2xl font-bold macos-text-primary">8</p> </div> </div> </CardContent> </Card> <Card className="glass border-0 shadow-lg"> <CardContent className="p-6"> <div className="flex items-center"> <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg"> <AlertTriangle className="w-6 h-6 text-white" /> </div> <div className="ml-4"> <p className="text-sm font-medium macos-text-secondary">{t('patients.criticalCases')}</p> <p className="text-2xl font-bold macos-text-primary">3</p> </div> </div> </CardContent> </Card> </div> {/* Patient Table */} <Card className="glass border-0 shadow-xl"> <CardHeader> <CardTitle className="flex items-center gap-2 macos-text-primary"> <Users className="w-5 h-5" /> {t('patients.patientList')} </CardTitle> <CardDescription className="macos-text-secondary"> {t('patients.managePatientRecords')} </CardDescription> </CardHeader> <CardContent> <DataTable data={patients} columns={columns} loading={loading} pagination={{ ...pagination, onChange: handlePaginationChange, }} onSearch={handleSearch} onSort={handleSort} actions={{ title: t('common.actions'), render: renderActions, }} selectable={{ selectedRowKeys: selectedPatients, onChange: setSelectedPatients, getRowKey: (record) => record.id.toString(), }} /> </CardContent> </Card> {/* CRUD Modal */} <CRUDModal isOpen={showModal} onClose={() => setShowModal(false)} onSubmit={handleSubmitPatient} title={modalMode === 'create' ? 'Add New Patient' : 'Edit Patient'} fields={patientFormFields} initialData={selectedPatient ? { full_name: selectedPatient.user.full_name, email: selectedPatient.user.email, phone_number: selectedPatient.user.phone_number, blood_group: selectedPatient.blood_group, insurance_provider: selectedPatient.insurance_provider } : {}} mode={modalMode} /> </div> </div> ); }; export default PatientList; 