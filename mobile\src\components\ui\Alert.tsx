import React from 'react'; import { View, Text, TouchableOpacity } from 'react-native'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; interface AlertProps { title?: string; message: string; variant?: 'info' | 'success' | 'warning' | 'error'; dismissible?: boolean; onDismiss?: () => void; className?: string; } const Alert: React.FC<AlertProps> = ({ title, message, variant = 'info', dismissible = false, onDismiss, className = '', }) => { const { isDark } = useTheme(); const getVariantStyles = () => { switch (variant) { case 'success': return 'bg-emerald-50/80 border-emerald-200/50'; case 'warning': return 'bg-amber-50/80 border-amber-200/50'; case 'error': return 'bg-rose-50/80 border-rose-200/50'; default: return 'bg-sky-50/80 border-sky-200/50'; } }; const getIconName = (): keyof typeof Ionicons.glyphMap => { switch (variant) { case 'success': return 'checkmark-circle'; case 'warning': return 'warning'; case 'error': return 'alert-circle'; default: return 'information-circle'; } }; const getIconColor = () => { switch (variant) { case 'success': return '#16A34A'; case 'warning': return '#D97706'; case 'error': return '#DC2626'; default: return '#2563EB'; } }; const getTitleTextColor = () => { switch (variant) { case 'success': return 'text-emerald-700 dark:text-emerald-400'; case 'warning': return 'text-amber-700 dark:text-amber-400'; case 'error': return 'text-rose-700 dark:text-rose-400'; default: return 'text-sky-700 dark:text-sky-400'; } }; const getMessageTextColor = () => { switch (variant) { case 'success': return 'text-emerald-700 dark:text-emerald-400'; case 'warning': return 'text-amber-700 dark:text-amber-400'; case 'error': return 'text-rose-700 dark:text-rose-400'; default: return 'text-sky-700 dark:text-sky-400'; } }; return ( <View className={`rounded-xl border p-4 ${getVariantStyles()} ${className}`}> <View className="flex-row"> <Ionicons name={getIconName()} size={20} color={getIconColor()} style={{ marginRight: 12, marginTop: 2 }} /> <View className="flex-1"> {title && ( <Text className={`font-semibold text-base mb-1 ${getTitleTextColor()}`}> {title} </Text> )} <Text className={`text-sm ${getMessageTextColor()}`}> {message} </Text> </View> {dismissible && onDismiss && ( <TouchableOpacity onPress={onDismiss} className="ml-2" > <Ionicons name="close" size={20} color={getIconColor()} /> </TouchableOpacity> )} </View> </View> ); }; export default Alert; 