import React, { useState, useEffect } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, TextInput, Alert, KeyboardAvoidingView, Platform } from 'react-native'; import { useNavigation, useRoute } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { RootState } from '../../store'; import { medicalRecordService, UpdateMedicalRecordData } from '../../services/medicalRecordService'; import { fetchMyMedicalRecords } from '../../store/slices/patientSlice'; import { useTranslation } from '../../hooks/useTranslation'; const EditMedicalRecordScreen: React.FC = () => { const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const route = useRoute(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { recordId } = route.params; const { medicalRecords } = useSelector((state: RootState) => state.patient); const [record, setRecord] = useState<any>(null); const [formData, setFormData] = useState<UpdateMedicalRecordData>({ diagnosis: '', symptoms: '', treatment: '', prescription: '', notes: '', visit_date: '', doctor_name: '', follow_up_date: '', }); const [isLoading, setIsLoading] = useState(false); const [errors, setErrors] = useState<Record<string, string>>({}); useEffect(() => { // Find the record from the store const foundRecord = medicalRecords.find(r => r.id === recordId); if (foundRecord) { setRecord(foundRecord); setFormData({ diagnosis: foundRecord.diagnosis || '', symptoms: foundRecord.symptoms || '', treatment: foundRecord.treatment || '', prescription: foundRecord.prescription || '', notes: foundRecord.notes || '', visit_date: foundRecord.visit_date || '', doctor_name: foundRecord.doctor_name || '', follow_up_date: foundRecord.follow_up_date || '', }); } }, [recordId, medicalRecords]); const handleInputChange = (field: keyof UpdateMedicalRecordData, value: string) => { setFormData(prev => ({ ...prev, [field]: value })); // Clear error when user starts typing if (errors[field]) { setErrors(prev => ({ ...prev, [field]: '' })); } }; const validateForm = (): boolean => { const newErrors: Record<string, string> = {}; if (!formData.diagnosis?.trim()) { newErrors.diagnosis = 'Diagnosis is required'; } if (!formData.symptoms?.trim()) { newErrors.symptoms = 'Symptoms are required'; } if (!formData.treatment?.trim()) { newErrors.treatment = 'Treatment is required'; } if (!formData.visit_date?.trim()) { newErrors.visit_date = 'Visit date is required'; } setErrors(newErrors); return Object.keys(newErrors).length === 0; }; const handleSubmit = async () => { if (!validateForm()) { return; } setIsLoading(true); try { await medicalRecordService.updateMedicalRecord(recordId, formData); // Refresh the medical records list await dispatch(fetchMyMedicalRecords({ page: 1, pageSize: 50 })); Alert.alert( 'Success', 'Medical record updated successfully!', [ { text: 'OK', onPress: () => navigation.goBack() } ] ); } catch (error: any) { Alert.alert('Error', error.message || 'Failed to update medical record'); } finally { setIsLoading(false); } }; if (!record) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <View className="flex-1 justify-center items-center px-6"> <Ionicons name="document-text-outline" size={64} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-xl font-bold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Record Not Found </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> The requested medical record could not be found. </Text> </View> </SafeAreaView> ); } return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1" > {/* Header */} <View className="flex-row items-center px-6 py-4 border-b border-border"> <TouchableOpacity onPress={() => navigation.goBack()}> <Ionicons name="arrow-back" size={24} color={isDark ? '#FFFFFF' : '#000000'} /> </TouchableOpacity> <Text className={`text-xl font-bold ml-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Edit Medical Record </Text> </View> <ScrollView className="flex-1 px-6 py-4" showsVerticalScrollIndicator={false}> {/* Record Info */} <View className={`p-4 rounded-xl mb-6 ${isDark ? 'bg-dark-card' : 'bg-blue-50'}`}> <Text className={`text-sm font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Editing Record #{record.id} </Text> <Text className={`text-xs mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Created: {new Date(record.created_at).toLocaleDateString()} </Text> </View> {/* Diagnosis */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Diagnosis * </Text> <TextInput value={formData.diagnosis} onChangeText={(value) => handleInputChange('diagnosis', value)} placeholder="Enter diagnosis" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ errors.diagnosis ? 'border-red-500' : isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} multiline numberOfLines={3} /> {errors.diagnosis && ( <Text className="text-red-500 text-sm mt-1">{errors.diagnosis}</Text> )} </View> {/* Symptoms */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Symptoms * </Text> <TextInput value={formData.symptoms} onChangeText={(value) => handleInputChange('symptoms', value)} placeholder="Enter symptoms" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ errors.symptoms ? 'border-red-500' : isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} multiline numberOfLines={3} /> {errors.symptoms && ( <Text className="text-red-500 text-sm mt-1">{errors.symptoms}</Text> )} </View> {/* Treatment */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Treatment * </Text> <TextInput value={formData.treatment} onChangeText={(value) => handleInputChange('treatment', value)} placeholder="Enter treatment" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ errors.treatment ? 'border-red-500' : isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} multiline numberOfLines={3} /> {errors.treatment && ( <Text className="text-red-500 text-sm mt-1">{errors.treatment}</Text> )} </View> {/* Prescription */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Prescription </Text> <TextInput value={formData.prescription} onChangeText={(value) => handleInputChange('prescription', value)} placeholder="Enter prescription (optional)" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} multiline numberOfLines={2} /> </View> {/* Doctor Name */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Doctor Name </Text> <TextInput value={formData.doctor_name} onChangeText={(value) => handleInputChange('doctor_name', value)} placeholder="Enter doctor name (optional)" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} /> </View> {/* Visit Date */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Visit Date * </Text> <TextInput value={formData.visit_date} onChangeText={(value) => handleInputChange('visit_date', value)} placeholder="YYYY-MM-DD" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ errors.visit_date ? 'border-red-500' : isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} /> {errors.visit_date && ( <Text className="text-red-500 text-sm mt-1">{errors.visit_date}</Text> )} </View> {/* Follow-up Date */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Follow-up Date </Text> <TextInput value={formData.follow_up_date} onChangeText={(value) => handleInputChange('follow_up_date', value)} placeholder="YYYY-MM-DD (optional)" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} /> </View> {/* Notes */} <View className="mb-6"> <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Additional Notes </Text> <TextInput value={formData.notes} onChangeText={(value) => handleInputChange('notes', value)} placeholder="Enter additional notes (optional)" placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`p-4 rounded-xl border ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} multiline numberOfLines={4} /> </View> {/* Submit Button */} <TouchableOpacity onPress={handleSubmit} disabled={isLoading} className={`py-4 px-6 rounded-xl mb-8 ${ isLoading ? 'bg-gray-400' : 'bg-primary-600' }`} > <Text className="text-white text-center font-semibold text-lg"> {isLoading ? 'Updating...' : 'Update Medical Record'} </Text> </TouchableOpacity> </ScrollView> </KeyboardAvoidingView> </SafeAreaView> ); }; export default EditMedicalRecordScreen; 