import { apiService } from './api'; /** * User service for getting current user information */ class UserService { private currentPatientCache: any = null; private cacheExpiry: number = 0; private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes /** * Get current user profile from auth */ async getCurrentUser(): Promise<any> { return apiService.get('/auth/profile/'); } /** * Get current user's patient profile * This method caches the result to avoid repeated API calls */ async getCurrentPatient(): Promise<any> { // Check if we have a valid cached result if (this.currentPatientCache && Date.now() < this.cacheExpiry) { return this.currentPatientCache; } try { // Get current user info const userProfile = await this.getCurrentUser(); // Get all patients and find the one for current user const patientsResponse = await apiService.get('/patients/patients/'); const patients = (patientsResponse as any)?.results || patientsResponse; const currentPatient = patients.find((p: any) => p.user === userProfile.id); if (!currentPatient) { throw new Error('Patient profile not found for current user'); } // Cache the result this.currentPatientCache = currentPatient; this.cacheExpiry = Date.now() + this.CACHE_DURATION; return currentPatient; } catch (error) { console.error('Failed to get current patient:', error); throw error; } } /** * Clear the patient cache (call this after profile updates) */ clearPatientCache(): void { this.currentPatientCache = null; this.cacheExpiry = 0; } /** * Check if current user has patient profile */ async hasPatientProfile(): Promise<boolean> { try { await this.getCurrentPatient(); return true; } catch (error) { return false; } } /** * Get current user's role */ async getCurrentUserRole(): Promise<string> { const user = await this.getCurrentUser(); return user.role || 'patient'; } /** * Check if current user is a patient */ async isPatient(): Promise<boolean> { const role = await this.getCurrentUserRole(); return role === 'patient'; } /** * Check if current user is a doctor */ async isDoctor(): Promise<boolean> { const role = await this.getCurrentUserRole(); return role === 'doctor'; } /** * Check if current user is admin */ async isAdmin(): Promise<boolean> { const role = await this.getCurrentUserRole(); return role === 'admin'; } } export const userService = new UserService(); 