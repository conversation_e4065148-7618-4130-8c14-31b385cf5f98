import React, { useState, useEffect } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl, Alert } from 'react-native'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { DateTimeUtils } from '../../utils/dateTime'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; import { useTranslation } from '../../hooks/useTranslation'; interface Notification { id: number; title: string; message: string; type: 'appointment' | 'reminder' | 'result' | 'general' | 'emergency'; read: boolean; timestamp: string; actionable?: boolean; actionText?: string; data?: any; } // Mock notifications data const mockNotifications: Notification[] = [ { id: 1, title: 'Appointment Reminder', message: 'Your appointment with Dr. <PERSON> is tomorrow at 2:00 PM', type: 'appointment', read: false, timestamp: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), actionable: true, actionText: 'View Details', data: { appointmentId: 1 } }, { id: 2, title: 'Lab Results Available', message: 'Your blood test results are now available for review', type: 'result', read: false, timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), actionable: true, actionText: 'View Results', data: { resultId: 1 } }, { id: 3, title: 'Medication Reminder', message: 'Time to take your prescribed medication - Aspirin 75mg', type: 'reminder', read: true, timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), actionable: true, actionText: 'Mark as Taken' }, { id: 4, title: 'Appointment Confirmed', message: 'Your appointment with Dr. Sarah Johnson has been confirmed for next week', type: 'appointment', read: true, timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), actionable: false }, { id: 5, title: 'Health Tip', message: 'Remember to stay hydrated! Aim for 8 glasses of water daily.', type: 'general', read: true, timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), actionable: false }, { id: 6, title: 'Emergency Alert', message: 'HMS Medical Center will be closed for emergency maintenance on Sunday', type: 'emergency', read: false, timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), actionable: false } ]; const NotificationsScreen: React.FC = () => { const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const [notifications, setNotifications] = useState<Notification[]>(mockNotifications); const [filter, setFilter] = useState<'all' | 'unread' | 'appointments' | 'results'>('all'); const [loading, setLoading] = useState(false); const [refreshing, setRefreshing] = useState(false); const getFilteredNotifications = () => { switch (filter) { case 'unread': return notifications.filter(n => !n.read); case 'appointments': return notifications.filter(n => n.type === 'appointment'); case 'results': return notifications.filter(n => n.type === 'result'); default: return notifications; } }; const unreadCount = notifications.filter(n => !n.read).length; const filteredNotifications = getFilteredNotifications(); const onRefresh = async () => { setRefreshing(true); // Simulate API call setTimeout(() => { setRefreshing(false); }, 1000); }; const markAsRead = (notificationId: number) => { setNotifications(prev => prev.map(n => n.id === notificationId ? { ...n, read: true } : n ) ); }; const markAllAsRead = () => { setNotifications(prev => prev.map(n => ({ ...n, read: true })) ); }; const deleteNotification = (notificationId: number) => { Alert.alert( 'Delete Notification', 'Are you sure you want to delete this notification?', [ { text: 'Cancel', style: 'cancel' }, { text: 'Delete', style: 'destructive', onPress: () => { setNotifications(prev => prev.filter(n => n.id !== notificationId) ); } } ] ); }; const handleNotificationAction = (notification: Notification) => { if (!notification.actionable) return; switch (notification.type) { case 'appointment': Alert.alert('Appointment Action', 'Navigate to appointment details'); break; case 'result': Alert.alert('Lab Results', 'Navigate to lab results'); break; case 'reminder': Alert.alert('Medication Taken', 'Medication marked as taken'); break; default: break; } markAsRead(notification.id); }; const getNotificationIcon = (type: string): keyof typeof Ionicons.glyphMap => { switch (type) { case 'appointment': return 'calendar'; case 'reminder': return 'alarm'; case 'result': return 'document-text'; case 'emergency': return 'warning'; default: return 'notifications'; } }; const getNotificationColor = (type: string) => { switch (type) { case 'appointment': return '#007AFF'; case 'reminder': return '#FF9500'; case 'result': return '#34C759'; case 'emergency': return '#FF3B30'; default: return '#8E8E93'; } }; const renderFilterTabs = () => ( <View className="px-6 mb-4"> <View className={`flex-row rounded-lg p-1 ${isDark ? 'bg-dark-card' : 'bg-muted'} ${isRTL ? 'flex-row-reverse' : ''}`}> {(['all', 'unread', 'appointments', 'results'] as const).map((filterOption) => ( <TouchableOpacity key={filterOption} onPress={() => setFilter(filterOption)} className={`flex-1 py-2 px-3 rounded-md ${ filter === filterOption ? 'bg-primary-600' : 'bg-transparent' }`} > <Text className={`text-center font-medium text-sm ${ filter === filterOption ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {filterOption === 'all' ? 'All' : filterOption === 'unread' ? 'Unread' : filterOption === 'appointments' ? 'Appointments' : 'Results'} </Text> </TouchableOpacity> ))} </View> </View> ); const renderNotification = (notification: Notification) => ( <TouchableOpacity key={notification.id} onPress={() => markAsRead(notification.id)} onLongPress={() => deleteNotification(notification.id)} > <Card className={`mb-3 ${!notification.read ? 'border-l-4 border-l-primary-600' : ''}`}> <View className="flex-row"> <View className={`w-10 h-10 rounded-full mr-3 items-center justify-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name={getNotificationIcon(notification.type)} size={20} color={getNotificationColor(notification.type)} /> </View> <View className="flex-1"> <View className="flex-row justify-between items-start mb-1"> <Text className={`font-semibold text-base flex-1 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {notification.title} </Text> {!notification.read && ( <View className="w-2 h-2 bg-primary-600 rounded-full ml-2 mt-1" /> )} </View> <Text className={`text-sm mb-2 ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {notification.message} </Text> <View className="flex-row justify-between items-center"> <Text className={`text-xs ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {DateTimeUtils.getRelativeTime(notification.timestamp)} </Text> {notification.actionable && ( <TouchableOpacity onPress={() => handleNotificationAction(notification)} className="bg-primary-600 px-3 py-1 rounded-full" > <Text className="text-white text-xs font-medium"> {notification.actionText} </Text> </TouchableOpacity> )} </View> </View> </View> </Card> </TouchableOpacity> ); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <View className="flex-row justify-between items-center"> <View> <Text className={`text-2xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Notifications </Text> {unreadCount > 0 && ( <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''} </Text> )} </View> {unreadCount > 0 && ( <TouchableOpacity onPress={markAllAsRead} className="bg-primary-600 px-4 py-2 rounded-lg" > <Text className="text-white font-medium text-sm">Mark All Read</Text> </TouchableOpacity> )} </View> </View> {renderFilterTabs()} {/* Notifications List */} <ScrollView className="flex-1 px-6" refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } showsVerticalScrollIndicator={false} > {loading ? ( <LoadingSpinner message="Loading notifications..." /> ) : filteredNotifications.length > 0 ? ( <View className="pb-6"> {filteredNotifications.map(renderNotification)} </View> ) : ( <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm`}> <View className="items-center"> <Ionicons name="notifications-outline" size={48} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-lg font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> No notifications </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {filter === 'unread' ? "You're all caught up! No unread notifications." : filter === 'appointments' ? "No appointment notifications at the moment." : filter === 'results' ? "No lab result notifications available." : "You don't have any notifications yet." } </Text> </View> </View> )} </ScrollView> </SafeAreaView> ); }; export default NotificationsScreen; 