import React, { useState, useEffect } from 'react'; import { useTranslation } from 'react-i18next'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import { Button } from '../ui/Button'; import { Badge } from '../ui/badge'; import { Input } from '../ui/Input'; import DataTable from '../ui/DataTable'; import { AlertTriangle, Phone, Plus, Search, Filter, Eye, Edit, Activity, Clock, Users, Heart, Stethoscope } from 'lucide-react'; import { emergencyService } from '../../services/emergencyService'; interface EmergencyCase { id: number; case_id: string; patient: { patient_id: string; user: { full_name: string; }; }; attending_doctor: { full_name: string; }; arrival_time: string; chief_complaint: string; triage_level: string; status: string; disposition?: string; } interface EmergencyContact { id: number; name: string; organization: string; phone_primary: string; contact_type: string; is_active: boolean; priority: number; } const EmergencyManagement: React.FC = () => { const { t } = useTranslation(); const [activeTab, setActiveTab] = useState<'cases' | 'contacts' | 'trauma' | 'code-blue'>('cases'); const [cases, setCases] = useState<EmergencyCase[]>([]); const [contacts, setContacts] = useState<EmergencyContact[]>([]); const [loading, setLoading] = useState(false); const [searchTerm, setSearchTerm] = useState(''); const [selectedFilter, setSelectedFilter] = useState('all'); useEffect(() => { loadData(); }, [activeTab]); const loadData = async () => { setLoading(true); try { if (activeTab === 'cases') { const response = await emergencyService.getAll(); setCases(response.data.results || response.data); } else if (activeTab === 'contacts') { const response = await emergencyService.getContacts(); setContacts(response.data.results || response.data); } } catch (error) { console.error('Error loading emergency data:', error); } finally { setLoading(false); } }; const getTriageLevelColor = (level: string) => { switch (level) { case 'level_1': return 'status-error '; case 'level_2': return 'bg-orange-100 text-orange-800 border-orange-200'; case 'level_3': return 'status-warning '; case 'level_4': return 'status-success '; case 'level_5': return 'status-info '; default: return 'bg-muted text-foreground border-border'; } }; const getTriageLevelText = (level: string) => { switch (level) { case 'level_1': return 'Level 1 - Immediate'; case 'level_2': return 'Level 2 - Urgent'; case 'level_3': return 'Level 3 - Less Urgent'; case 'level_4': return 'Level 4 - Non-Urgent'; case 'level_5': return 'Level 5 - Clinic Care'; default: return level; } }; const getStatusColor = (status: string) => { switch (status) { case 'active': return 'status-success '; case 'completed': return 'status-info '; case 'transferred': return 'bg-purple-100 text-purple-800 border-purple-200'; case 'cancelled': return 'status-error '; default: return 'bg-muted text-foreground border-border'; } }; const caseColumns = [ { key: 'case_id', title: 'Case ID', render: (value: string) => ( <span className="font-mono text-sm font-medium">{value}</span> ) }, { key: 'patient', title: 'Patient', render: (patient: any) => ( <div> <div className="font-medium">{patient.user.full_name}</div> <div className="text-sm text-gray-500">{patient.patient_id}</div> </div> ) }, { key: 'chief_complaint', title: 'Chief Complaint', render: (value: string) => ( <span className="text-sm">{value}</span> ) }, { key: 'triage_level', title: 'Triage Level', render: (level: string) => ( <Badge className={`${getTriageLevelColor(level)} rounded-full px-2 py-1 text-xs`}> {getTriageLevelText(level)} </Badge> ) }, { key: 'status', title: 'Status', render: (status: string) => ( <Badge className={`${getStatusColor(status)} rounded-full px-2 py-1 text-xs`}> {status} </Badge> ) }, { key: 'arrival_time', title: 'Arrival Time', render: (time: string) => ( <span className="text-sm">{new Date(time).toLocaleString()}</span> ) } ]; const contactColumns = [ { key: 'name', title: 'Name', render: (value: string) => ( <span className="font-medium">{value}</span> ) }, { key: 'organization', title: 'Organization', render: (value: string) => ( <span className="text-sm">{value}</span> ) }, { key: 'phone_primary', title: 'Phone', render: (value: string) => ( <span className="font-mono text-sm">{value}</span> ) }, { key: 'contact_type', title: 'Type', render: (value: string) => ( <Badge className="status-info rounded-full px-2 py-1 text-xs"> {value.replace('_', ' ').toUpperCase()} </Badge> ) }, { key: 'priority', title: 'Priority', render: (value: number) => ( <span className="text-sm font-medium">{value}</span> ) }, { key: 'is_active', title: 'Status', render: (isActive: boolean) => ( <Badge className={`${isActive ? 'status-success ' : 'status-error '} rounded-full px-2 py-1 text-xs`}> {isActive ? 'Active' : 'Inactive'} </Badge> ) } ]; const renderActions = (record: any) => ( <div className="flex items-center gap-2"> <Button variant="ghost" size="sm" onClick={() => { /* TODO: Handle action */ }} className="h-8 w-8 p-0" > <Eye className="h-4 w-4" /> </Button> <Button variant="ghost" size="sm" onClick={() => { /* TODO: Handle action */ }} className="h-8 w-8 p-0" > <Edit className="h-4 w-4" /> </Button> </div> ); const tabs = [ { id: 'cases', label: 'Emergency Cases', icon: AlertTriangle }, { id: 'contacts', label: 'Emergency Contacts', icon: Phone }, { id: 'trauma', label: 'Trauma Cases', icon: Activity }, { id: 'code-blue', label: 'Code Blue', icon: Heart } ]; return ( <div className="space-y-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */} <Card className="glass border-0 shadow-xl"> <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg"> <AlertTriangle className="w-6 h-6 text-white" /> </div> <div> <CardTitle className="text-2xl font-bold macos-text-primary"> Emergency Management </CardTitle> <CardDescription className="macos-text-secondary"> Manage emergency cases, contacts, and protocols </CardDescription> </div> </div> <Button className="bg-red-600 hover:bg-red-700 text-white"> <Plus className="w-4 h-4 mr-2" /> New Emergency Case </Button> </div> </CardHeader> </Card> {/* Navigation Tabs */} <Card className="glass border-0 shadow-xl"> <CardContent className="p-6"> <div className="flex space-x-1 bg-muted dark:bg-gray-800 p-1 rounded-lg"> {tabs.map((tab) => { const Icon = tab.icon; return ( <button key={tab.id} onClick={() => setActiveTab(tab.id as any)} className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${ activeTab === tab.id ? 'bg-background dark:bg-gray-700 text-rose-700 dark:text-rose-400 shadow-sm' : 'text-muted-foreground dark:text-gray-400 hover:text-foreground dark:hover:text-gray-200' }`} > <Icon className="w-4 h-4" /> {tab.label} </button> ); })} </div> </CardContent> </Card> {/* Content */} <Card className="glass border-0 shadow-xl"> <CardHeader> <div className="flex items-center justify-between"> <CardTitle className="flex items-center gap-2"> {activeTab === 'cases' && <><AlertTriangle className="w-5 h-5" /> Emergency Cases</>} {activeTab === 'contacts' && <><Phone className="w-5 h-5" /> Emergency Contacts</>} {activeTab === 'trauma' && <><Activity className="w-5 h-5" /> Trauma Cases</>} {activeTab === 'code-blue' && <><Heart className="w-5 h-5" /> Code Blue Events</>} </CardTitle> <div className="flex items-center gap-2"> <div className="relative"> <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" /> <Input placeholder="Search..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-10 w-64" /> </div> <Button variant="outline" size="sm"> <Filter className="w-4 h-4 mr-2" /> Filter </Button> </div> </div> </CardHeader> <CardContent> {activeTab === 'cases' && ( <DataTable data={cases} columns={caseColumns} loading={loading} actions={{ title: 'Actions', render: renderActions, }} /> )} {activeTab === 'contacts' && ( <DataTable data={contacts} columns={contactColumns} loading={loading} actions={{ title: 'Actions', render: renderActions, }} /> )} {(activeTab === 'trauma' || activeTab === 'code-blue') && ( <div className="text-center py-12"> <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4"> <Stethoscope className="w-8 h-8 text-gray-400" /> </div> <h3 className="text-lg font-medium text-foreground mb-2"> {activeTab === 'trauma' ? 'Trauma Management' : 'Code Blue Management'} </h3> <p className="text-muted-foreground mb-4"> {activeTab === 'trauma' ? 'Manage trauma cases and severity scoring' : 'Manage cardiac arrest events and resuscitation protocols' } </p> <Button> <Plus className="w-4 h-4 mr-2" /> {activeTab === 'trauma' ? 'Add Trauma Case' : 'Record Code Blue'} </Button> </div> )} </CardContent> </Card> </div> </div> ); }; export default EmergencyManagement; 