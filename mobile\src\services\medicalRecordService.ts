import { apiService } from './api'; import { userService } from './userService'; import { MedicalRecord } from '../types/patient'; import { PaginatedResponse } from '../types/api'; export interface CreateMedicalRecordData { diagnosis: string; symptoms: string; treatment: string; prescription?: string; notes?: string; visit_date: string; doctor_name?: string; follow_up_date?: string; } export interface UpdateMedicalRecordData { diagnosis?: string; symptoms?: string; treatment?: string; prescription?: string; notes?: string; visit_date?: string; doctor_name?: string; follow_up_date?: string; } class MedicalRecordService { /** * Get all medical records for current user */ async getMyMedicalRecords(page = 1, pageSize = 20): Promise<PaginatedResponse<MedicalRecord>> { const currentPatient = await userService.getCurrentPatient(); return apiService.getPaginated<MedicalRecord>(`/patients/medical-records/?patient=${currentPatient.id}&page=${page}&page_size=${pageSize}`); } /** * Get a specific medical record */ async getMedicalRecord(id: number): Promise<MedicalRecord> { return apiService.get<MedicalRecord>(`/patients/medical-records/${id}/`); } /** * Create a new medical record */ async createMedicalRecord(data: CreateMedicalRecordData): Promise<MedicalRecord> { const currentPatient = await userService.getCurrentPatient(); const recordData = { ...data, patient: currentPatient.id }; return apiService.post<MedicalRecord>('/patients/medical-records/', recordData); } /** * Update an existing medical record */ async updateMedicalRecord(id: number, data: UpdateMedicalRecordData): Promise<MedicalRecord> { return apiService.patch<MedicalRecord>(`/patients/medical-records/${id}/`, data); } /** * Delete a medical record */ async deleteMedicalRecord(id: number): Promise<void> { return apiService.delete(`/patients/medical-records/${id}/`); } /** * Search medical records */ async searchMedicalRecords(query: string, page = 1, pageSize = 20): Promise<PaginatedResponse<MedicalRecord>> { const currentPatient = await userService.getCurrentPatient(); return apiService.getPaginated<MedicalRecord>(`/patients/medical-records/?patient=${currentPatient.id}&search=${encodeURIComponent(query)}&page=${page}&page_size=${pageSize}`); } /** * Filter medical records by date range */ async getMedicalRecordsByDateRange(startDate: string, endDate: string, page = 1, pageSize = 20): Promise<PaginatedResponse<MedicalRecord>> { const currentPatient = await userService.getCurrentPatient(); return apiService.getPaginated<MedicalRecord>(`/patients/medical-records/?patient=${currentPatient.id}&visit_date__gte=${startDate}&visit_date__lte=${endDate}&page=${page}&page_size=${pageSize}`); } /** * Get medical records by type (e.g., prescriptions only) */ async getMedicalRecordsByType(type: 'prescription' | 'diagnosis' | 'all', page = 1, pageSize = 20): Promise<PaginatedResponse<MedicalRecord>> { const currentPatient = await userService.getCurrentPatient(); let url = `/patients/medical-records/?patient=${currentPatient.id}&page=${page}&page_size=${pageSize}`; if (type === 'prescription') { url += '&prescription__isnull=false'; } else if (type === 'diagnosis') { url += '&diagnosis__isnull=false'; } return apiService.getPaginated<MedicalRecord>(url); } /** * Get recent medical records */ async getRecentMedicalRecords(days = 30, page = 1, pageSize = 10): Promise<PaginatedResponse<MedicalRecord>> { const currentPatient = await userService.getCurrentPatient(); const startDate = new Date(); startDate.setDate(startDate.getDate() - days); const dateStr = startDate.toISOString().split('T')[0]; return apiService.getPaginated<MedicalRecord>(`/patients/medical-records/?patient=${currentPatient.id}&visit_date__gte=${dateStr}&page=${page}&page_size=${pageSize}`); } /** * Export medical records (for sharing with doctors) */ async exportMedicalRecords(format: 'pdf' | 'json' = 'pdf'): Promise<Blob> { const currentPatient = await userService.getCurrentPatient(); return apiService.get(`/patients/medical-records/export/?patient=${currentPatient.id}&format=${format}`, { responseType: 'blob' }); } /** * Share medical record with doctor */ async shareMedicalRecord(recordId: number, doctorEmail: string, message?: string): Promise<void> { return apiService.post(`/patients/medical-records/${recordId}/share/`, { doctor_email: doctorEmail, message: message || 'Medical record shared via HMS Mobile App' }); } /** * Add attachment to medical record */ async addAttachment(recordId: number, file: File, description?: string): Promise<any> { const formData = new FormData(); formData.append('file', file); if (description) { formData.append('description', description); } return apiService.post(`/patients/medical-records/${recordId}/attachments/`, formData, { headers: { 'Content-Type': 'multipart/form-data' } }); } /** * Get medical record statistics */ async getMedicalRecordStats(): Promise<{ total: number; thisMonth: number; thisYear: number; prescriptions: number; followUps: number; }> { const currentPatient = await userService.getCurrentPatient(); return apiService.get(`/patients/medical-records/stats/?patient=${currentPatient.id}`); } } export const medicalRecordService = new MedicalRecordService(); 