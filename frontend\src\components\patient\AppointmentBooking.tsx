import React, { useState } from 'react'; import { useTranslation } from 'react-i18next'; import { useSelector } from 'react-redux'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import { Button } from '../ui/Button'; import { Badge } from '../ui/badge'; import { Input } from '../ui/Input'; import { Calendar, Clock, User, MapPin, Search, ChevronLeft, ChevronRight, CheckCircle, AlertCircle, Stethoscope, Heart, Eye, Brain, Bone } from 'lucide-react'; import appointmentService from '../../services/appointmentService'; import type { RootState } from '../../store'; const AppointmentBooking: React.FC = () => { const { t } = useTranslation(); const { user } = useSelector((state: RootState) => state.auth); const [selectedStep, setSelectedStep] = useState(1); const [selectedDepartment, setSelectedDepartment] = useState(''); const [selectedDoctor, setSelectedDoctor] = useState(''); const [selectedDate, setSelectedDate] = useState(''); const [selectedTime, setSelectedTime] = useState(''); const [appointmentType, setAppointmentType] = useState(''); const [reason, setReason] = useState(''); const [isBooking, setIsBooking] = useState(false); const [bookingError, setBookingError] = useState<string | null>(null); const departments = [ { id: 'cardiology', name: t('appointmentBooking.departments.cardiology'), icon: Heart, description: t('appointmentBooking.departmentDescriptions.cardiology') }, { id: 'neurology', name: t('appointmentBooking.departments.neurology'), icon: Brain, description: t('appointmentBooking.departmentDescriptions.neurology') }, { id: 'orthopedics', name: t('appointmentBooking.departments.orthopedics'), icon: Bone, description: t('appointmentBooking.departmentDescriptions.orthopedics') }, { id: 'ophthalmology', name: t('appointmentBooking.departments.ophthalmology'), icon: Eye, description: t('appointmentBooking.departmentDescriptions.ophthalmology') }, { id: 'general', name: t('appointmentBooking.departments.general'), icon: Stethoscope, description: t('appointmentBooking.departmentDescriptions.general') } ]; const doctors = { cardiology: [ { id: 1, name: 'Dr. Sarah Johnson', specialization: 'Interventional Cardiology', rating: 4.9, experience: '15 years' }, { id: 2, name: 'Dr. Michael Chen', specialization: 'Cardiac Surgery', rating: 4.8, experience: '12 years' } ], neurology: [ { id: 3, name: 'Dr. Emily Rodriguez', specialization: 'Neurological Disorders', rating: 4.9, experience: '18 years' }, { id: 4, name: 'Dr. David Kim', specialization: 'Pediatric Neurology', rating: 4.7, experience: '10 years' } ], orthopedics: [ { id: 5, name: 'Dr. Robert Wilson', specialization: 'Sports Medicine', rating: 4.8, experience: '14 years' }, { id: 6, name: 'Dr. Lisa Thompson', specialization: 'Joint Replacement', rating: 4.9, experience: '16 years' } ], ophthalmology: [ { id: 7, name: 'Dr. James Brown', specialization: 'Retinal Diseases', rating: 4.8, experience: '13 years' }, { id: 8, name: 'Dr. Maria Garcia', specialization: 'Cataract Surgery', rating: 4.9, experience: '11 years' } ], general: [ { id: 9, name: 'Dr. John Smith', specialization: 'Family Medicine', rating: 4.7, experience: '20 years' }, { id: 10, name: 'Dr. Anna Davis', specialization: 'Internal Medicine', rating: 4.8, experience: '8 years' } ] }; const appointmentTypes = [ { id: 'consultation', name: t('appointmentBooking.appointmentTypes.consultation'), duration: t('appointmentBooking.durations.consultation'), description: t('appointmentBooking.appointmentTypeDescriptions.consultation') }, { id: 'checkup', name: t('appointmentBooking.appointmentTypes.checkup'), duration: t('appointmentBooking.durations.checkup'), description: t('appointmentBooking.appointmentTypeDescriptions.checkup') }, { id: 'emergency', name: t('appointmentBooking.appointmentTypes.emergency'), duration: t('appointmentBooking.durations.emergency'), description: t('appointmentBooking.appointmentTypeDescriptions.emergency') }, { id: 'followup', name: t('appointmentBooking.appointmentTypes.followup'), duration: t('appointmentBooking.durations.followup'), description: t('appointmentBooking.appointmentTypeDescriptions.followup') } ]; const availableSlots = [ '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '02:00 PM', '02:30 PM', '03:00 PM', '03:30 PM', '04:00 PM', '04:30 PM' ]; const generateDates = () => { const dates = []; const today = new Date(); for (let i = 1; i <= 14; i++) { const date = new Date(today); date.setDate(today.getDate() + i); dates.push(date); } return dates; }; const availableDates = generateDates(); const handleBookAppointment = async () => { setIsBooking(true); setBookingError(null); try { // TODO: In production, fetch the actual patient profile ID from the backend // For now, we'll use a mock patient ID that matches our test data const mockPatientId = 123; // This should match CURRENT_PATIENT_DATA.id const appointmentData = { patient: mockPatientId, // Patient model ID (not User ID) doctor: parseInt(selectedDoctor), // Doctor User ID appointment_date: selectedDate, appointment_time: selectedTime, appointment_type: appointmentType, reason_for_visit: reason, duration_minutes: appointmentType === 'consultation' ? 30 : appointmentType === 'checkup' ? 20 : appointmentType === 'emergency' ? 45 : 15 }; console.log('Booking appointment with data:', appointmentData); console.log('User context:', user); const result = await appointmentService.create(appointmentData); console.log('Appointment booked successfully:', result); setSelectedStep(5); // Show confirmation } catch (error: any) { console.error('Failed to book appointment:', error); console.error('Error details:', error.response?.data); // Enhanced error handling with more specific messages let errorMessage = 'Failed to book appointment. Please try again.'; if (error.response?.status === 404) { errorMessage = 'Appointment service not available. Please contact support.'; } else if (error.response?.status === 400) { const errorData = error.response.data; if (errorData.patient) { errorMessage = 'Patient profile not found. Please contact support.'; } else if (errorData.doctor) { errorMessage = 'Selected doctor is not available. Please choose another doctor.'; } else if (errorData.appointment_date || errorData.appointment_time) { errorMessage = 'Selected date/time is not available. Please choose another slot.'; } else { errorMessage = errorData.message || errorData.error || 'Invalid appointment data.'; } } else if (error.response?.status === 401) { errorMessage = 'Please log in to book an appointment.'; } else if (error.response?.status >= 500) { errorMessage = 'Server error. Please try again later.'; } setBookingError(errorMessage); } finally { setIsBooking(false); } }; const renderStepContent = () => { switch (selectedStep) { case 1: return ( <div className="space-y-4"> <h3 className="text-lg font-semibold mb-4">{t('appointmentBooking.selectDepartment')}</h3> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {departments.map((dept) => { const IconComponent = dept.icon; return ( <Card key={dept.id} className={`cursor-pointer transition-all hover:shadow-md ${ selectedDepartment === dept.id ? 'ring-2 ring-blue-500 bg-blue-50' : '' }`} onClick={() => setSelectedDepartment(dept.id)} > <CardContent className="p-4"> <div className="flex items-center space-x-3"> <IconComponent className="w-8 h-8 text-sky-700 dark:text-sky-400" /> <div> <h4 className="font-medium">{dept.name}</h4> <p className="text-sm text-muted-foreground">{dept.description}</p> </div> </div> </CardContent> </Card> ); })} </div> </div> ); case 2: return ( <div className="space-y-4"> <h3 className="text-lg font-semibold mb-4">{t('appointmentBooking.selectDoctor')}</h3> <div className="space-y-3"> {doctors[selectedDepartment]?.map((doctor) => ( <Card key={doctor.id} className={`cursor-pointer transition-all hover:shadow-md ${ selectedDoctor === doctor.id.toString() ? 'ring-2 ring-blue-500 bg-blue-50' : '' }`} onClick={() => setSelectedDoctor(doctor.id.toString())} > <CardContent className="p-4"> <div className="flex items-center justify-between"> <div className="flex items-center space-x-4"> <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center"> <User className="w-6 h-6 text-sky-700 dark:text-sky-400" /> </div> <div> <h4 className="font-medium">{doctor.name}</h4> <p className="text-sm text-muted-foreground">{doctor.specialization}</p> <p className="text-xs text-gray-500">{doctor.experience} {t('appointmentBooking.experience')}</p> </div> </div> <div className="text-right"> <div className="flex items-center space-x-1"> <span className="text-yellow-500">★</span> <span className="text-sm font-medium">{doctor.rating}</span> </div> </div> </div> </CardContent> </Card> ))} </div> </div> ); case 3: return ( <div className="space-y-4"> <h3 className="text-lg font-semibold mb-4">{t('appointmentBooking.selectDateTime')}</h3> <div className="mb-6"> <h4 className="font-medium mb-3">{t('appointmentBooking.availableDates')}</h4> <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2"> {availableDates.map((date, index) => ( <Button key={index} variant={selectedDate === date.toISOString().split('T')[0] ? "default" : "outline"} onClick={() => setSelectedDate(date.toISOString().split('T')[0])} className="flex flex-col p-3 h-auto" > <span className="text-xs">{date.toLocaleDateString('en', { weekday: 'short' })}</span> <span className="font-medium">{date.getDate()}</span> <span className="text-xs">{date.toLocaleDateString('en', { month: 'short' })}</span> </Button> ))} </div> </div> {selectedDate && ( <div> <h4 className="font-medium mb-3">{t('appointmentBooking.availableTimeSlots')}</h4> <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2"> {availableSlots.map((time) => ( <Button key={time} variant={selectedTime === time ? "default" : "outline"} onClick={() => setSelectedTime(time)} className="text-sm" > {time} </Button> ))} </div> </div> )} </div> ); case 4: return ( <div className="space-y-4"> <h3 className="text-lg font-semibold mb-4">{t('appointmentBooking.appointmentDetails')}</h3> <div className="space-y-4"> <div> <label className="block text-sm font-medium mb-2">{t('appointmentBooking.appointmentType')}</label> <div className="grid grid-cols-1 md:grid-cols-2 gap-3"> {appointmentTypes.map((type) => ( <Card key={type.id} className={`cursor-pointer transition-all hover:shadow-md ${ appointmentType === type.id ? 'ring-2 ring-blue-500 bg-blue-50' : '' }`} onClick={() => setAppointmentType(type.id)} > <CardContent className="p-3"> <div className="flex justify-between items-start"> <div> <h4 className="font-medium">{type.name}</h4> <p className="text-sm text-muted-foreground">{type.description}</p> </div> <Badge variant="outline">{type.duration}</Badge> </div> </CardContent> </Card> ))} </div> </div> <div> <label className="block text-sm font-medium mb-2">{t('appointmentBooking.reasonForVisit')}</label> <textarea className="w-full p-3 border border-border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows={4} placeholder={t('appointmentBooking.reasonPlaceholder')} value={reason} onChange={(e) => setReason(e.target.value)} /> </div> {/* Error Message */} {bookingError && ( <div className="p-4 bg-red-50 border rounded-lg"> <div className="flex items-center"> <AlertCircle className="w-5 h-5 text-red-400 mr-3" /> <p className="text-sm font-medium text-rose-700 dark:text-rose-400"> {bookingError} </p> </div> </div> )} </div> </div> ); case 5: return ( <div className="text-center space-y-6"> <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto"> <CheckCircle className="w-8 h-8 text-emerald-700 dark:text-emerald-400" /> </div> <div> <h3 className="text-xl font-semibold text-emerald-700 dark:text-emerald-400 mb-2">{t('appointmentBooking.appointmentBookedSuccessfully')}</h3> <p className="text-muted-foreground">{t('appointmentBooking.confirmationEmailSent')}</p> </div> <Card className="text-left"> <CardHeader> <CardTitle>{t('appointmentBooking.appointmentDetailsTitle')}</CardTitle> </CardHeader> <CardContent className="space-y-3"> <div className="flex justify-between"> <span className="text-muted-foreground">{t('appointmentBooking.department')}:</span> <span className="font-medium">{departments.find(d => d.id === selectedDepartment)?.name}</span> </div> <div className="flex justify-between"> <span className="text-muted-foreground">{t('appointmentBooking.doctor')}:</span> <span className="font-medium"> {doctors[selectedDepartment]?.find(d => d.id.toString() === selectedDoctor)?.name} </span> </div> <div className="flex justify-between"> <span className="text-muted-foreground">{t('appointmentBooking.date')}:</span> <span className="font-medium">{new Date(selectedDate).toLocaleDateString()}</span> </div> <div className="flex justify-between"> <span className="text-muted-foreground">{t('appointmentBooking.time')}:</span> <span className="font-medium">{selectedTime}</span> </div> <div className="flex justify-between"> <span className="text-muted-foreground">{t('appointmentBooking.type')}:</span> <span className="font-medium">{appointmentTypes.find(t => t.id === appointmentType)?.name}</span> </div> </CardContent> </Card> <div className="flex space-x-3"> <Button onClick={() => window.print()} variant="outline" className="flex-1"> {t('appointmentBooking.printDetails')} </Button> <Button onClick={() => setSelectedStep(1)} className="flex-1"> {t('appointmentBooking.bookAnother')} </Button> </div> </div> ); default: return null; } }; const canProceed = () => { switch (selectedStep) { case 1: return selectedDepartment !== ''; case 2: return selectedDoctor !== ''; case 3: return selectedDate !== '' && selectedTime !== ''; case 4: return appointmentType !== '' && reason.trim() !== ''; default: return false; } }; return ( <div className="space-y-6 p-6"> {/* Header */} <div className="text-center"> <h1 className="text-3xl font-bold text-foreground mb-2"> {t('appointmentBooking.title')} </h1> <p className="text-lg text-muted-foreground"> {t('appointmentBooking.subtitle')} </p> </div> {/* Progress Steps */} <div className="flex items-center justify-center space-x-4 mb-8"> {[1, 2, 3, 4, 5].map((step) => ( <div key={step} className="flex items-center"> <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${ step <= selectedStep ? 'bg-blue-600 text-white' : 'bg-accent text-muted-foreground' }`}> {step < selectedStep ? <CheckCircle className="w-4 h-4" /> : step} </div> {step < 5 && ( <div className={`w-12 h-0.5 ${ step < selectedStep ? 'bg-blue-600' : 'bg-accent' }`} /> )} </div> ))} </div> {/* Step Content */} <Card className="max-w-4xl mx-auto"> <CardContent className="p-6"> {renderStepContent()} </CardContent> </Card> {/* Navigation Buttons */} {selectedStep < 5 && ( <div className="flex justify-between max-w-4xl mx-auto"> <Button variant="outline" onClick={() => setSelectedStep(Math.max(1, selectedStep - 1))} disabled={selectedStep === 1} className="flex items-center space-x-2" > <ChevronLeft className="w-4 h-4" /> <span>{t('appointmentBooking.previous')}</span> </Button> <Button onClick={() => { if (selectedStep === 4) { handleBookAppointment(); } else { setSelectedStep(selectedStep + 1); } }} disabled={!canProceed() || isBooking} className="flex items-center space-x-2" > <span> {selectedStep === 4 ? (isBooking ? 'Booking...' : t('appointmentBooking.bookAppointment')) : t('appointmentBooking.next') } </span> {selectedStep < 4 && <ChevronRight className="w-4 h-4" />} </Button> </div> )} </div> ); }; export default AppointmentBooking; 