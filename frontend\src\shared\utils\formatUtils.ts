/** * Format Utilities * Centralized formatting functions for numbers, currency, text, and other data types */ export interface CurrencyOptions { currency?: string; locale?: string; minimumFractionDigits?: number; maximumFractionDigits?: number; showSymbol?: boolean; } export interface NumberOptions { locale?: string; minimumFractionDigits?: number; maximumFractionDigits?: number; useGrouping?: boolean; } export interface PhoneOptions { country?: string; format?: 'national' | 'international' | 'e164'; } /** * Format currency with locale support */ export function formatCurrency( amount: number, options: CurrencyOptions = {} ): string { const { currency = 'USD', locale = 'en-US', minimumFractionDigits = 2, maximumFractionDigits = 2, showSymbol = true, } = options; if (isNaN(amount)) { return '0.00'; } try { const formatter = new Intl.NumberFormat(locale, { style: showSymbol ? 'currency' : 'decimal', currency: showSymbol ? currency : undefined, minimumFractionDigits, maximumFractionDigits, }); return formatter.format(amount); } catch (error) { // Fallback formatting const fixed = amount.toFixed(maximumFractionDigits); return showSymbol ? `$${fixed}` : fixed; } } /** * Format number with locale support */ export function formatNumber( value: number, options: NumberOptions = {} ): string { const { locale = 'en-US', minimumFractionDigits = 0, maximumFractionDigits = 2, useGrouping = true, } = options; if (isNaN(value)) { return '0'; } try { const formatter = new Intl.NumberFormat(locale, { minimumFractionDigits, maximumFractionDigits, useGrouping, }); return formatter.format(value); } catch (error) { return value.toString(); } } /** * Format percentage */ export function formatPercentage( value: number, decimals: number = 1, locale: string = 'en-US' ): string { if (isNaN(value)) { return '0%'; } try { const formatter = new Intl.NumberFormat(locale, { style: 'percent', minimumFractionDigits: decimals, maximumFractionDigits: decimals, }); return formatter.format(value / 100); } catch (error) { return `${value.toFixed(decimals)}%`; } } /** * Format file size in human readable format */ export function formatFileSize(bytes: number, decimals: number = 2): string { if (bytes === 0) return '0 Bytes'; const k = 1024; const dm = decimals < 0 ? 0 : decimals; const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]; } /** * Format phone number */ export function formatPhoneNumber( phone: string, options: PhoneOptions = {} ): string { const { country = 'US', format = 'national' } = options; // Remove all non-digit characters const cleaned = phone.replace(/\D/g, ''); if (!cleaned) return ''; // US phone number formatting if (country === 'US') { if (cleaned.length === 10) { switch (format) { case 'national': return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`; case 'international': return `+1 (${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`; case 'e164': return `+1${cleaned}`; default: return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`; } } else if (cleaned.length === 11 && cleaned.startsWith('1')) { const number = cleaned.slice(1); switch (format) { case 'national': return `(${number.slice(0, 3)}) ${number.slice(3, 6)}-${number.slice(6)}`; case 'international': return `+1 (${number.slice(0, 3)}) ${number.slice(3, 6)}-${number.slice(6)}`; case 'e164': return `+${cleaned}`; default: return `(${number.slice(0, 3)}) ${number.slice(3, 6)}-${number.slice(6)}`; } } } // Return cleaned number if no specific formatting applies return cleaned; } /** * Format text to title case */ export function toTitleCase(text: string): string { if (!text) return ''; return text .toLowerCase() .split(' ') .map(word => word.charAt(0).toUpperCase() + word.slice(1)) .join(' '); } /** * Format text to sentence case */ export function toSentenceCase(text: string): string { if (!text) return ''; return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase(); } /** * Format text to camelCase */ export function toCamelCase(text: string): string { if (!text) return ''; return text .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => { return index === 0 ? word.toLowerCase() : word.toUpperCase(); }) .replace(/\s+/g, ''); } /** * Format text to kebab-case */ export function toKebabCase(text: string): string { if (!text) return ''; return text .replace(/([a-z])([A-Z])/g, '$1-$2') .replace(/[\s_]+/g, '-') .toLowerCase(); } /** * Format text to snake_case */ export function toSnakeCase(text: string): string { if (!text) return ''; return text .replace(/([a-z])([A-Z])/g, '$1_$2') .replace(/[\s-]+/g, '_') .toLowerCase(); } /** * Truncate text with ellipsis */ export function truncateText(text: string, maxLength: number, suffix: string = '...'): string { if (!text || text.length <= maxLength) return text; return text.slice(0, maxLength - suffix.length) + suffix; } /** * Truncate text at word boundary */ export function truncateWords(text: string, maxWords: number, suffix: string = '...'): string { if (!text) return ''; const words = text.split(' '); if (words.length <= maxWords) return text; return words.slice(0, maxWords).join(' ') + suffix; } /** * Format initials from name */ export function getInitials(name: string, maxInitials: number = 2): string { if (!name) return ''; const words = name.trim().split(/\s+/); const initials = words .slice(0, maxInitials) .map(word => word.charAt(0).toUpperCase()) .join(''); return initials; } /** * Format address for display */ export function formatAddress(address: { street?: string; city?: string; state?: string; zipCode?: string; country?: string; }, format: 'single-line' | 'multi-line' = 'single-line'): string { const parts = [ address.street, address.city, address.state, address.zipCode, address.country, ].filter(Boolean); if (format === 'multi-line') { return parts.join('\n'); } return parts.join(', '); } /** * Format credit card number with masking */ export function formatCreditCard(cardNumber: string, maskDigits: boolean = true): string { const cleaned = cardNumber.replace(/\D/g, ''); if (!cleaned) return ''; if (maskDigits && cleaned.length > 4) { const lastFour = cleaned.slice(-4); const masked = '*'.repeat(cleaned.length - 4); return (masked + lastFour).replace(/(.{4})/g, '$1 ').trim(); } return cleaned.replace(/(.{4})/g, '$1 ').trim(); } /** * Format social security number with masking */ export function formatSSN(ssn: string, maskDigits: boolean = true): string { const cleaned = ssn.replace(/\D/g, ''); if (cleaned.length !== 9) return ssn; if (maskDigits) { return `***-**-${cleaned.slice(-4)}`; } return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 5)}-${cleaned.slice(5)}`; } /** * Format medical record number */ export function formatMedicalRecordNumber(mrn: string, prefix: string = 'MRN'): string { if (!mrn) return ''; const cleaned = mrn.replace(/\D/g, ''); return `${prefix}-${cleaned.padStart(6, '0')}`; } /** * Format patient ID */ export function formatPatientId(id: string | number, prefix: string = 'P'): string { if (!id) return ''; const numericId = typeof id === 'string' ? id.replace(/\D/g, '') : id.toString(); return `${prefix}${numericId.padStart(6, '0')}`; } /** * Format appointment ID */ export function formatAppointmentId(id: string | number, prefix: string = 'APT'): string { if (!id) return ''; const numericId = typeof id === 'string' ? id.replace(/\D/g, '') : id.toString(); return `${prefix}-${numericId.padStart(8, '0')}`; } /** * Format duration in human readable format */ export function formatDuration(minutes: number): string { if (minutes < 60) { return `${minutes} min${minutes !== 1 ? 's' : ''}`; } const hours = Math.floor(minutes / 60); const remainingMinutes = minutes % 60; if (remainingMinutes === 0) { return `${hours} hour${hours !== 1 ? 's' : ''}`; } return `${hours}h ${remainingMinutes}m`; } /** * Format list with proper conjunction */ export function formatList( items: string[], conjunction: 'and' | 'or' = 'and' ): string { if (items.length === 0) return ''; if (items.length === 1) return items[0]; if (items.length === 2) return `${items[0]} ${conjunction} ${items[1]}`; const lastItem = items[items.length - 1]; const otherItems = items.slice(0, -1); return `${otherItems.join(', ')}, ${conjunction} ${lastItem}`; } /** * Format boolean as Yes/No */ export function formatBoolean(value: boolean, format: 'yes-no' | 'true-false' | 'on-off' = 'yes-no'): string { switch (format) { case 'yes-no': return value ? 'Yes' : 'No'; case 'true-false': return value ? 'True' : 'False'; case 'on-off': return value ? 'On' : 'Off'; default: return value ? 'Yes' : 'No'; } } /** * Format array as comma-separated string */ export function formatArray( items: any[], formatter?: (item: any) => string, separator: string = ', ' ): string { if (!Array.isArray(items) || items.length === 0) return ''; const formatted = formatter ? items.map(formatter) : items.map(String); return formatted.join(separator); } /** * Format object as key-value pairs */ export function formatObject( obj: Record<string, any>, keyFormatter?: (key: string) => string, valueFormatter?: (value: any) => string, separator: string = ': ', delimiter: string = ', ' ): string { if (!obj || typeof obj !== 'object') return ''; const pairs = Object.entries(obj).map(([key, value]) => { const formattedKey = keyFormatter ? keyFormatter(key) : key; const formattedValue = valueFormatter ? valueFormatter(value) : String(value); return `${formattedKey}${separator}${formattedValue}`; }); return pairs.join(delimiter); } 