/** * Advanced Search and Filter Component * Provides comprehensive search and filtering capabilities with debouncing and multiple filter types */ import React, { useState, useCallback, useEffect } from 'react'; import { Search, Filter, X, ChevronDown, Calendar, SlidersHorizontal } from 'lucide-react'; import { Button } from '../../../components/ui/Button'; import { Input } from '../../../components/ui/Input'; import { Label } from '../../../components/ui/label'; import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'; import { Checkbox } from '../../../components/ui/checkbox'; import { RadioGroup, RadioGroupItem } from '../../../components/ui/radio-group'; import { Popover, PopoverContent, PopoverTrigger } from '../../../components/ui/popover'; import { Badge } from '../../../components/ui/badge'; import { Separator } from '../../../components/ui/separator'; import { useDebounce } from '../../hooks/useDebounce'; import { cn } from '../../../lib/utils'; export interface FilterOption { value: string; label: string; count?: number; } export interface FilterConfig { key: string; label: string; type: 'text' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'date' | 'daterange' | 'number'; options?: FilterOption[]; placeholder?: string; defaultValue?: any; validation?: (value: any) => boolean; } export interface SearchFilterProps { searchPlaceholder?: string; searchValue?: string; onSearchChange?: (value: string) => void; filters?: FilterConfig[]; filterValues?: Record<string, any>; onFilterChange?: (filters: Record<string, any>) => void; onClearAll?: () => void; showFilterCount?: boolean; debounceMs?: number; className?: string; size?: 'sm' | 'md' | 'lg'; variant?: 'default' | 'compact'; } export const SearchFilter: React.FC<SearchFilterProps> = ({ searchPlaceholder = 'Search...', searchValue = '', onSearchChange, filters = [], filterValues = {}, onFilterChange, onClearAll, showFilterCount = true, debounceMs = 300, className, size = 'md', variant = 'default', }) => { const [localSearchValue, setLocalSearchValue] = useState(searchValue); const [localFilterValues, setLocalFilterValues] = useState(filterValues); const [isFilterOpen, setIsFilterOpen] = useState(false); const debouncedSearchValue = useDebounce(localSearchValue, debounceMs); // Handle search changes useEffect(() => { if (debouncedSearchValue !== searchValue) { onSearchChange?.(debouncedSearchValue); } }, [debouncedSearchValue, searchValue, onSearchChange]); // Handle filter changes const handleFilterChange = useCallback((key: string, value: any) => { const newFilters = { ...localFilterValues, [key]: value }; setLocalFilterValues(newFilters); onFilterChange?.(newFilters); }, [localFilterValues, onFilterChange]); // Clear individual filter const clearFilter = useCallback((key: string) => { const newFilters = { ...localFilterValues }; delete newFilters[key]; setLocalFilterValues(newFilters); onFilterChange?.(newFilters); }, [localFilterValues, onFilterChange]); // Clear all filters and search const handleClearAll = useCallback(() => { setLocalSearchValue(''); setLocalFilterValues({}); onSearchChange?.(''); onFilterChange?.({}); onClearAll?.(); }, [onSearchChange, onFilterChange, onClearAll]); // Get active filter count const activeFilterCount = Object.keys(localFilterValues).filter( key => localFilterValues[key] !== undefined && localFilterValues[key] !== '' && localFilterValues[key] !== null ).length; // Size classes const sizeClasses = { sm: 'h-8 text-sm', md: 'h-10 text-sm', lg: 'h-12 text-base', }; // Render filter input based on type const renderFilterInput = (filter: FilterConfig) => { const value = localFilterValues[filter.key] || filter.defaultValue; switch (filter.type) { case 'text': return ( <Input placeholder={filter.placeholder} value={value || ''} onChange={(e) => handleFilterChange(filter.key, e.target.value)} className={cn(sizeClasses[size])} /> ); case 'select': return ( <Select value={value || ''} onValueChange={(val) => handleFilterChange(filter.key, val)}> <SelectTrigger className={cn(sizeClasses[size])}> <SelectValue placeholder={filter.placeholder} /> </SelectTrigger> <SelectContent> {filter.options?.map((option) => ( <SelectItem key={option.value} value={option.value}> {option.label} {option.count !== undefined && ( <span className="ml-2 text-xs text-gray-500">({option.count})</span> )} </SelectItem> ))} </SelectContent> </Select> ); case 'multiselect': const selectedValues = Array.isArray(value) ? value : []; return ( <div className="space-y-2"> {filter.options?.map((option) => ( <div key={option.value} className="flex items-center space-x-2"> <Checkbox id={`${filter.key}-${option.value}`} checked={selectedValues.includes(option.value)} onCheckedChange={(checked) => { const newValues = checked ? [...selectedValues, option.value] : selectedValues.filter(v => v !== option.value); handleFilterChange(filter.key, newValues); }} /> <Label htmlFor={`${filter.key}-${option.value}`} className="text-sm"> {option.label} {option.count !== undefined && ( <span className="ml-2 text-xs text-gray-500">({option.count})</span> )} </Label> </div> ))} </div> ); case 'radio': return ( <RadioGroup value={value || ''} onValueChange={(val) => handleFilterChange(filter.key, val)} > {filter.options?.map((option) => ( <div key={option.value} className="flex items-center space-x-2"> <RadioGroupItem value={option.value} id={`${filter.key}-${option.value}`} /> <Label htmlFor={`${filter.key}-${option.value}`} className="text-sm"> {option.label} {option.count !== undefined && ( <span className="ml-2 text-xs text-gray-500">({option.count})</span> )} </Label> </div> ))} </RadioGroup> ); case 'checkbox': return ( <div className="flex items-center space-x-2"> <Checkbox id={filter.key} checked={!!value} onCheckedChange={(checked) => handleFilterChange(filter.key, checked)} /> <Label htmlFor={filter.key} className="text-sm"> {filter.label} </Label> </div> ); case 'date': return ( <Input type="date" value={value || ''} onChange={(e) => handleFilterChange(filter.key, e.target.value)} className={cn(sizeClasses[size])} /> ); case 'number': return ( <Input type="number" placeholder={filter.placeholder} value={value || ''} onChange={(e) => handleFilterChange(filter.key, e.target.value)} className={cn(sizeClasses[size])} /> ); default: return null; } }; // Render active filter badges const renderActiveFilters = () => { const activeFilters = Object.entries(localFilterValues).filter( ([key, value]) => value !== undefined && value !== '' && value !== null ); if (activeFilters.length === 0) return null; return ( <div className="flex flex-wrap gap-2"> {activeFilters.map(([key, value]) => { const filter = filters.find(f => f.key === key); if (!filter) return null; let displayValue = value; if (filter.type === 'select' || filter.type === 'radio') { const option = filter.options?.find(opt => opt.value === value); displayValue = option?.label || value; } else if (filter.type === 'multiselect' && Array.isArray(value)) { displayValue = value.length > 1 ? `${value.length} selected` : value[0]; } return ( <Badge key={key} variant="secondary" className="flex items-center gap-1"> <span className="text-xs">{filter.label}: {displayValue}</span> <Button variant="ghost" size="sm" className="h-4 w-4 p-0 hover:bg-transparent" onClick={() => clearFilter(key)} > <X className="h-3 w-3" /> </Button> </Badge> ); })} </div> ); }; if (variant === 'compact') { return ( <div className={cn('flex items-center gap-2', className)}> <div className="relative flex-1"> <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" /> <Input placeholder={searchPlaceholder} value={localSearchValue} onChange={(e) => setLocalSearchValue(e.target.value)} className={cn('pl-10', sizeClasses[size])} /> </div> {filters.length > 0 && ( <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}> <PopoverTrigger asChild> <Button variant="outline" size={size} className="relative"> <SlidersHorizontal className="h-4 w-4" /> {showFilterCount && activeFilterCount > 0 && ( <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs"> {activeFilterCount} </Badge> )} </Button> </PopoverTrigger> <PopoverContent className="w-80" align="end"> <div className="space-y-4"> <div className="flex items-center justify-between"> <h4 className="font-medium">Filters</h4> {activeFilterCount > 0 && ( <Button variant="ghost" size="sm" onClick={handleClearAll}> Clear All </Button> )} </div> <Separator /> {filters.map((filter) => ( <div key={filter.key} className="space-y-2"> <Label className="text-sm font-medium">{filter.label}</Label> {renderFilterInput(filter)} </div> ))} </div> </PopoverContent> </Popover> )} </div> ); } return ( <div className={cn('space-y-4', className)}> {/* Search Bar */} <div className="relative"> <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" /> <Input placeholder={searchPlaceholder} value={localSearchValue} onChange={(e) => setLocalSearchValue(e.target.value)} className={cn('pl-10', sizeClasses[size])} /> </div> {/* Filters */} {filters.length > 0 && ( <div className="space-y-4"> <div className="flex items-center justify-between"> <div className="flex items-center gap-2"> <Filter className="h-4 w-4" /> <span className="font-medium">Filters</span> {showFilterCount && activeFilterCount > 0 && ( <Badge variant="secondary">{activeFilterCount}</Badge> )} </div> {activeFilterCount > 0 && ( <Button variant="ghost" size="sm" onClick={handleClearAll}> Clear All </Button> )} </div> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> {filters.map((filter) => ( <div key={filter.key} className="space-y-2"> <Label className="text-sm font-medium">{filter.label}</Label> {renderFilterInput(filter)} </div> ))} </div> {/* Active Filters */} {renderActiveFilters()} </div> )} </div> ); }; export default SearchFilter; 