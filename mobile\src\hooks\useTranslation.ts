import { useTranslation as useI18nTranslation } from 'react-i18next'; import { I18nManager } from 'react-native'; export const useTranslation = () => { const { t, i18n } = useI18nTranslation(); const isRTL = i18n.language === 'ar'; const currentLanguage = i18n.language; const changeLanguage = async (language: string) => { try { await i18n.changeLanguage(language); // Update RTL layout const isRTL = language === 'ar'; I18nManager.allowRTL(isRTL); I18nManager.forceRTL(isRTL); return true; } catch (error) { console.log('Error changing language:', error); return false; } }; return { t, i18n, isRTL, currentLanguage, changeLanguage, }; }; export default useTranslation; 