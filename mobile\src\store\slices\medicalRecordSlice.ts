import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { MedicalRecord } from '../../types/patient'; import { medicalRecordService, CreateMedicalRecordData, UpdateMedicalRecordData } from '../../services/medicalRecordService'; interface MedicalRecordState { records: MedicalRecord[]; currentRecord: MedicalRecord | null; isLoading: boolean; error: string | null; totalCount: number; hasMore: boolean; } const initialState: MedicalRecordState = { records: [], currentRecord: null, isLoading: false, error: null, totalCount: 0, hasMore: true, }; // Async thunks export const fetchMedicalRecords = createAsyncThunk( 'medicalRecord/fetchMedicalRecords', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await medicalRecordService.getMyMedicalRecords(page, pageSize); return { ...response, page }; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch medical records'); } } ); export const fetchMedicalRecord = createAsyncThunk( 'medicalRecord/fetchMedicalRecord', async (id: number, { rejectWithValue }) => { try { const record = await medicalRecordService.getMedicalRecord(id); return record; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch medical record'); } } ); export const createMedicalRecord = createAsyncThunk( 'medicalRecord/createMedicalRecord', async (data: CreateMedicalRecordData, { rejectWithValue }) => { try { const record = await medicalRecordService.createMedicalRecord(data); return record; } catch (error: any) { return rejectWithValue(error.message || 'Failed to create medical record'); } } ); export const updateMedicalRecord = createAsyncThunk( 'medicalRecord/updateMedicalRecord', async ({ id, data }: { id: number; data: UpdateMedicalRecordData }, { rejectWithValue }) => { try { const record = await medicalRecordService.updateMedicalRecord(id, data); return record; } catch (error: any) { return rejectWithValue(error.message || 'Failed to update medical record'); } } ); export const deleteMedicalRecord = createAsyncThunk( 'medicalRecord/deleteMedicalRecord', async (id: number, { rejectWithValue }) => { try { await medicalRecordService.deleteMedicalRecord(id); return id; } catch (error: any) { return rejectWithValue(error.message || 'Failed to delete medical record'); } } ); export const searchMedicalRecords = createAsyncThunk( 'medicalRecord/searchMedicalRecords', async ({ query, page = 1, pageSize = 20 }: { query: string; page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await medicalRecordService.searchMedicalRecords(query, page, pageSize); return { ...response, page, query }; } catch (error: any) { return rejectWithValue(error.message || 'Failed to search medical records'); } } ); const medicalRecordSlice = createSlice({ name: 'medicalRecord', initialState, reducers: { clearError: (state) => { state.error = null; }, setCurrentRecord: (state, action) => { state.currentRecord = action.payload; }, clearCurrentRecord: (state) => { state.currentRecord = null; }, resetRecords: (state) => { state.records = []; state.totalCount = 0; state.hasMore = true; }, }, extraReducers: (builder) => { // Fetch Medical Records builder .addCase(fetchMedicalRecords.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMedicalRecords.fulfilled, (state, action) => { state.isLoading = false; const { results, count, page } = action.payload; if (page === 1) { state.records = results; } else { state.records = [...state.records, ...results]; } state.totalCount = count; state.hasMore = state.records.length < count; state.error = null; }) .addCase(fetchMedicalRecords.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch Single Medical Record builder .addCase(fetchMedicalRecord.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMedicalRecord.fulfilled, (state, action) => { state.isLoading = false; state.currentRecord = action.payload; state.error = null; }) .addCase(fetchMedicalRecord.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Create Medical Record builder .addCase(createMedicalRecord.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(createMedicalRecord.fulfilled, (state, action) => { state.isLoading = false; state.records = [action.payload, ...state.records]; state.totalCount += 1; state.error = null; }) .addCase(createMedicalRecord.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Update Medical Record builder .addCase(updateMedicalRecord.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(updateMedicalRecord.fulfilled, (state, action) => { state.isLoading = false; const updatedRecord = action.payload; const index = state.records.findIndex(record => record.id === updatedRecord.id); if (index !== -1) { state.records[index] = updatedRecord; } if (state.currentRecord?.id === updatedRecord.id) { state.currentRecord = updatedRecord; } state.error = null; }) .addCase(updateMedicalRecord.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Delete Medical Record builder .addCase(deleteMedicalRecord.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(deleteMedicalRecord.fulfilled, (state, action) => { state.isLoading = false; const deletedId = action.payload; state.records = state.records.filter(record => record.id !== deletedId); state.totalCount -= 1; if (state.currentRecord?.id === deletedId) { state.currentRecord = null; } state.error = null; }) .addCase(deleteMedicalRecord.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Search Medical Records builder .addCase(searchMedicalRecords.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(searchMedicalRecords.fulfilled, (state, action) => { state.isLoading = false; const { results, count, page } = action.payload; if (page === 1) { state.records = results; } else { state.records = [...state.records, ...results]; } state.totalCount = count; state.hasMore = state.records.length < count; state.error = null; }) .addCase(searchMedicalRecords.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); }, }); export const { clearError, setCurrentRecord, clearCurrentRecord, resetRecords } = medicalRecordSlice.actions; export default medicalRecordSlice.reducer; 