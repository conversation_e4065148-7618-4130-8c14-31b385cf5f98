import React from 'react'; import { View, ActivityIndicator, Text } from 'react-native'; import { useTheme } from '../../contexts/ThemeContext'; interface LoadingSpinnerProps { size?: 'small' | 'large'; color?: string; message?: string; overlay?: boolean; } const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'large', color, message, overlay = false, }) => { const { isDark } = useTheme(); const spinnerColor = color || (isDark ? '#007AFF' : '#007AFF'); const content = ( <View className="items-center justify-center"> <ActivityIndicator size={size} color={spinnerColor} /> {message && ( <Text className={`mt-3 text-center ${ isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground' }`}> {message} </Text> )} </View> ); if (overlay) { return ( <View className={`absolute inset-0 items-center justify-center z-50 ${ isDark ? 'bg-dark-background/80' : 'bg-background/80' }`}> {content} </View> ); } return content; }; export default LoadingSpinner; 