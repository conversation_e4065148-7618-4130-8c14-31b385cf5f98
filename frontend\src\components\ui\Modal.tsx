import React, { useEffect } from 'react'; import { createPortal } from 'react-dom'; import { X } from 'lucide-react'; import { cn } from '../../lib/utils'; import { Button } from './Button'; interface ModalProps { isOpen: boolean; onClose: () => void; title?: string; children: React.ReactNode; size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'; showCloseButton?: boolean; closeOnOverlayClick?: boolean; className?: string; } const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, size = 'md', showCloseButton = true, closeOnOverlayClick = true, className, }) => { // Handle escape key useEffect(() => { const handleEscape = (e: KeyboardEvent) => { if (e.key === 'Escape' && isOpen) { onClose(); } }; if (isOpen) { document.addEventListener('keydown', handleEscape); document.body.style.overflow = 'hidden'; } return () => { document.removeEventListener('keydown', handleEscape); document.body.style.overflow = 'unset'; }; }, [isOpen, onClose]); if (!isOpen) return null; const sizeClasses = { sm: 'max-w-md', md: 'max-w-lg', lg: 'max-w-2xl', xl: 'max-w-4xl', full: 'max-w-[95vw] max-h-[95vh]', }; const modalContent = ( <div className="fixed inset-0 z-50 flex items-center justify-center p-4"> {/* Backdrop */} <div className="absolute inset-0 frosted-overlay animate-in fade-in duration-200" onClick={closeOnOverlayClick ? onClose : undefined} /> {/* Modal */} <div className={cn( 'relative w-full macos-modal animate-in zoom-in-95 slide-in-from-bottom-2 duration-200', sizeClasses[size], className )} onClick={(e) => e.stopPropagation()} > {/* macOS Traffic Lights */} <div className="macos-traffic-lights"> <div className="traffic-light close" onClick={onClose} /> <div className="traffic-light minimize" /> <div className="traffic-light maximize" /> </div> {/* Header */} {(title || showCloseButton) && ( <div className="flex items-center justify-between p-6 pb-4"> {title && ( <h2 className="text-xl font-semibold macos-text-primary"> {title} </h2> )} {showCloseButton && ( <Button variant="ghost" size="icon" onClick={onClose} className="ml-auto" > <X className="w-4 h-4" /> </Button> )} </div> )} {/* Content */} <div className="px-6 pb-6"> {children} </div> </div> </div> ); return createPortal(modalContent, document.body); }; // Modal components for better composition const ModalHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className, }) => ( <div className={cn('px-6 py-4 border-b border-border', className)}> {children} </div> ); const ModalBody: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className, }) => ( <div className={cn('px-6 py-4', className)}> {children} </div> ); const ModalFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className, }) => ( <div className={cn('px-6 py-4 border-t border-border flex justify-end gap-3', className)}> {children} </div> ); // Confirmation Modal interface ConfirmModalProps { isOpen: boolean; onClose: () => void; onConfirm: () => void; title: string; message: string; confirmText?: string; cancelText?: string; variant?: 'default' | 'destructive'; } const ConfirmModal: React.FC<ConfirmModalProps> = ({ isOpen, onClose, onConfirm, title, message, confirmText = 'Confirm', cancelText = 'Cancel', variant = 'default', }) => { const handleConfirm = () => { onConfirm(); onClose(); }; return ( <Modal isOpen={isOpen} onClose={onClose} size="sm" showCloseButton={false}> <ModalHeader> <h3 className="text-lg font-semibold macos-text-primary">{title}</h3> </ModalHeader> <ModalBody> <p className="macos-text-secondary">{message}</p> </ModalBody> <ModalFooter> <Button variant="outline" onClick={onClose}> {cancelText} </Button> <Button variant={variant === 'destructive' ? 'destructive' : 'default'} onClick={handleConfirm} > {confirmText} </Button> </ModalFooter> </Modal> ); }; export { Modal, ModalHeader, ModalBody, ModalFooter, ConfirmModal }; 