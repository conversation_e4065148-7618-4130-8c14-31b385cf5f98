import React, { createContext, useContext, ReactNode } from 'react'; import { useSelector } from 'react-redux'; import { RootState } from '../store'; import type { ThemeMode, GlassmorphismLevel } from '../store/slices/themeSlice'; interface ThemeContextType { mode: ThemeMode; effectiveTheme: 'light' | 'dark'; systemTheme: 'light' | 'dark'; glassmorphismLevel: GlassmorphismLevel; reducedMotion: boolean; isDark: boolean; } const ThemeContext = createContext<ThemeContextType | undefined>(undefined); interface ThemeProviderProps { children: ReactNode; } export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => { const theme = useSelector((state: RootState) => state.theme); const contextValue: ThemeContextType = { mode: theme.mode, effectiveTheme: theme.effectiveTheme, systemTheme: theme.systemTheme, glassmorphismLevel: theme.glassmorphismLevel, reducedMotion: theme.reducedMotion, isDark: theme.effectiveTheme === 'dark', }; return ( <ThemeContext.Provider value={contextValue}> {children} </ThemeContext.Provider> ); }; export const useTheme = (): ThemeContextType => { const context = useContext(ThemeContext); if (context === undefined) { throw new Error('useTheme must be used within a ThemeProvider'); } return context; }; 