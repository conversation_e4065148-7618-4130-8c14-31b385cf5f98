import React, { useState } from 'react'; import { useTranslation } from 'react-i18next'; import { Button } from '../ui/Button'; import { Input } from '../ui/Input'; import { patientAPI } from '../../utils/api'; interface PatientRegistrationData { // User fields username: string; email: string; first_name: string; last_name: string; phone_number: string; date_of_birth: string; address: string; emergency_contact: string; // Patient fields blood_group: string; allergies: string; chronic_conditions: string; current_medications: string; insurance_provider: string; insurance_policy_number: string; emergency_contact_name: string; emergency_contact_phone: string; emergency_contact_relationship: string; } const PatientRegistrationForm: React.FC = () => { const { t } = useTranslation(); const [formData, setFormData] = useState<PatientRegistrationData>({ username: '', email: '', first_name: '', last_name: '', phone_number: '', date_of_birth: '', address: '', emergency_contact: '', blood_group: '', allergies: '', chronic_conditions: '', current_medications: '', insurance_provider: '', insurance_policy_number: '', emergency_contact_name: '', emergency_contact_phone: '', emergency_contact_relationship: '', }); const [currentStep, setCurrentStep] = useState(1); const [isLoading, setIsLoading] = useState(false); const [success, setSuccess] = useState(false); const [error, setError] = useState<string | null>(null); const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => { const { name, value } = e.target; setFormData(prev => ({ ...prev, [name]: value, })); }; const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); setIsLoading(true); setError(null); try { // Create patient using API const result = await patientAPI.createPatient(formData); // TODO: Handle action setSuccess(true); } catch (error: any) { console.error('Registration failed:', error); setError(error.response?.data?.message || 'Registration failed. Please try again.'); } finally { setIsLoading(false); } }; const nextStep = () => { if (currentStep < 3) { setCurrentStep(currentStep + 1); } }; const prevStep = () => { if (currentStep > 1) { setCurrentStep(currentStep - 1); } }; const renderStep1 = () => ( <div className="space-y-4"> <h3 className="text-lg font-semibold">{t('patientRegistration.personalInfo')}</h3> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.firstName')} *</label> <Input name="first_name" value={formData.first_name} onChange={handleChange} required className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.lastName')} *</label> <Input name="last_name" value={formData.last_name} onChange={handleChange} required className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.username')} *</label> <Input name="username" value={formData.username} onChange={handleChange} required className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.email')} *</label> <Input name="email" type="email" value={formData.email} onChange={handleChange} required className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.phoneNumber')}</label> <Input name="phone_number" value={formData.phone_number} onChange={handleChange} className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.dateOfBirth')}</label> <Input name="date_of_birth" type="date" value={formData.date_of_birth} onChange={handleChange} className="mt-1" /> </div> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.address')}</label> <textarea name="address" value={formData.address} onChange={handleChange} rows={3} className="mt-1 block w-full rounded-md border border-border px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" /> </div> </div> ); const renderStep2 = () => ( <div className="space-y-4"> <h3 className="text-lg font-semibold">{t('patientRegistration.medicalInfo')}</h3> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.bloodGroup')}</label> <select name="blood_group" value={formData.blood_group} onChange={handleChange} className="mt-1 block w-full rounded-md border border-border px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" > <option value="">{t('patientRegistration.selectBloodGroup')}</option> <option value="A+">A+</option> <option value="A-">A-</option> <option value="B+">B+</option> <option value="B-">B-</option> <option value="AB+">AB+</option> <option value="AB-">AB-</option> <option value="O+">O+</option> <option value="O-">O-</option> </select> </div> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.allergies')}</label> <textarea name="allergies" value={formData.allergies} onChange={handleChange} rows={3} placeholder={t('patientRegistration.enterAllergies')} className="mt-1 block w-full rounded-md border border-border px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.chronicConditions')}</label> <textarea name="chronic_conditions" value={formData.chronic_conditions} onChange={handleChange} rows={3} placeholder={t('patientRegistration.enterConditions')} className="mt-1 block w-full rounded-md border border-border px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.currentMedications')}</label> <textarea name="current_medications" value={formData.current_medications} onChange={handleChange} rows={3} placeholder={t('patientRegistration.enterMedications')} className="mt-1 block w-full rounded-md border border-border px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" /> </div> </div> ); const renderStep3 = () => ( <div className="space-y-4"> <h3 className="text-lg font-semibold">{t('patientRegistration.emergencyContact')}</h3> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.insuranceProvider')}</label> <Input name="insurance_provider" value={formData.insurance_provider} onChange={handleChange} className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.insurancePolicyNumber')}</label> <Input name="insurance_policy_number" value={formData.insurance_policy_number} onChange={handleChange} className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.emergencyContactName')}</label> <Input name="emergency_contact_name" value={formData.emergency_contact_name} onChange={handleChange} className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.emergencyContactPhone')}</label> <Input name="emergency_contact_phone" value={formData.emergency_contact_phone} onChange={handleChange} className="mt-1" /> </div> <div> <label className="block text-sm font-medium text-foreground">{t('patientRegistration.emergencyContactRelationship')}</label> <select name="emergency_contact_relationship" value={formData.emergency_contact_relationship} onChange={handleChange} className="mt-1 block w-full rounded-md border border-border px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" > <option value="">{t('patientRegistration.relationship.other')}</option> <option value="spouse">{t('patientRegistration.relationship.spouse')}</option> <option value="parent">{t('patientRegistration.relationship.parent')}</option> <option value="child">{t('patientRegistration.relationship.child')}</option> <option value="sibling">{t('patientRegistration.relationship.sibling')}</option> <option value="friend">{t('patientRegistration.relationship.friend')}</option> <option value="other">{t('patientRegistration.relationship.other')}</option> </select> </div> </div> </div> ); return ( <div className="max-w-4xl mx-auto p-6 bg-background rounded-lg shadow"> <div className="mb-8"> <h2 className="text-2xl font-bold text-foreground">{t('patientRegistration.title')}</h2> <p className="text-muted-foreground mt-2">{t('patientRegistration.subtitle')}</p> {/* Progress indicator */} <div className="mt-4"> <div className="flex items-center"> {[1, 2, 3].map((step) => ( <div key={step} className="flex items-center"> <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${ step <= currentStep ? 'bg-blue-600 text-white' : 'bg-accent text-muted-foreground' }`} > {step} </div> {step < 3 && ( <div className={`w-16 h-1 mx-2 ${ step < currentStep ? 'bg-blue-600' : 'bg-accent' }`} /> )} </div> ))} </div> <div className="flex justify-between mt-2 text-sm text-muted-foreground"> <span>{t('patientRegistration.personalInfo')}</span> <span>{t('patientRegistration.medicalInfo')}</span> <span>{t('patientRegistration.emergencyContact')}</span> </div> </div> </div> {/* Success Message */} {success && ( <div className="mb-6 p-4 bg-green-50 border rounded-lg"> <div className="flex items-center"> <div className="flex-shrink-0"> <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor"> <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /> </svg> </div> <div className="ml-3"> <p className="text-sm font-medium text-emerald-700 dark:text-emerald-400"> {t('patients.registrationSuccess')} </p> </div> </div> </div> )} {/* Error Message */} {error && ( <div className="mb-6 p-4 bg-red-50 border rounded-lg"> <div className="flex items-center"> <div className="flex-shrink-0"> <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor"> <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /> </svg> </div> <div className="ml-3"> <p className="text-sm font-medium text-rose-700 dark:text-rose-400"> {error} </p> </div> </div> </div> )} <form onSubmit={handleSubmit}> {currentStep === 1 && renderStep1()} {currentStep === 2 && renderStep2()} {currentStep === 3 && renderStep3()} <div className="mt-8 flex justify-between"> <Button type="button" variant="outline" onClick={prevStep} disabled={currentStep === 1} > {t('patientRegistration.previousStep')} </Button> {currentStep < 3 ? ( <Button type="button" onClick={nextStep}> {t('patientRegistration.nextStep')} </Button> ) : ( <Button type="submit" disabled={isLoading}> {isLoading ? t('common.loading') : t('patientRegistration.register')} </Button> )} </div> </form> </div> ); }; export default PatientRegistrationForm; 