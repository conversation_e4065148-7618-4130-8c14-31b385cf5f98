/** * API Response Types - Consistent with HMS Backend */ export interface ApiResponse<T = any> { data?: T; error?: string; message?: string; details?: any; status_code?: number; } export interface PaginatedResponse<T = any> { count: number; next: string | null; previous: string | null; results: T[]; } export class ApiError extends Error { public status: number; public details?: any; constructor(message: string, status: number, details?: any) { super(message); this.name = 'ApiError'; this.status = status; this.details = details; } } export interface ApiConfig { baseURL: string; timeout: number; headers: Record<string, string>; } export interface RequestConfig { method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'; url: string; data?: any; params?: any; headers?: Record<string, string>; } 