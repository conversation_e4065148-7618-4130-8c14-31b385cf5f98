import React, { useState } from 'react'; import { MessageSquare, X, Minimize2, Maximize2 } from 'lucide-react'; import AIChat from './AIChat'; interface FloatingAIChatProps { className?: string; } const FloatingAIChat: React.FC<FloatingAIChatProps> = ({ className = '' }) => { const [isOpen, setIsOpen] = useState(false); const [isMinimized, setIsMinimized] = useState(false); const toggleChat = () => { setIsOpen(!isOpen); setIsMinimized(false); }; const minimizeChat = () => { setIsMinimized(!isMinimized); }; const closeChat = () => { setIsOpen(false); setIsMinimized(false); }; return ( <> {/* Floating Chat Button */} {!isOpen && ( <button onClick={toggleChat} className={`fixed bottom-6 right-6 z-50 p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ${className}`} title="Open AI Assistant" > <MessageSquare className="w-6 h-6" /> </button> )} {/* Floating Chat Window */} {isOpen && ( <div className={`fixed bottom-6 right-6 z-50 bg-background dark:bg-gray-900 rounded-lg shadow-2xl border border-border dark:border-gray-700 transition-all duration-300 ${ isMinimized ? 'w-80 h-16' : 'w-96 h-[600px]' }`} > {/* Chat Header */} <div className="flex items-center justify-between p-4 border-b border-border dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-t-lg"> <div className="flex items-center gap-2"> <div className="p-1 bg-blue-100 dark:bg-blue-900 rounded"> <MessageSquare className="w-4 h-4 text-sky-700 dark:text-sky-400 dark:text-blue-400" /> </div> <h3 className="font-semibold text-foreground dark:text-white text-sm"> AI Assistant </h3> </div> <div className="flex items-center gap-1"> <button onClick={minimizeChat} className="p-1 hover:bg-accent dark:hover:bg-gray-600 rounded transition-colors" title={isMinimized ? "Maximize" : "Minimize"} > {isMinimized ? ( <Maximize2 className="w-4 h-4 text-muted-foreground dark:text-gray-400" /> ) : ( <Minimize2 className="w-4 h-4 text-muted-foreground dark:text-gray-400" /> )} </button> <button onClick={closeChat} className="p-1 hover:bg-accent dark:hover:bg-gray-600 rounded transition-colors" title="Close" > <X className="w-4 h-4 text-muted-foreground dark:text-gray-400" /> </button> </div> </div> {/* Chat Content */} {!isMinimized && ( <div className="h-[calc(100%-4rem)]"> <AIChat conversationType="general" className="h-full border-0 rounded-none rounded-b-lg" /> </div> )} </div> )} </> ); }; export default FloatingAIChat; 