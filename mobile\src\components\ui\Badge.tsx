import React from 'react';
import { View, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'destructive' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
}) => {
  const { isDark } = useTheme();

  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return isDark ? 'bg-green-900/20 border-green-700/50' : 'bg-green-100/80 border-green-200/50';
      case 'warning':
        return isDark ? 'bg-yellow-900/20 border-yellow-700/50' : 'bg-yellow-100/80 border-yellow-200/50';
      case 'error':
      case 'destructive':
        return isDark ? 'bg-red-900/20 border-red-700/50' : 'bg-red-100/80 border-red-200/50';
      case 'info':
        return isDark ? 'bg-blue-900/20 border-blue-700/50' : 'bg-blue-100/80 border-blue-200/50';
      case 'secondary':
        return isDark ? 'bg-dark-muted border-dark-border' : 'bg-muted border-border';
      default:
        return isDark ? 'bg-dark-muted border-dark-border' : 'bg-muted border-border';
    }
  };

  const getTextStyles = () => {
    const sizeStyles = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
    }[size];

    const variantTextStyles = {
      success: isDark ? 'text-green-400' : 'text-green-800',
      warning: isDark ? 'text-yellow-400' : 'text-yellow-800',
      error: isDark ? 'text-red-400' : 'text-red-800',
      destructive: isDark ? 'text-red-400' : 'text-red-800',
      info: isDark ? 'text-blue-400' : 'text-blue-800',
      secondary: isDark ? 'text-dark-foreground' : 'text-foreground',
      default: isDark ? 'text-dark-foreground' : 'text-foreground',
    }[variant];

    return `font-medium ${sizeStyles} ${variantTextStyles}`;
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1';
      case 'md':
        return 'px-3 py-1';
      case 'lg':
        return 'px-4 py-2';
      default:
        return 'px-3 py-1';
    }
  };

  return (
    <View className={`rounded-full border ${getVariantStyles()} ${getSizeStyles()} ${className}`}>
      <Text className={getTextStyles()}>
        {children}
      </Text>
    </View>
  );
};

export default Badge;
