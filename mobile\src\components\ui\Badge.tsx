import React from 'react'; import { View, Text } from 'react-native'; import { useTheme } from '../../contexts/ThemeContext'; interface BadgeProps { children: React.ReactNode; variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'destructive' | 'secondary'; size?: 'sm' | 'md' | 'lg'; className?: string; } const Badge: React.FC<BadgeProps> = ({ children, variant = 'default', size = 'md', className = '', }) => { const { isDark } = useTheme(); const getVariantStyles = () => { switch (variant) { case 'success': return 'bg-emerald-50/80 border-emerald-200/50 dark:bg-emerald-950/20 dark:border-emerald-800/50'; case 'warning': return 'bg-amber-50/80 border-amber-200/50 dark:bg-amber-950/20 dark:border-amber-800/50'; case 'error': case 'destructive': return 'bg-rose-50/80 border-rose-200/50 dark:bg-rose-950/20 dark:border-rose-800/50'; case 'info': return 'bg-sky-50/80 border-sky-200/50 dark:bg-sky-950/20 dark:border-sky-800/50'; case 'secondary': return isDark ? 'bg-dark-muted border-dark-border' : 'bg-muted border-border'; default: return isDark ? 'bg-dark-muted border-dark-border' : 'bg-muted border-border'; } }; const getTextStyles = () => { const sizeStyles = { sm: 'text-xs', md: 'text-sm', lg: 'text-base', }[size]; const variantTextStyles = { success: isDark ? 'text-green-400' : 'text-emerald-700 dark:text-emerald-400', warning: isDark ? 'text-yellow-400' : 'text-amber-700 dark:text-amber-400', error: isDark ? 'text-red-400' : 'text-rose-700 dark:text-rose-400', destructive: isDark ? 'text-red-400' : 'text-rose-700 dark:text-rose-400', info: isDark ? 'text-blue-400' : 'text-sky-700 dark:text-sky-400', secondary: isDark ? 'text-dark-foreground' : 'text-foreground', default: isDark ? 'text-dark-foreground' : 'text-foreground', }[variant]; return `font-medium ${sizeStyles} ${variantTextStyles}`; }; const getSizeStyles = () => { switch (size) { case 'sm': return 'px-2 py-1'; case 'md': return 'px-3 py-1'; case 'lg': return 'px-4 py-2'; default: return 'px-3 py-1'; } }; return ( <View className={`rounded-full border ${getVariantStyles()} ${getSizeStyles()} ${className}`}> <Text className={getTextStyles()}> {children} </Text> </View> ); }; export default Badge; 