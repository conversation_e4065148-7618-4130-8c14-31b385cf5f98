import i18n from 'i18next'; import { initReactI18next } from 'react-i18next'; import { I18nManager } from 'react-native'; import AsyncStorage from '@react-native-async-storage/async-storage'; // Import translation files import en from '../locales/en.json'; import ar from '../locales/ar.json'; const LANGUAGE_STORAGE_KEY = 'app_language'; // Language detector const languageDetector = { type: 'languageDetector' as const, async: true, detect: async (callback: (lng: string) => void) => { try { // Try to get saved language from AsyncStorage const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY); if (savedLanguage) { callback(savedLanguage); return; } // Fallback to device language or default to English const deviceLanguage = I18nManager.isRTL ? 'ar' : 'en'; callback(deviceLanguage); } catch (error) { console.log('Error detecting language:', error); callback('en'); // Fallback to English } }, init: () => {}, cacheUserLanguage: async (lng: string) => { try { await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, lng); } catch (error) { console.log('Error saving language:', error); } }, }; i18n .use(languageDetector) .use(initReactI18next) .init({ compatibilityJSON: 'v3', fallbackLng: 'en', debug: __DEV__, resources: { en: { translation: en, }, ar: { translation: ar, }, }, interpolation: { escapeValue: false, // React already does escaping }, react: { useSuspense: false, }, }); // Function to change language and update RTL export const changeLanguage = async (language: string) => { try { await i18n.changeLanguage(language); // Update RTL layout const isRTL = language === 'ar'; I18nManager.allowRTL(isRTL); I18nManager.forceRTL(isRTL); // Save language preference await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language); return true; } catch (error) { console.log('Error changing language:', error); return false; } }; // Function to get current language export const getCurrentLanguage = () => i18n.language; // Function to check if current language is RTL export const isRTL = () => i18n.language === 'ar'; export default i18n; 