import React, { useState } from 'react'; import { useTranslation } from 'react-i18next'; import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Linkedin, Instagram, ArrowRight } from 'lucide-react'; import FormField from '../../shared/components/forms/FormField'; import { Button } from '../ui/Button'; const Footer: React.FC = () => { const { t } = useTranslation(); const [newsletterEmail, setNewsletterEmail] = useState(''); const [isSubscribing, setIsSubscribing] = useState(false); const footerLinks = { product: [ { label: t('footer.product.features'), href: '#features' }, { label: t('footer.product.pricing'), href: '#pricing' }, { label: t('footer.product.demo'), href: '#demo' }, { label: t('footer.product.integrations'), href: '#integrations' } ], company: [ { label: t('footer.company.about'), href: '#about' }, { label: t('footer.company.careers'), href: '#careers' }, { label: t('footer.company.news'), href: '#news' }, { label: t('footer.company.contact'), href: '#contact' } ], resources: [ { label: t('footer.resources.documentation'), href: '#docs' }, { label: t('footer.resources.support'), href: '#support' }, { label: t('footer.resources.community'), href: '#community' }, { label: t('footer.resources.blog'), href: '#blog' } ], legal: [ { label: t('footer.legal.privacy'), href: '#privacy' }, { label: t('footer.legal.terms'), href: '#terms' }, { label: t('footer.legal.security'), href: '#security' }, { label: t('footer.legal.compliance'), href: '#compliance' } ] }; const socialLinks = [ { icon: Facebook, href: '#', label: 'Facebook' }, { icon: Twitter, href: '#', label: 'Twitter' }, { icon: Linkedin, href: '#', label: 'LinkedIn' }, { icon: Instagram, href: '#', label: 'Instagram' } ]; return ( <footer className="bg-gray-900 text-white"> {/* Newsletter Section */} <div className="border-b border-gray-800"> <div className="container mx-auto px-6 py-12"> <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center"> <div> <h3 className="text-2xl font-bold mb-2"> {t('footer.newsletter.title')} </h3> <p className="text-gray-400"> {t('footer.newsletter.description')} </p> </div> <div className="flex flex-col sm:flex-row gap-4"> <div className="flex-1"> <FormField name="newsletter-email" type="email" value={newsletterEmail} onChange={setNewsletterEmail} placeholder={t('footer.newsletter.placeholder')} icon={Mail} variant="glass" className="bg-gray-800 border-gray-700 text-white placeholder-gray-400" /> </div> <Button onClick={async () => { setIsSubscribing(true); // TODO: Implement newsletter subscription setTimeout(() => setIsSubscribing(false), 1000); }} disabled={isSubscribing || !newsletterEmail} className="bg-blue-600 hover:bg-blue-700 px-6 py-3 font-semibold" variant="glass" > {isSubscribing ? ( <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" /> ) : ( <> {t('footer.newsletter.subscribe')} <ArrowRight className="w-4 h-4 ml-2" /> </> )} </Button> </div> </div> </div> </div> {/* Main Footer Content */} <div className="container mx-auto px-6 py-16"> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8"> {/* Brand Section */} <div className="lg:col-span-2"> <div className="flex items-center space-x-2 mb-6"> <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center"> <Heart className="w-6 h-6 text-white" /> </div> <span className="text-2xl font-bold">HMS AI</span> </div> <p className="text-gray-400 mb-6 leading-relaxed"> {t('footer.brand.description')} </p> {/* Contact Info */} <div className="space-y-3"> <div className="flex items-center space-x-3 text-gray-400"> <Mail className="w-5 h-5" /> <span><EMAIL></span> </div> <div className="flex items-center space-x-3 text-gray-400"> <Phone className="w-5 h-5" /> <span>+****************</span> </div> <div className="flex items-center space-x-3 text-gray-400"> <MapPin className="w-5 h-5" /> <span>{t('footer.brand.address')}</span> </div> </div> </div> {/* Product Links */} <div> <h4 className="text-lg font-semibold mb-6">{t('footer.product.title')}</h4> <ul className="space-y-3"> {footerLinks.product.map((link, index) => ( <li key={index}> <a href={link.href} className="text-gray-400 hover:text-white transition-colors" > {link.label} </a> </li> ))} </ul> </div> {/* Company Links */} <div> <h4 className="text-lg font-semibold mb-6">{t('footer.company.title')}</h4> <ul className="space-y-3"> {footerLinks.company.map((link, index) => ( <li key={index}> <a href={link.href} className="text-gray-400 hover:text-white transition-colors" > {link.label} </a> </li> ))} </ul> </div> {/* Resources Links */} <div> <h4 className="text-lg font-semibold mb-6">{t('footer.resources.title')}</h4> <ul className="space-y-3"> {footerLinks.resources.map((link, index) => ( <li key={index}> <a href={link.href} className="text-gray-400 hover:text-white transition-colors" > {link.label} </a> </li> ))} </ul> </div> {/* Legal Links */} <div> <h4 className="text-lg font-semibold mb-6">{t('footer.legal.title')}</h4> <ul className="space-y-3"> {footerLinks.legal.map((link, index) => ( <li key={index}> <a href={link.href} className="text-gray-400 hover:text-white transition-colors" > {link.label} </a> </li> ))} </ul> </div> </div> </div> {/* Bottom Footer */} <div className="border-t border-gray-800"> <div className="container mx-auto px-6 py-8"> <div className="flex flex-col md:flex-row justify-between items-center"> <div className="text-gray-400 mb-4 md:mb-0"> © 2024 HMS AI. {t('footer.copyright')} </div> {/* Social Links */} <div className="flex items-center space-x-4"> {socialLinks.map((social, index) => { const Icon = social.icon; return ( <a key={index} href={social.href} aria-label={social.label} className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-700 transition-colors" > <Icon className="w-5 h-5" /> </a> ); })} </div> </div> </div> </div> </footer> ); }; export default Footer; 