/** * Shared API Types for HMS Frontend * Provides consistent type definitions for API responses and requests */ import React from 'react'; export interface ApiResponse<T = any> { data: T; message?: string; success: boolean; errors?: Record<string, string[]>; status?: number; } export interface PaginatedResponse<T = any> extends ApiResponse<T[]> { pagination: { page: number; totalPages: number; totalCount: number; pageSize: number; hasNext: boolean; hasPrevious: boolean; }; } export interface ApiError { message: string; status: number; code?: string; details?: Record<string, any>; } export interface RequestConfig { params?: Record<string, any>; headers?: Record<string, string>; timeout?: number; retries?: number; } export interface PaginationParams { page?: number; pageSize?: number; ordering?: string; search?: string; } export interface FilterParams { [key: string]: any; } export interface SortParams { field: string; direction: 'asc' | 'desc'; } export interface BulkActionParams { ids: (string | number)[]; action: string; data?: Record<string, any>; } export interface ApiEndpoints { list: string; detail: string; create?: string; update?: string; delete?: string; [key: string]: string | undefined; } export interface ApiServiceConfig { baseURL: string; endpoints: ApiEndpoints; defaultParams?: Record<string, any>; timeout?: number; retries?: number; } // Common response status types export type ApiStatus = 'idle' | 'loading' | 'success' | 'error'; // Generic CRUD operation types export type CrudOperation = 'create' | 'read' | 'update' | 'delete' | 'list'; // API method types export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'; // Loading state for different operations export interface LoadingState { [key: string]: boolean; list: boolean; create: boolean; update: boolean; delete: boolean; } // Error state for different operations export interface ErrorState { [key: string]: string | null; list: string | null; create: string | null; update: string | null; delete: string | null; } // Generic entity interface export interface BaseEntity { id: string | number; created_at?: string; updated_at?: string; } // Search and filter configuration export interface SearchConfig { fields: string[]; placeholder?: string; debounceMs?: number; } export interface FilterConfig { field: string; type: 'select' | 'date' | 'range' | 'boolean'; options?: Array<{ label: string; value: any }>; placeholder?: string; } // API cache configuration export interface CacheConfig { enabled: boolean; ttl: number; // Time to live in milliseconds key?: string; } // Retry configuration export interface RetryConfig { attempts: number; delay: number; backoff: 'linear' | 'exponential'; } // Request interceptor types export interface RequestInterceptor { onRequest?: (config: any) => any; onRequestError?: (error: any) => any; } export interface ResponseInterceptor { onResponse?: (response: any) => any; onResponseError?: (error: any) => any; } // Validation types export interface ValidationRule { type: 'required' | 'email' | 'min' | 'max' | 'pattern' | 'custom'; value?: any; message: string; validator?: (value: any) => boolean; } export interface ValidationResult { isValid: boolean; errors: Record<string, string[]>; } // Form field types export interface FormField { name: string; label: string; type: string; required?: boolean; validation?: ValidationRule[]; placeholder?: string; options?: Array<{ label: string; value: any }>; } // API hook return types export interface UseApiResult<T> { data: T | null; loading: boolean; error: string | null; refetch: () => Promise<void>; mutate: (data: T) => void; } export interface UseCrudResult<T> { items: T[]; selectedItem: T | null; loading: LoadingState; error: ErrorState; pagination: PaginatedResponse<T>['pagination'] | null; // Actions fetchAll: (params?: PaginationParams & FilterParams) => Promise<void>; fetchById: (id: string | number) => Promise<void>; create: (data: Partial<T>) => Promise<T>; update: (id: string | number, data: Partial<T>) => Promise<T>; delete: (id: string | number) => Promise<void>; bulkDelete: (ids: (string | number)[]) => Promise<void>; // Utilities selectItem: (item: T | null) => void; clearError: (operation?: CrudOperation) => void; reset: () => void; } // Theme and styling types export interface ThemeColors { primary: string; secondary: string; accent: string; background: string; foreground: string; muted: string; border: string; success: string; warning: string; error: string; info: string; } export interface GlassmorphismConfig { blur: number; opacity: number; border: boolean; shadow: boolean; } // Localization types export interface LocaleConfig { code: string; name: string; direction: 'ltr' | 'rtl'; flag?: string; } // Component prop types export interface BaseComponentProps { className?: string; children?: React.ReactNode; 'data-testid'?: string; } export interface LoadingProps extends BaseComponentProps { size?: 'sm' | 'md' | 'lg'; variant?: 'spinner' | 'skeleton' | 'pulse'; } export interface ErrorProps extends BaseComponentProps { message: string; retry?: () => void; variant?: 'inline' | 'card' | 'toast'; } // Export utility type helpers export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>; export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>; export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>; // Ensure all types are properly exported export type { ApiEndpoints, ApiServiceConfig, BaseEntity, ApiResponse, PaginatedResponse, ApiError, RequestConfig, PaginationParams, FilterParams, BulkActionParams }; 