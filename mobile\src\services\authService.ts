import { apiService } from './api'; import { LoginCredentials, RegisterData, AuthResponse, User } from '../types/auth'; import { TokenManager } from '../utils/tokenManager'; class AuthService { async login(credentials: LoginCredentials): Promise<AuthResponse> { const response = await apiService.post<AuthResponse>('/auth/login/', credentials); // Store tokens securely await TokenManager.storeTokens(response.tokens); return response; } async register(data: RegisterData): Promise<AuthResponse> { const response = await apiService.post<AuthResponse>('/auth/register/', data); // Store tokens securely await TokenManager.storeTokens(response.tokens); return response; } async logout(): Promise<void> { try { // Call backend logout endpoint await apiService.post('/auth/logout/'); } catch (error) { // Continue with local logout even if backend call fails console.warn('Backend logout failed:', error); } finally { // Clear local tokens await TokenManager.clearTokens(); } } async getProfile(): Promise<User> { return apiService.get<User>('/auth/profile/'); } async updateProfile(data: Partial<User>): Promise<User> { return apiService.patch<User>('/auth/profile/', data); } async getStoredToken(): Promise<string | null> { return TokenManager.getAccessToken(); } async getStoredRefreshToken(): Promise<string | null> { return TokenManager.getRefreshToken(); } async isAuthenticated(): Promise<boolean> { return TokenManager.isAuthenticated(); } } export const authService = new AuthService(); 