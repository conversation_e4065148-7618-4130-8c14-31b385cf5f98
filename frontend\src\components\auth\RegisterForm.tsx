import React, { useState } from 'react'; import { useTranslation } from 'react-i18next'; import { Link, useNavigate } from 'react-router-dom'; import { useDispatch, useSelector } from 'react-redux'; import { Heart, Building, User, Mail, Lock, Phone, ArrowLeft, UserPlus } from 'lucide-react'; import { Button } from '../ui/Button'; import Form<PERSON>ield from '../../shared/components/forms/FormField'; import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'; import LanguageSwitcher from '../ui/LanguageSwitcher'; import { register } from '../../store/slices/authSlice'; import type { AppDispatch, RootState } from '../../store'; import type { RegisterData } from '../../types/auth'; const RegisterForm: React.FC = () => { const { t } = useTranslation(); const dispatch = useDispatch<AppDispatch>(); const navigate = useNavigate(); const { isLoading, error } = useSelector((state: RootState) => state.auth); const [formData, setFormData] = useState({ username: '', email: '', password: '', password_confirm: '', first_name: '', last_name: '', role: 'admin' as const, phone_number: '', agreeToTerms: false, subscribeNewsletter: false }); const [errors, setErrors] = useState<Record<string, string>>({}); const [touched, setTouched] = useState<Record<string, boolean>>({}); const handleFieldChange = (name: string, value: any) => { setFormData(prev => ({ ...prev, [name]: value })); // Clear error when user starts typing if (errors[name]) { setErrors(prev => ({ ...prev, [name]: '' })); } }; const handleFieldBlur = (name: string) => { setTouched(prev => ({ ...prev, [name]: true })); // Validate field on blur validateField(name, formData[name as keyof typeof formData]); }; const validateField = (name: string, value: any) => { let error = ''; switch (name) { case 'email': if (!value) error = t('validation.required'); else if (!/\S+@\S+\.\S+/.test(value)) error = t('validation.invalidEmail'); break; case 'password': if (!value) error = t('validation.required'); else if (value.length < 8) error = t('validation.passwordTooShort'); break; case 'password_confirm': if (!value) error = t('validation.required'); else if (value !== formData.password) error = t('register.passwordMismatch'); break; case 'first_name': case 'last_name': if (!value) error = t('validation.required'); break; case 'agreeToTerms': if (!value) error = t('register.mustAgreeToTerms'); break; } setErrors(prev => ({ ...prev, [name]: error })); return !error; }; const validateForm = (): boolean => { const fieldsToValidate = ['email', 'password', 'password_confirm', 'first_name', 'last_name', 'agreeToTerms']; let isValid = true; fieldsToValidate.forEach(field => { const fieldValid = validateField(field, formData[field as keyof typeof formData]); if (!fieldValid) isValid = false; }); setTouched(fieldsToValidate.reduce((acc, field) => ({ ...acc, [field]: true }), {})); return isValid; }; const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); if (!validateForm()) { return; } // Create username from email if not provided const username = formData.username || formData.email.split('@')[0]; const registerData: RegisterData = { username, email: formData.email, password: formData.password, password_confirm: formData.password_confirm, first_name: formData.first_name, last_name: formData.last_name, role: formData.role, phone_number: formData.phone_number || undefined, }; try { const result = await dispatch(register(registerData)); if (register.fulfilled.match(result)) { navigate('/dashboard'); } } catch (error) { console.error('Registration failed:', error); } }; const roleOptions = [ { label: t('register.roles.admin'), value: 'admin' }, { label: t('register.roles.doctor'), value: 'doctor' }, { label: t('register.roles.nurse'), value: 'nurse' }, { label: t('register.roles.patient'), value: 'patient' }, { label: t('register.roles.receptionist'), value: 'receptionist' } ]; return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 relative overflow-hidden"> {/* Background Pattern */} <div className="absolute inset-0 opacity-30"> <div className="absolute inset-0" style={{ backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23007AFF' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")` }}></div> </div> {/* Header Controls */} <div className="absolute top-4 right-4 flex items-center gap-3 z-10"> <LanguageSwitcher /> </div> {/* Back to Home */} <div className="absolute top-4 left-4 z-10"> <Link to="/" className="macos-button flex items-center gap-2 px-4 py-2 text-sm font-medium macos-transition hover:scale-105" > <ArrowLeft className="w-4 h-4" /> {t('common.backToHome')} </Link> </div> <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"> <div className="max-w-2xl w-full space-y-8"> <Card variant="glass" className="shadow-2xl"> <CardHeader className="text-center space-y-4"> <div className="mx-auto w-16 h-16 macos-accent-bg rounded-2xl flex items-center justify-center shadow-lg"> <Heart className="w-8 h-8 text-white" /> </div> <CardTitle className="text-3xl font-bold macos-text-primary"> {t('register.title')} </CardTitle> <CardDescription className="text-lg macos-text-secondary"> {t('register.subtitle')} </CardDescription> </CardHeader> <CardContent className="space-y-6"> {error && ( <div className="status-error-bg rounded-lg p-4"> <div className="flex items-start gap-2"> <div className="w-5 h-5 status-error rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"> <span className="text-white text-xs">!</span> </div> <div className="flex-1"> <h4 className="status-error font-medium text-sm mb-1">Registration Failed</h4> <div className="status-error text-sm whitespace-pre-line">{error}</div> </div> </div> </div> )} <form onSubmit={handleSubmit} className="space-y-6"> {/* Username */} <FormField name="username" label={t('register.username')} type="text" value={formData.username} onChange={(value) => handleFieldChange('username', value)} onBlur={() => handleFieldBlur('username')} placeholder={t('register.usernamePlaceholder')} icon={User} variant="glass" className="h-12" description={t('register.usernameHint')} /> {/* Role Selection */} <FormField name="role" label={t('register.role')} type="select" value={formData.role} onChange={(value) => handleFieldChange('role', value)} options={roleOptions} icon={Building} variant="glass" required /> {/* Name Fields */} <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <FormField name="first_name" label={t('register.firstName')} type="text" value={formData.first_name} onChange={(value) => handleFieldChange('first_name', value)} onBlur={() => handleFieldBlur('first_name')} placeholder={t('register.firstNamePlaceholder')} required variant="glass" className="h-12" error={errors.first_name} touched={touched.first_name} /> <FormField name="last_name" label={t('register.lastName')} type="text" value={formData.last_name} onChange={(value) => handleFieldChange('last_name', value)} onBlur={() => handleFieldBlur('last_name')} placeholder={t('register.lastNamePlaceholder')} required variant="glass" className="h-12" error={errors.last_name} touched={touched.last_name} /> </div> {/* Contact Information */} <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <FormField name="email" label={t('register.email')} type="email" value={formData.email} onChange={(value) => handleFieldChange('email', value)} onBlur={() => handleFieldBlur('email')} placeholder={t('register.emailPlaceholder')} required icon={Mail} variant="glass" className="h-12" error={errors.email} touched={touched.email} /> <FormField name="phone_number" label={t('register.phone')} type="tel" value={formData.phone_number} onChange={(value) => handleFieldChange('phone_number', value)} placeholder={t('register.phonePlaceholder')} icon={Phone} variant="glass" className="h-12" /> </div> {/* Password Fields */} <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <FormField name="password" label={t('register.password')} type="password" value={formData.password} onChange={(value) => handleFieldChange('password', value)} onBlur={() => handleFieldBlur('password')} placeholder={t('register.passwordPlaceholder')} required icon={Lock} variant="glass" className="h-12" showPasswordToggle error={errors.password} touched={touched.password} description="Password must be at least 8 characters and not too common." /> <FormField name="password_confirm" label={t('register.confirmPassword')} type="password" value={formData.password_confirm} onChange={(value) => handleFieldChange('password_confirm', value)} onBlur={() => handleFieldBlur('password_confirm')} placeholder={t('register.confirmPasswordPlaceholder')} required variant="glass" className="h-12" showPasswordToggle error={errors.password_confirm} touched={touched.password_confirm} /> </div> {/* Checkboxes */} <div className="space-y-4"> <FormField name="agreeToTerms" label={ <> {t('register.agreeToTerms')}{' '} <Link to="/terms" className="macos-accent-text hover:opacity-80 macos-transition"> {t('register.termsOfService')} </Link>{' '} {t('register.and')}{' '} <Link to="/privacy" className="macos-accent-text hover:opacity-80 macos-transition"> {t('register.privacyPolicy')} </Link> </> } type="checkbox" checked={formData.agreeToTerms} onChange={(value) => handleFieldChange('agreeToTerms', value)} onBlur={() => handleFieldBlur('agreeToTerms')} required error={errors.agreeToTerms} touched={touched.agreeToTerms} /> <FormField name="subscribeNewsletter" label={t('register.subscribeNewsletter')} type="checkbox" checked={formData.subscribeNewsletter} onChange={(value) => handleFieldChange('subscribeNewsletter', value)} /> </div> {/* Submit Button */} <Button type="submit" disabled={isLoading} className="w-full h-12 text-base font-semibold" variant="glass" > <div className="flex items-center gap-2"> {isLoading ? ( <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" /> ) : ( <UserPlus className="w-5 h-5" /> )} {isLoading ? t('common.loading') : t('register.createAccount')} </div> </Button> {/* Sign In Link */} <div className="text-center"> <p className="text-sm macos-text-secondary"> {t('register.alreadyHaveAccount')}{' '} <Link to="/login" className="font-semibold macos-accent-text hover:opacity-80 macos-transition" > {t('register.signIn')} </Link> </p> </div> </form> </CardContent> </Card> </div> </div> </div> ); }; export default RegisterForm; 