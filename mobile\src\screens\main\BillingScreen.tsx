import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, RefreshControl, Alert } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { RootState } from '../../store'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; interface Bill { id: number; bill_number: string; service_type: string; service_description: string; amount: number; currency: string; issue_date: string; due_date: string; status: 'pending' | 'paid' | 'overdue' | 'cancelled'; doctor_name?: string; department: string; payment_method?: string; payment_date?: string; insurance_covered?: number; patient_portion: number; } interface PaymentHistory { id: number; bill_id: number; amount: number; currency: string; payment_date: string; payment_method: string; transaction_id: string; status: 'completed' | 'pending' | 'failed'; } const BillingScreen: React.FC = () => { const navigation = useNavigation(); const dispatch = useDispatch(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const [bills, setBills] = useState<Bill[]>([]); const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]); const [isLoading, setIsLoading] = useState(true); const [refreshing, setRefreshing] = useState(false); const [activeTab, setActiveTab] = useState<'bills' | 'payments'>('bills'); const [filter, setFilter] = useState<'all' | 'pending' | 'paid' | 'overdue'>('all'); // Mock data - replace with actual API calls const mockBills: Bill[] = [ { id: 1, bill_number: 'INV-2024-001', service_type: 'Consultation', service_description: 'General Medicine Consultation', amount: 500, currency: 'SAR', issue_date: '2024-01-15', due_date: '2024-02-15', status: 'pending', doctor_name: 'Dr. Ahmed Hassan', department: 'General Medicine', insurance_covered: 300, patient_portion: 200 }, { id: 2, bill_number: 'INV-2024-002', service_type: 'Lab Tests', service_description: 'Complete Blood Count + Glucose Test', amount: 250, currency: 'SAR', issue_date: '2024-01-10', due_date: '2024-02-10', status: 'paid', department: 'Laboratory', payment_method: 'Credit Card', payment_date: '2024-01-12', insurance_covered: 200, patient_portion: 50 }, { id: 3, bill_number: 'INV-2023-045', service_type: 'Emergency', service_description: 'Emergency Room Visit', amount: 1200, currency: 'SAR', issue_date: '2023-12-20', due_date: '2024-01-20', status: 'overdue', doctor_name: 'Dr. Sarah Ahmed', department: 'Emergency', insurance_covered: 800, patient_portion: 400 } ]; const mockPaymentHistory: PaymentHistory[] = [ { id: 1, bill_id: 2, amount: 50, currency: 'SAR', payment_date: '2024-01-12', payment_method: 'Credit Card', transaction_id: 'TXN-*********', status: 'completed' }, { id: 2, bill_id: 4, amount: 150, currency: 'SAR', payment_date: '2024-01-05', payment_method: 'Bank Transfer', transaction_id: 'TXN-*********', status: 'completed' } ]; useEffect(() => { loadBillingData(); }, []); const loadBillingData = async () => { try { setIsLoading(true); // TODO: Replace with actual API calls // const [billsData, paymentsData] = await Promise.all([ // billingService.getMyBills(), // billingService.getPaymentHistory() // ]); setBills(mockBills); setPaymentHistory(mockPaymentHistory); } catch (error) { Alert.alert(t('common.error'), t('errors.networkError')); } finally { setIsLoading(false); } }; const onRefresh = async () => { setRefreshing(true); await loadBillingData(); setRefreshing(false); }; const getFilteredBills = () => { switch (filter) { case 'pending': return bills.filter(bill => bill.status === 'pending'); case 'paid': return bills.filter(bill => bill.status === 'paid'); case 'overdue': return bills.filter(bill => bill.status === 'overdue'); default: return bills; } }; const getStatusColor = (status: string) => { switch (status) { case 'paid': return 'success'; case 'pending': return 'warning'; case 'overdue': return 'destructive'; case 'cancelled': return 'secondary'; default: return 'secondary'; } }; const getStatusIcon = (status: string) => { switch (status) { case 'paid': return 'checkmark-circle'; case 'pending': return 'time'; case 'overdue': return 'warning'; case 'cancelled': return 'close-circle'; default: return 'help-circle'; } }; const handleBillPress = (bill: Bill) => { navigation.navigate('BillDetail' as never, { billId: bill.id }); }; const handlePayBill = (bill: Bill) => { Alert.alert( t('billing.payBill'), t('billing.payConfirm', { amount: bill.patient_portion, currency: bill.currency }), [ { text: t('common.cancel'), style: 'cancel' }, { text: t('billing.pay'), onPress: () => { navigation.navigate('PaymentMethod' as never, { billId: bill.id }); }} ] ); }; const getTotalOutstanding = () => { return bills .filter(bill => bill.status === 'pending' || bill.status === 'overdue') .reduce((total, bill) => total + bill.patient_portion, 0); }; const renderBillsTab = () => { const filteredBills = getFilteredBills(); const totalOutstanding = getTotalOutstanding(); return ( <View> {/* Summary Card */} <Card className="p-4 mb-4"> <Text className={`text-lg font-semibold mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('billing.summary')} </Text> <View className={`flex-row justify-between ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.totalOutstanding')} </Text> <Text className={`font-bold text-lg ${totalOutstanding > 0 ? 'text-rose-700 dark:text-rose-400' : 'text-emerald-700 dark:text-emerald-400'}`}> {totalOutstanding} SAR </Text> </View> </Card> {/* Filter Tabs */} <View className="mb-4"> <ScrollView horizontal showsHorizontalScrollIndicator={false}> <View className={`flex-row space-x-3 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> {[ { key: 'all', label: t('common.all') }, { key: 'pending', label: t('billing.pending') }, { key: 'paid', label: t('billing.paid') }, { key: 'overdue', label: t('billing.overdue') } ].map((tab) => ( <TouchableOpacity key={tab.key} onPress={() => setFilter(tab.key as any)} className={`px-4 py-2 rounded-full ${ filter === tab.key ? 'bg-primary-600' : isDark ? 'bg-dark-card border border-dark-border' : 'bg-background border border-border' }`} > <Text className={`font-medium ${ filter === tab.key ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {tab.label} </Text> </TouchableOpacity> ))} </View> </ScrollView> </View> {/* Bills List */} {filteredBills.length === 0 ? ( <View className="flex-1 justify-center items-center py-12"> <Ionicons name="receipt-outline" size={64} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-xl font-bold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('billing.noBills')} </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.noBillsMessage')} </Text> </View> ) : ( <View className="space-y-4"> {filteredBills.map((bill) => ( <Card key={bill.id} className="p-4"> <TouchableOpacity onPress={() => handleBillPress(bill)}> <View className={`flex-row items-start justify-between ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <View className="flex-1"> <View className={`flex-row items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Ionicons name={getStatusIcon(bill.status)} size={20} color={bill.status === 'paid' ? '#10B981' : bill.status === 'pending' ? '#F59E0B' : '#EF4444'} style={{ marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }} /> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {bill.service_description} </Text> </View> <Text className={`text-sm mb-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.billNumber')}: {bill.bill_number} </Text> <Text className={`text-sm mb-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.department')}: {bill.department} </Text> {bill.doctor_name && ( <Text className={`text-sm mb-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.doctor')}: {bill.doctor_name} </Text> )} <Text className={`text-sm mb-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.dueDate')}: {new Date(bill.due_date).toLocaleDateString()} </Text> <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <Text className={`font-bold text-lg ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {bill.patient_portion} {bill.currency} </Text> {bill.insurance_covered && bill.insurance_covered > 0 && ( <Text className={`text-sm ml-2 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> ({t('billing.insuranceCovered')}: {bill.insurance_covered} {bill.currency}) </Text> )} </View> </View> <View className={`items-end ${isRTL ? 'items-start' : ''}`}> <Badge variant={getStatusColor(bill.status)} className="mb-2" > {t(`billing.status.${bill.status}`)} </Badge> {(bill.status === 'pending' || bill.status === 'overdue') && ( <TouchableOpacity onPress={() => handlePayBill(bill)} className="bg-primary-600 px-4 py-2 rounded-lg" > <Text className="text-white text-sm font-medium"> {t('billing.payNow')} </Text> </TouchableOpacity> )} </View> </View> </TouchableOpacity> </Card> ))} </View> )} </View> ); }; const renderPaymentsTab = () => ( <View> {paymentHistory.length === 0 ? ( <View className="flex-1 justify-center items-center py-12"> <Ionicons name="card-outline" size={64} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-xl font-bold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('billing.noPayments')} </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.noPaymentsMessage')} </Text> </View> ) : ( <View className="space-y-4"> {paymentHistory.map((payment) => ( <Card key={payment.id} className="p-4"> <View className={`flex-row items-center justify-between ${isRTL ? 'flex-row-reverse' : ''} ${isRTL ? 'flex-row-reverse' : ''}`}> <View className="flex-1"> <Text className={`text-lg font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {payment.amount} {payment.currency} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {payment.payment_method} • {new Date(payment.payment_date).toLocaleDateString()} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('billing.transactionId')}: {payment.transaction_id} </Text> </View> <Badge variant={payment.status === 'completed' ? 'success' : 'warning'}> {t(`billing.paymentStatus.${payment.status}`)} </Badge> </View> </Card> ))} </View> )} </View> ); if (isLoading) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <LoadingSpinner /> </SafeAreaView> ); } return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('billing.title')} </Text> <Text className={`text-sm mt-1 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'} ${isRTL ? 'text-right' : 'text-left'}`}> {t('billing.subtitle')} </Text> </View> {/* Tab Navigation */} <View className="px-6 mb-4"> <View className={`flex-row ${isDark ? 'bg-dark-card' : 'bg-background'} rounded-xl p-1 ${isRTL ? 'flex-row-reverse' : ''}`}> <TouchableOpacity onPress={() => setActiveTab('bills')} className={`flex-1 py-3 px-4 rounded-lg ${ activeTab === 'bills' ? 'bg-primary-600' : '' }`} > <Text className={`text-center font-medium ${ activeTab === 'bills' ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('billing.bills')} </Text> </TouchableOpacity> <TouchableOpacity onPress={() => setActiveTab('payments')} className={`flex-1 py-3 px-4 rounded-lg ${ activeTab === 'payments' ? 'bg-primary-600' : '' }`} > <Text className={`text-center font-medium ${ activeTab === 'payments' ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {t('billing.paymentHistory')} </Text> </TouchableOpacity> </View> </View> {/* Content */} <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false} refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } > <View className="pb-8"> {activeTab === 'bills' ? renderBillsTab() : renderPaymentsTab()} </View> </ScrollView> </SafeAreaView> ); }; export default BillingScreen; 