import { User } from './auth'; export interface Patient { id: number; patient_id: string; user: User; // Medical Information blood_group?: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-'; allergies?: string; chronic_conditions?: string; current_medications?: string; // Insurance Information insurance_provider?: string; insurance_policy_number?: string; // Emergency Contact emergency_contact_name?: string; emergency_contact_phone?: string; emergency_contact_relationship?: string; // Timestamps created_at: string; updated_at: string; } export interface MedicalRecord { id: number; patient: number; patient_name: string; doctor: number; doctor_name: string; visit_date: string; diagnosis: string; symptoms: string; treatment: string; prescription: string; notes?: string; follow_up_date?: string; created_at: string; updated_at: string; } export interface PatientState { patients: Patient[]; currentPatient: Patient | null; medicalRecords: MedicalRecord[]; isLoading: boolean; error: string | null; } 