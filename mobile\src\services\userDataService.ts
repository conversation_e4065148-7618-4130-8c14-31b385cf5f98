import { apiService } from './api'; /** * User-specific data service * Ensures each user only sees their own data */ export class UserDataService { /** * Get user-specific dashboard data */ async getDashboardData(userId: number) { try { const [appointments, medicalRecords, notifications] = await Promise.all([ this.getUserAppointments(userId), this.getUserMedicalRecords(userId), this.getUserNotifications(userId), ]); return { appointments: (appointments as any)?.results || [], medicalRecords: (medicalRecords as any)?.results || [], notifications: (notifications as any)?.results || [], stats: { totalAppointments: (appointments as any)?.count || 0, totalRecords: (medicalRecords as any)?.count || 0, unreadNotifications: (notifications as any)?.results?.filter((n: any) => !n.read).length || 0, } }; } catch (error) { console.error('Failed to load dashboard data:', error); throw error; } } /** * Get user's appointments only */ async getUserAppointments(userId: number, page: number = 1, pageSize: number = 20) { return apiService.get('/appointments/my-appointments/', { params: { page, page_size: pageSize } }); } /** * Get user's medical records only */ async getUserMedicalRecords(userId: number, page: number = 1, pageSize: number = 20) { return apiService.get('/patients/my-medical-records/', { params: { page, page_size: pageSize } }); } /** * Get user's notifications only */ async getUserNotifications(userId: number, page: number = 1, pageSize: number = 20) { // This would be a real API endpoint in production return { results: this.getMockUserNotifications(userId), count: this.getMockUserNotifications(userId).length, next: null, previous: null, }; } /** * Get user's profile data */ async getUserProfile(userId: number) { return apiService.get('/patients/my-profile/'); } /** * Get user-specific doctors (based on user's location, preferences, etc.) */ async getUserDoctors(userId: number, filters?: any) { // In a real app, this would filter doctors based on user's location, // insurance, preferences, etc. return this.getMockUserDoctors(userId, filters); } /** * Mock user-specific notifications * In production, this would come from the backend */ private getMockUserNotifications(userId: number) { const baseNotifications = [ { id: 1, title: 'Appointment Reminder', message: 'Your appointment with Dr. Ahmed Hassan is tomorrow at 2:00 PM', type: 'appointment', read: false, timestamp: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), userId: userId, }, { id: 2, title: 'Lab Results Available', message: 'Your blood test results are now available for review', type: 'result', read: false, timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), userId: userId, }, { id: 3, title: 'Medication Reminder', message: 'Time to take your prescribed medication - Aspirin 75mg', type: 'reminder', read: true, timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), userId: userId, }, ]; // Add user-specific variations if (userId === 1) { baseNotifications.push({ id: 4, title: 'Health Tip for You', message: 'Based on your medical history, remember to monitor your blood pressure daily.', type: 'general', read: false, timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), userId: userId, }); } return baseNotifications.filter(n => n.userId === userId); } /** * Mock user-specific doctors */ private getMockUserDoctors(userId: number, filters?: any) { const allDoctors = [ { id: 1, name: 'Dr. Ahmed Hassan', specialization: 'Cardiology', experience: 15, rating: 4.9, reviews: 234, available: true, distance: '2.5 km', acceptsInsurance: true, }, { id: 2, name: 'Dr. Sarah Johnson', specialization: 'Dermatology', experience: 12, rating: 4.8, reviews: 189, available: true, distance: '1.8 km', acceptsInsurance: true, }, { id: 3, name: 'Dr. Michael Chen', specialization: 'Orthopedics', experience: 18, rating: 4.7, reviews: 156, available: false, distance: '3.2 km', acceptsInsurance: false, }, ]; // Filter based on user preferences (mock logic) let filteredDoctors = allDoctors; if (filters?.nearbyOnly) { filteredDoctors = filteredDoctors.filter(d => parseFloat(d.distance) < 3); } if (filters?.insuranceOnly) { filteredDoctors = filteredDoctors.filter(d => d.acceptsInsurance); } if (filters?.availableOnly) { filteredDoctors = filteredDoctors.filter(d => d.available); } return { results: filteredDoctors, count: filteredDoctors.length, next: null, previous: null, }; } /** * Get user's recent activity */ async getUserActivity(userId: number, days: number = 30) { // Mock user activity data return { appointmentsThisMonth: 3, recordsUpdated: 2, medicationsTaken: 28, lastLogin: new Date().toISOString(), healthScore: 85, // Based on user's compliance, appointments, etc. }; } /** * Get user's health insights */ async getUserHealthInsights(userId: number) { // Mock health insights based on user's data return { insights: [ { type: 'positive', title: 'Great Progress!', message: 'You\'ve attended all your appointments this month.', icon: 'checkmark-circle', }, { type: 'reminder', title: 'Health Checkup Due', message: 'It\'s time for your annual health checkup.', icon: 'calendar', }, { type: 'tip', title: 'Stay Hydrated', message: 'Remember to drink 8 glasses of water daily.', icon: 'water', }, ], healthScore: 85, trends: { appointments: 'improving', medication_compliance: 'stable', health_metrics: 'good', } }; } /** * Update user preferences */ async updateUserPreferences(userId: number, preferences: any) { // In production, this would update user preferences in the backend return apiService.put(`/users/${userId}/preferences/`, preferences); } /** * Get user's emergency contacts */ async getUserEmergencyContacts(userId: number) { return apiService.get('/patients/emergency-contacts/'); } /** * Check if user has access to specific data */ validateUserAccess(userId: number, resourceUserId: number): boolean { // Ensure users can only access their own data return userId === resourceUserId; } } export const userDataService = new UserDataService(); 