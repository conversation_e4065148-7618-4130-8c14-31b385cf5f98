import React, { useState, useRef, useEffect } from 'react'; import { ChevronDown, Check } from 'lucide-react'; import { cn } from '../../lib/utils'; interface DropdownOption { value: string; label: string; disabled?: boolean; icon?: React.ReactNode; } interface DropdownProps { options: DropdownOption[]; value?: string; placeholder?: string; onChange: (value: string) => void; disabled?: boolean; className?: string; variant?: 'default' | 'glass' | 'outline'; size?: 'sm' | 'md' | 'lg'; } const Dropdown: React.FC<DropdownProps> = ({ options, value, placeholder = 'Select an option', onChange, disabled = false, className, variant = 'default', size = 'md', }) => { const [isOpen, setIsOpen] = useState(false); const [highlightedIndex, setHighlightedIndex] = useState(-1); const dropdownRef = useRef<HTMLDivElement>(null); const listRef = useRef<HTMLUListElement>(null); const selectedOption = options.find(option => option.value === value); const sizeClasses = { sm: 'h-8 px-2 text-xs', md: 'h-10 px-3 text-sm', lg: 'h-12 px-4 text-base', }; const variantClasses = { default: 'bg-background border border-input hover:bg-accent', glass: 'macos-input', outline: 'bg-transparent border-2 border-border hover:border-primary', }; // Handle click outside useEffect(() => { const handleClickOutside = (event: MouseEvent) => { if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) { setIsOpen(false); setHighlightedIndex(-1); } }; if (isOpen) { document.addEventListener('mousedown', handleClickOutside); } return () => { document.removeEventListener('mousedown', handleClickOutside); }; }, [isOpen]); // Handle keyboard navigation useEffect(() => { const handleKeyDown = (event: KeyboardEvent) => { if (!isOpen) return; switch (event.key) { case 'ArrowDown': event.preventDefault(); setHighlightedIndex(prev => prev < options.length - 1 ? prev + 1 : 0 ); break; case 'ArrowUp': event.preventDefault(); setHighlightedIndex(prev => prev > 0 ? prev - 1 : options.length - 1 ); break; case 'Enter': event.preventDefault(); if (highlightedIndex >= 0 && !options[highlightedIndex].disabled) { onChange(options[highlightedIndex].value); setIsOpen(false); setHighlightedIndex(-1); } break; case 'Escape': setIsOpen(false); setHighlightedIndex(-1); break; } }; if (isOpen) { document.addEventListener('keydown', handleKeyDown); } return () => { document.removeEventListener('keydown', handleKeyDown); }; }, [isOpen, highlightedIndex, options, onChange]); // Scroll highlighted option into view useEffect(() => { if (highlightedIndex >= 0 && listRef.current) { const highlightedElement = listRef.current.children[highlightedIndex] as HTMLElement; if (highlightedElement) { highlightedElement.scrollIntoView({ block: 'nearest', behavior: 'smooth', }); } } }, [highlightedIndex]); const handleToggle = () => { if (!disabled) { setIsOpen(!isOpen); setHighlightedIndex(-1); } }; const handleOptionClick = (option: DropdownOption) => { if (!option.disabled) { onChange(option.value); setIsOpen(false); setHighlightedIndex(-1); } }; return ( <div ref={dropdownRef} className={cn('relative', className)}> {/* Trigger */} <button type="button" onClick={handleToggle} disabled={disabled} className={cn( 'w-full flex items-center justify-between macos-rounded macos-transition macos-focus', sizeClasses[size], variantClasses[variant], disabled && 'opacity-50 cursor-not-allowed', isOpen && 'ring-2 ring-primary ring-offset-2', className )} > <div className="flex items-center gap-2 flex-1 text-left"> {selectedOption?.icon && ( <span className="flex-shrink-0">{selectedOption.icon}</span> )} <span className={cn( 'truncate', selectedOption ? 'macos-text-primary' : 'macos-text-tertiary' )}> {selectedOption?.label || placeholder} </span> </div> <ChevronDown className={cn( 'w-4 h-4 flex-shrink-0 macos-text-tertiary macos-transition', isOpen && 'rotate-180' )} /> </button> {/* Dropdown Menu */} {isOpen && ( <div className="absolute top-full left-0 right-0 z-[9999] mt-1"> <ul ref={listRef} className="macos-dropdown max-h-60 overflow-auto py-1 shadow-2xl border border-border/20" > {options.map((option, index) => ( <li key={option.value}> <button type="button" onClick={() => handleOptionClick(option)} disabled={option.disabled} className={cn( 'w-full flex items-center gap-2 px-3 py-2 text-left text-sm macos-transition', 'hover:bg-primary/10 focus:bg-primary/10', option.disabled && 'opacity-50 cursor-not-allowed', highlightedIndex === index && 'bg-primary/10', option.value === value && 'macos-text-primary font-medium' )} > {option.icon && ( <span className="flex-shrink-0">{option.icon}</span> )} <span className="flex-1 truncate">{option.label}</span> {option.value === value && ( <Check className="w-4 h-4 flex-shrink-0 text-primary" /> )} </button> </li> ))} </ul> </div> )} </div> ); }; // Multi-select dropdown interface MultiSelectProps extends Omit<DropdownProps, 'value' | 'onChange'> { values: string[]; onChange: (values: string[]) => void; maxSelections?: number; } const MultiSelect: React.FC<MultiSelectProps> = ({ options, values, onChange, maxSelections, placeholder = 'Select options', ...props }) => { const selectedOptions = options.filter(option => values.includes(option.value)); const handleToggleOption = (optionValue: string) => { const isSelected = values.includes(optionValue); if (isSelected) { onChange(values.filter(v => v !== optionValue)); } else { if (!maxSelections || values.length < maxSelections) { onChange([...values, optionValue]); } } }; const displayText = selectedOptions.length > 0 ? selectedOptions.length === 1 ? selectedOptions[0].label : `${selectedOptions.length} selected` : placeholder; return ( <Dropdown {...props} options={options.map(option => ({ ...option, disabled: option.disabled || ( maxSelections ? values.length >= maxSelections && !values.includes(option.value) : false ) }))} value={displayText} onChange={handleToggleOption} placeholder={placeholder} /> ); }; export { Dropdown, MultiSelect }; 