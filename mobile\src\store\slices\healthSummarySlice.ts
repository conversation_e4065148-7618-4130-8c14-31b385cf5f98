import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { HealthMetric, VitalSign, HealthAlert, HealthGoal, CreateHealthMetricData, CreateVitalSignData, healthSummaryService } from '../../services/healthSummaryService'; interface HealthSummaryState { healthMetrics: HealthMetric[]; vitalSigns: VitalSign[]; healthAlerts: HealthAlert[]; healthGoals: HealthGoal[]; healthSummary: any | null; isLoading: boolean; error: string | null; } const initialState: HealthSummaryState = { healthMetrics: [], vitalSigns: [], healthAlerts: [], healthGoals: [], healthSummary: null, isLoading: false, error: null, }; // Async thunks export const fetchHealthMetrics = createAsyncThunk( 'healthSummary/fetchHealthMetrics', async (_, { rejectWithValue }) => { try { const response = await healthSummaryService.getHealthMetrics(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch health metrics'); } } ); export const fetchVitalSigns = createAsyncThunk( 'healthSummary/fetchVitalSigns', async (_, { rejectWithValue }) => { try { const response = await healthSummaryService.getVitalSigns(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch vital signs'); } } ); export const fetchHealthAlerts = createAsyncThunk( 'healthSummary/fetchHealthAlerts', async (_, { rejectWithValue }) => { try { const response = await healthSummaryService.getHealthAlerts(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch health alerts'); } } ); export const fetchHealthGoals = createAsyncThunk( 'healthSummary/fetchHealthGoals', async (_, { rejectWithValue }) => { try { const response = await healthSummaryService.getHealthGoals(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch health goals'); } } ); export const fetchHealthSummary = createAsyncThunk( 'healthSummary/fetchHealthSummary', async (_, { rejectWithValue }) => { try { const response = await healthSummaryService.getHealthSummary(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch health summary'); } } ); export const createHealthMetric = createAsyncThunk( 'healthSummary/createHealthMetric', async (metricData: CreateHealthMetricData, { rejectWithValue }) => { try { const response = await healthSummaryService.createHealthMetric(metricData); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to create health metric'); } } ); export const createVitalSign = createAsyncThunk( 'healthSummary/createVitalSign', async (vitalData: CreateVitalSignData, { rejectWithValue }) => { try { const response = await healthSummaryService.createVitalSign(vitalData); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to create vital sign'); } } ); export const markAlertAsRead = createAsyncThunk( 'healthSummary/markAlertAsRead', async (alertId: number, { rejectWithValue }) => { try { const response = await healthSummaryService.markAlertAsRead(alertId); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to mark alert as read'); } } ); const healthSummarySlice = createSlice({ name: 'healthSummary', initialState, reducers: { clearError: (state) => { state.error = null; }, clearHealthSummary: (state) => { state.healthSummary = null; }, }, extraReducers: (builder) => { builder // Fetch health metrics .addCase(fetchHealthMetrics.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchHealthMetrics.fulfilled, (state, action) => { state.isLoading = false; state.healthMetrics = action.payload; }) .addCase(fetchHealthMetrics.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch vital signs .addCase(fetchVitalSigns.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchVitalSigns.fulfilled, (state, action) => { state.isLoading = false; state.vitalSigns = action.payload; }) .addCase(fetchVitalSigns.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch health alerts .addCase(fetchHealthAlerts.fulfilled, (state, action) => { state.healthAlerts = action.payload; }) // Fetch health goals .addCase(fetchHealthGoals.fulfilled, (state, action) => { state.healthGoals = action.payload; }) // Fetch health summary .addCase(fetchHealthSummary.fulfilled, (state, action) => { state.healthSummary = action.payload; }) // Create health metric .addCase(createHealthMetric.fulfilled, (state, action) => { state.healthMetrics.push(action.payload); }) // Create vital sign .addCase(createVitalSign.fulfilled, (state, action) => { state.vitalSigns.push(action.payload); }) // Mark alert as read .addCase(markAlertAsRead.fulfilled, (state, action) => { const index = state.healthAlerts.findIndex(alert => alert.id === action.payload.id); if (index !== -1) { state.healthAlerts[index] = action.payload; } }); }, }); export const { clearError, clearHealthSummary } = healthSummarySlice.actions; export default healthSummarySlice.reducer; 