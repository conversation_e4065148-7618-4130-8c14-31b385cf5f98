import React, { useEffect, useState } from 'react'; import { View, Text, ScrollView, SafeAreaView, TouchableOpacity, Alert } from 'react-native'; import { useRoute, RouteProp, useNavigation } from '@react-navigation/native'; import { useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { MainStackParamList } from '../../navigation/MainNavigator'; import { RootState } from '../../store'; import { DateTimeUtils } from '../../utils/dateTime'; import { medicalRecordService } from '../../services/medicalRecordService'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; import { useTranslation } from '../../hooks/useTranslation'; type MedicalRecordDetailRouteProp = RouteProp<MainStackParamList, 'MedicalRecordDetail'>; const MedicalRecordDetailScreen: React.FC = () => { const route = useRoute(); const navigation = useNavigation(); const { t, isRTL } = useTranslation(); const { isDark } = useTheme(); const { recordId } = route.params; const { medicalRecords } = useSelector((state: RootState) => state.patient); const [record, setRecord] = useState<any>(null); const [loading, setLoading] = useState(true); useEffect(() => { // Find the record from the store const foundRecord = medicalRecords.find(r => r.id === recordId); if (foundRecord) { setRecord(foundRecord); } setLoading(false); }, [recordId, medicalRecords]); const handleDownloadPrescription = () => { Alert.alert( 'Download Prescription', 'Prescription download functionality will be available in the next update.', [{ text: 'OK' }] ); }; const handleShareRecord = () => { Alert.alert( 'Share Record', 'Record sharing functionality will be available in the next update.', [{ text: 'OK' }] ); }; const handleEditRecord = () => { navigation.navigate('EditMedicalRecord' as never, { recordId: recordId }); }; const handleDeleteRecord = () => { Alert.alert( 'Delete Medical Record', 'Are you sure you want to delete this medical record? This action cannot be undone.', [ { text: 'Cancel', style: 'cancel' }, { text: 'Delete', style: 'destructive', onPress: async () => { try { await medicalRecordService.deleteMedicalRecord(recordId); Alert.alert('Success', 'Medical record deleted successfully!', [ { text: 'OK', onPress: () => navigation.goBack() } ]); } catch (error: any) { Alert.alert('Error', error.message || 'Failed to delete medical record'); } } } ] ); }; if (loading) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <LoadingSpinner message="Loading medical record..." /> </SafeAreaView> ); } if (!record) { return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <View className="flex-1 justify-center items-center px-6"> <Ionicons name="document-text-outline" size={64} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-xl font-bold mb-4 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Record Not Found </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> The requested medical record could not be found. </Text> </View> </SafeAreaView> ); } return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <ScrollView className="flex-1 px-6 pt-6" showsVerticalScrollIndicator={false}> {/* Header */} <View className="mb-6"> <Text className={`text-2xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Medical Record </Text> <View className="flex-row items-center mt-2"> <Badge variant="info" size="sm"> {DateTimeUtils.formatDate(record.visit_date)} </Badge> <Text className={`ml-3 text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Record ID: #{record.id} </Text> </View> </View> {/* Doctor Information */} <Card className="mb-4"> <View className="flex-row items-center"> <View className={`w-12 h-12 rounded-full mr-4 items-center justify-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name="person" size={24} color="#007AFF" /> </View> <View className="flex-1"> <Text className={`font-semibold text-lg ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Dr. {record.doctor_name} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Attending Physician </Text> </View> </View> </Card> {/* Diagnosis */} <Card className="mb-4"> <View className="flex-row items-start"> <Ionicons name="medical" size={20} color="#007AFF" style={{ marginRight: 12, marginTop: 2 }} /> <View className="flex-1"> <Text className={`font-semibold text-base mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Diagnosis </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {record.diagnosis} </Text> </View> </View> </Card> {/* Symptoms */} {record.symptoms && ( <Card className="mb-4"> <View className="flex-row items-start"> <Ionicons name="body" size={20} color="#FF9500" style={{ marginRight: 12, marginTop: 2 }} /> <View className="flex-1"> <Text className={`font-semibold text-base mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Symptoms </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {record.symptoms} </Text> </View> </View> </Card> )} {/* Treatment */} {record.treatment && ( <Card className="mb-4"> <View className="flex-row items-start"> <Ionicons name="fitness" size={20} color="#34C759" style={{ marginRight: 12, marginTop: 2 }} /> <View className="flex-1"> <Text className={`font-semibold text-base mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Treatment </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {record.treatment} </Text> </View> </View> </Card> )} {/* Prescription */} {record.prescription && ( <Card className="mb-4"> <View className="flex-row items-start"> <Ionicons name="medical" size={20} color="#FF3B30" style={{ marginRight: 12, marginTop: 2 }} /> <View className="flex-1"> <View className="flex-row justify-between items-center mb-2"> <Text className={`font-semibold text-base ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Prescription </Text> <TouchableOpacity onPress={handleDownloadPrescription} className="flex-row items-center" > <Ionicons name="download-outline" size={16} color="#007AFF" /> <Text className="text-primary-600 text-sm ml-1">Download</Text> </TouchableOpacity> </View> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {record.prescription} </Text> </View> </View> </Card> )} {/* Notes */} {record.notes && ( <Card className="mb-4"> <View className="flex-row items-start"> <Ionicons name="document-text" size={20} color="#8E8E93" style={{ marginRight: 12, marginTop: 2 }} /> <View className="flex-1"> <Text className={`font-semibold text-base mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Doctor's Notes </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {record.notes} </Text> </View> </View> </Card> )} {/* Follow-up */} {record.follow_up_date && ( <Card className="mb-4"> <View className="flex-row items-start"> <Ionicons name="calendar" size={20} color="#FF9500" style={{ marginRight: 12, marginTop: 2 }} /> <View className="flex-1"> <Text className={`font-semibold text-base mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Follow-up Appointment </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Scheduled for {DateTimeUtils.formatDate(record.follow_up_date)} </Text> </View> </View> </Card> )} {/* Record Metadata */} <Card className="mb-6"> <Text className={`font-semibold text-base mb-3 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Record Information </Text> <View className="space-y-2"> <View className="flex-row justify-between"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Visit Date </Text> <Text className={`text-sm ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {DateTimeUtils.formatDate(record.visit_date)} </Text> </View> <View className="flex-row justify-between"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Created </Text> <Text className={`text-sm ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {DateTimeUtils.formatDate(record.created_at)} </Text> </View> <View className="flex-row justify-between"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Last Updated </Text> <Text className={`text-sm ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {DateTimeUtils.formatDate(record.updated_at)} </Text> </View> </View> </Card> {/* Primary Action Buttons */} <View className="flex-row space-x-3 mb-4"> <TouchableOpacity onPress={handleEditRecord} className="flex-1 bg-primary-600 py-3 px-4 rounded-xl" > <View className="flex-row items-center justify-center"> <Ionicons name="create-outline" size={20} color="white" /> <Text className="text-white ml-2 font-medium">Edit Record</Text> </View> </TouchableOpacity> <TouchableOpacity onPress={handleDeleteRecord} className="flex-1 bg-red-600 py-3 px-4 rounded-xl" > <View className="flex-row items-center justify-center"> <Ionicons name="trash-outline" size={20} color="white" /> <Text className="text-white ml-2 font-medium">Delete</Text> </View> </TouchableOpacity> </View> {/* Secondary Action Buttons */} <View className="flex-row space-x-3 mb-8"> <TouchableOpacity onPress={handleShareRecord} className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-xl border ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' } ${isRTL ? 'flex-row-reverse' : ''}`} > <Ionicons name="share-outline" size={20} color="#007AFF" /> <Text className="text-primary-600 ml-2 font-medium">Share</Text> </TouchableOpacity> {record.prescription && ( <TouchableOpacity onPress={handleDownloadPrescription} className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-xl border ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' } ${isRTL ? 'flex-row-reverse' : ''}`} > <Ionicons name="download-outline" size={20} color="#007AFF" /> <Text className="text-primary-600 ml-2 font-medium">Download</Text> </TouchableOpacity> )} </View> </ScrollView> </SafeAreaView> ); }; export default MedicalRecordDetailScreen; 