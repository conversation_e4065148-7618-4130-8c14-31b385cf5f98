import React, { useState } from 'react'; import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ScrollView, Alert, KeyboardAvoidingView, Platform } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import Card from '../../components/ui/Card'; interface EmergencyContactForm { name: string; relationship: string; phone: string; email: string; isPrimary: boolean; } const AddEmergencyContactScreen: React.FC = () => { const navigation = useNavigation(); const { isDark } = useTheme(); const { t, isRTL } = useTranslation(); const [formData, setFormData] = useState<EmergencyContactForm>({ name: '', relationship: '', phone: '', email: '', isPrimary: false }); const [isLoading, setIsLoading] = useState(false); const relationshipOptions = [ 'Spouse', 'Parent', 'Child', 'Sibling', 'Friend', 'Doctor', 'Colleague', 'Other' ]; const handleInputChange = (field: keyof EmergencyContactForm, value: string | boolean) => { setFormData(prev => ({ ...prev, [field]: value })); }; const validateForm = (): boolean => { if (!formData.name.trim()) { Alert.alert(t('common.error'), t('emergency.nameRequired')); return false; } if (!formData.relationship.trim()) { Alert.alert(t('common.error'), t('emergency.relationshipRequired')); return false; } if (!formData.phone.trim()) { Alert.alert(t('common.error'), t('emergency.phoneRequired')); return false; } // Basic phone validation const phoneRegex = /^[\+]?[0-9\-\s\(\)]{10,}$/; if (!phoneRegex.test(formData.phone)) { Alert.alert(t('common.error'), t('emergency.invalidPhone')); return false; } return true; }; const handleSave = async () => { if (!validateForm()) return; setIsLoading(true); try { // TODO: Implement API call to save emergency contact // await emergencyContactService.create(formData); // Simulate API call await new Promise(resolve => setTimeout(resolve, 1000)); Alert.alert( t('common.success'), t('emergency.contactAdded'), [ { text: t('common.ok'), onPress: () => navigation.goBack() } ] ); } catch (error) { Alert.alert(t('common.error'), t('emergency.failedToAddContact')); } finally { setIsLoading(false); } }; return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1" > {/* Header */} <View className="px-6 pt-6 pb-4 flex-row items-center"> <TouchableOpacity onPress={() => navigation.goBack()} className="mr-4" > <Ionicons name={isRTL ? "chevron-forward" : "chevron-back"} size={24} color={isDark ? '#FFFFFF' : '#000000'} /> </TouchableOpacity> <Text className={`text-xl font-bold flex-1 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('emergency.addContact')} </Text> </View> <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}> <Card className="p-6 mb-6"> {/* Name Field */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('emergency.contactName')} * </Text> <TextInput value={formData.name} onChangeText={(value) => handleInputChange('name', value)} placeholder={t('emergency.enterContactName')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} autoCapitalize="words" /> </View> {/* Relationship Field */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('emergency.relationship')} * </Text> <TextInput value={formData.relationship} onChangeText={(value) => handleInputChange('relationship', value)} placeholder={t('emergency.enterRelationship')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} autoCapitalize="words" /> </View> {/* Quick Relationship Options */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('emergency.quickSelect')} </Text> <View className="flex-row flex-wrap"> {relationshipOptions.map((option) => ( <TouchableOpacity key={option} onPress={() => handleInputChange('relationship', option)} className={`px-3 py-2 rounded-full mr-2 mb-2 ${ formData.relationship === option ? 'bg-primary-600' : isDark ? 'bg-dark-muted' : 'bg-muted' }`} > <Text className={`text-sm ${ formData.relationship === option ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {option} </Text> </TouchableOpacity> ))} </View> </View> {/* Phone Field */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('emergency.phoneNumber')} * </Text> <TextInput value={formData.phone} onChangeText={(value) => handleInputChange('phone', value)} placeholder={t('emergency.enterPhoneNumber')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} keyboardType="phone-pad" /> </View> {/* Email Field */} <View className="mb-4"> <Text className={`text-sm font-medium mb-2 ${isRTL ? 'text-right' : 'text-left'} ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('emergency.email')} ({t('common.optional')}) </Text> <TextInput value={formData.email} onChangeText={(value) => handleInputChange('email', value)} placeholder={t('emergency.enterEmail')} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`border rounded-xl px-4 py-4 text-base ${isRTL ? 'text-right' : 'text-left'} ${ isDark ? 'border-dark-border bg-dark-card text-dark-foreground' : 'border-border bg-background text-foreground' }`} keyboardType="email-address" autoCapitalize="none" /> </View> {/* Primary Contact Toggle */} <TouchableOpacity onPress={() => handleInputChange('isPrimary', !formData.isPrimary)} className={`flex-row items-center p-4 rounded-xl ${isRTL ? 'flex-row-reverse' : ''} ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`} > <View className={`w-6 h-6 rounded border-2 mr-3 justify-center items-center ${ formData.isPrimary ? 'bg-primary-600 border-primary-600' : isDark ? 'border-dark-border' : 'border-border' }`}> {formData.isPrimary && ( <Ionicons name="checkmark" size={16} color="white" /> )} </View> <View className="flex-1"> <Text className={`font-medium ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {t('emergency.setPrimary')} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {t('emergency.primaryDescription')} </Text> </View> </TouchableOpacity> </Card> {/* Save Button */} <TouchableOpacity onPress={handleSave} disabled={isLoading} className={`py-4 px-8 rounded-xl shadow-lg mb-8 ${ isLoading ? 'bg-gray-400' : 'bg-primary-600' }`} > <Text className="text-white text-lg font-semibold text-center"> {isLoading ? t('emergency.saving') : t('emergency.saveContact')} </Text> </TouchableOpacity> </ScrollView> </KeyboardAvoidingView> </SafeAreaView> ); }; export default AddEmergencyContactScreen; 