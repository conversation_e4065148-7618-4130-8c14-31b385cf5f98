import React, { useState } from 'react'; import { View, TextInput, Text, TouchableOpacity } from 'react-native'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; interface InputProps { label?: string; placeholder?: string; value: string; onChangeText: (text: string) => void; secureTextEntry?: boolean; keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad'; autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'; autoCorrect?: boolean; editable?: boolean; multiline?: boolean; numberOfLines?: number; error?: string; icon?: keyof typeof Ionicons.glyphMap; rightIcon?: keyof typeof Ionicons.glyphMap; onRightIconPress?: () => void; className?: string; inputClassName?: string; required?: boolean; } const Input: React.FC<InputProps> = ({ label, placeholder, value, onChangeText, secureTextEntry = false, keyboardType = 'default', autoCapitalize = 'sentences', autoCorrect = true, editable = true, multiline = false, numberOfLines = 1, error, icon, rightIcon, onRightIconPress, className = '', inputClassName = '', required = false, }) => { const { isDark } = useTheme(); const [isFocused, setIsFocused] = useState(false); const [showPassword, setShowPassword] = useState(false); const handleRightIconPress = () => { if (secureTextEntry) { setShowPassword(!showPassword); } else if (onRightIconPress) { onRightIconPress(); } }; const getRightIcon = () => { if (secureTextEntry) { return showPassword ? 'eye-off-outline' : 'eye-outline'; } return rightIcon; }; const getInputStyles = () => { const baseStyles = `flex-1 text-base ${isDark ? 'text-dark-foreground' : 'text-foreground'}`; const multilineStyles = multiline ? 'text-top' : ''; return `${baseStyles} ${multilineStyles} ${inputClassName}`; }; const getContainerStyles = () => { const baseStyles = `flex-row items-center border rounded-xl px-4 ${multiline ? 'py-3' : 'py-4'}`; const focusStyles = isFocused ? 'border-primary-600' : ''; const errorStyles = error ? 'border-destructive' : ''; const themeStyles = isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background'; const disabledStyles = !editable ? 'opacity-50' : ''; return `${baseStyles} ${focusStyles} ${errorStyles} ${themeStyles} ${disabledStyles}`; }; return ( <View className={className}> {label && ( <Text className={`text-sm font-medium mb-2 ${ isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {label} {required && <Text className="text-destructive"> *</Text>} </Text> )} <View className={getContainerStyles()}> {icon && ( <Ionicons name={icon} size={20} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginRight: 12 }} /> )} <TextInput value={value} onChangeText={onChangeText} placeholder={placeholder} placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} secureTextEntry={secureTextEntry && !showPassword} keyboardType={keyboardType} autoCapitalize={autoCapitalize} autoCorrect={autoCorrect} editable={editable} multiline={multiline} numberOfLines={numberOfLines} className={getInputStyles()} onFocus={() => setIsFocused(true)} onBlur={() => setIsFocused(false)} /> {(rightIcon || secureTextEntry) && ( <TouchableOpacity onPress={handleRightIconPress} className="ml-2" disabled={!secureTextEntry && !onRightIconPress} > <Ionicons name={getRightIcon()!} size={20} color={isDark ? '#8E8E93' : '#8E8E93'} /> </TouchableOpacity> )} </View> {error && ( <Text className="text-destructive text-sm mt-1"> {error} </Text> )} </View> ); }; export default Input; 