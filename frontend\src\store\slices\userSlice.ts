import { createCrudSlice } from '../../shared/store/createCrudSlice'; import { BaseApiService } from '../../shared/services/BaseApiService'; import type { User } from '../../types/auth'; // Create a user service instance const userService = new BaseApiService<User>({ baseURL: '/users', endpoints: { list: '/users/', detail: '/users/:id/', create: '/users/', update: '/users/:id/', delete: '/users/:id/', }, }); // Create the user slice using the shared CRUD slice factory const userCrud = createCrudSlice<User>('user', userService, { // Additional custom reducers can be added here if needed setSelectedUser: (state, action) => { state.selectedItem = action.payload; }, }); // Export actions and reducer export const { setItems: setUsers, setSelectedItem: setSelectedUser, setLoading, setError, clearError, reset: resetUserState, } = userCrud.actions; export const { fetchAll: fetchUsers, fetchById: fetchUser, create: createUser, update: updateUser, deleteById: deleteUser, } = userCrud.thunks; export default userCrud.reducer; 