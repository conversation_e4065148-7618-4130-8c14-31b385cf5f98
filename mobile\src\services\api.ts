import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'; import { ApiError, PaginatedResponse } from '../types/api'; import { TokenManager } from '../utils/tokenManager'; // API Configuration const API_BASE_URL = __DEV__ ? 'http://127.0.0.1:8000/api' // Django backend URL : 'https://your-production-api.com/api'; class ApiService { private client: AxiosInstance; constructor() { this.client = axios.create({ baseURL: API_BASE_URL, timeout: 10000, headers: { 'Content-Type': 'application/json', }, }); this.setupInterceptors(); } private setupInterceptors() { // Request interceptor to add auth token this.client.interceptors.request.use( async (config) => { const authHeader = await TokenManager.getAuthorizationHeader(); if (authHeader) { config.headers.Authorization = authHeader; } return config; }, (error) => { return Promise.reject(error); } ); // Response interceptor for error handling and token refresh this.client.interceptors.response.use( (response) => response, async (error) => { const originalRequest = error.config; if (error.response?.status === 401 && !originalRequest._retry) { originalRequest._retry = true; try { const refreshToken = await TokenManager.getRefreshToken(); if (refreshToken) { const response = await this.refreshToken(refreshToken); await TokenManager.handleTokenRefresh(response); // Retry original request with new token const authHeader = await TokenManager.getAuthorizationHeader(); if (authHeader) { originalRequest.headers.Authorization = authHeader; } return this.client(originalRequest); } } catch (refreshError) { // Refresh failed, redirect to login await TokenManager.clearTokens(); throw new ApiError('Session expired', 401); } } // Handle other errors const message = error.response?.data?.error || error.response?.data?.message || error.message || 'An error occurred'; throw new ApiError(message, error.response?.status || 500, error.response?.data); } ); } private async refreshToken(refreshToken: string) { const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, { refresh: refreshToken, }); return response.data; } private async clearTokens() { await TokenManager.clearTokens(); } // Generic API methods async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> { const response: AxiosResponse<T> = await this.client.get(url, config); return response.data; } async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> { const response: AxiosResponse<T> = await this.client.post(url, data, config); return response.data; } async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> { const response: AxiosResponse<T> = await this.client.put(url, data, config); return response.data; } async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> { const response: AxiosResponse<T> = await this.client.patch(url, data, config); return response.data; } async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> { const response: AxiosResponse<T> = await this.client.delete(url, config); return response.data; } // Paginated requests async getPaginated<T>(url: string, config?: AxiosRequestConfig): Promise<PaginatedResponse<T>> { return this.get<PaginatedResponse<T>>(url, config); } } export const apiService = new ApiService(); 