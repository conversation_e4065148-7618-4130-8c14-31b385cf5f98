import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { AuthState, LoginCredentials, RegisterData, User } from '../../types/auth'; import { authService } from '../../services/authService'; const initialState: AuthState = { user: null, token: null, refreshToken: null, isAuthenticated: false, isLoading: false, error: null, }; // Async thunks export const login = createAsyncThunk( 'auth/login', async (credentials: LoginCredentials, { rejectWithValue }) => { try { const response = await authService.login(credentials); return response; } catch (error: any) { return rejectWithValue(error.message || 'Login failed'); } } ); export const register = createAsyncThunk( 'auth/register', async (data: RegisterData, { rejectWithValue }) => { try { const response = await authService.register(data); return response; } catch (error: any) { return rejectWithValue(error.message || 'Registration failed'); } } ); export const logout = createAsyncThunk( 'auth/logout', async (_, { rejectWithValue }) => { try { await authService.logout(); } catch (error: any) { return rejectWithValue(error.message || 'Logout failed'); } } ); export const fetchProfile = createAsyncThunk( 'auth/fetchProfile', async (_, { rejectWithValue }) => { try { const user = await authService.getProfile(); return user; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch profile'); } } ); export const updateProfile = createAsyncThunk( 'auth/updateProfile', async (data: Partial<User>, { rejectWithValue }) => { try { const user = await authService.updateProfile(data); return user; } catch (error: any) { return rejectWithValue(error.message || 'Failed to update profile'); } } ); export const checkAuthStatus = createAsyncThunk( 'auth/checkAuthStatus', async (_, { rejectWithValue }) => { try { const isAuthenticated = await authService.isAuthenticated(); if (isAuthenticated) { const user = await authService.getProfile(); const token = await authService.getStoredToken(); const refreshToken = await authService.getStoredRefreshToken(); return { user, token, refreshToken }; } return null; } catch (error: any) { return rejectWithValue(error.message || 'Failed to check auth status'); } } ); const authSlice = createSlice({ name: 'auth', initialState, reducers: { clearError: (state) => { state.error = null; }, setCredentials: (state, action) => { state.user = action.payload.user; state.token = action.payload.token; state.refreshToken = action.payload.refreshToken; state.isAuthenticated = true; }, clearCredentials: (state) => { state.user = null; state.token = null; state.refreshToken = null; state.isAuthenticated = false; }, }, extraReducers: (builder) => { // Login builder .addCase(login.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(login.fulfilled, (state, action) => { state.isLoading = false; state.user = action.payload.user; state.token = action.payload.tokens.access; state.refreshToken = action.payload.tokens.refresh; state.isAuthenticated = true; state.error = null; }) .addCase(login.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; state.isAuthenticated = false; }); // Register builder .addCase(register.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(register.fulfilled, (state, action) => { state.isLoading = false; state.user = action.payload.user; state.token = action.payload.tokens.access; state.refreshToken = action.payload.tokens.refresh; state.isAuthenticated = true; state.error = null; }) .addCase(register.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; state.isAuthenticated = false; }); // Logout builder .addCase(logout.pending, (state) => { state.isLoading = true; }) .addCase(logout.fulfilled, (state) => { state.isLoading = false; state.user = null; state.token = null; state.refreshToken = null; state.isAuthenticated = false; state.error = null; }) .addCase(logout.rejected, (state, action) => { state.isLoading = false; state.user = null; state.token = null; state.refreshToken = null; state.isAuthenticated = false; state.error = action.payload as string; }); // Fetch Profile builder .addCase(fetchProfile.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchProfile.fulfilled, (state, action) => { state.isLoading = false; state.user = action.payload; state.error = null; }) .addCase(fetchProfile.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Update Profile builder .addCase(updateProfile.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(updateProfile.fulfilled, (state, action) => { state.isLoading = false; state.user = action.payload; state.error = null; }) .addCase(updateProfile.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Check Auth Status builder .addCase(checkAuthStatus.pending, (state) => { state.isLoading = true; }) .addCase(checkAuthStatus.fulfilled, (state, action) => { state.isLoading = false; if (action.payload) { state.user = action.payload.user; state.token = action.payload.token; state.refreshToken = action.payload.refreshToken; state.isAuthenticated = true; } else { state.user = null; state.token = null; state.refreshToken = null; state.isAuthenticated = false; } state.error = null; }) .addCase(checkAuthStatus.rejected, (state, action) => { state.isLoading = false; state.user = null; state.token = null; state.refreshToken = null; state.isAuthenticated = false; state.error = action.payload as string; }); }, }); export const { clearError, setCredentials, clearCredentials } = authSlice.actions; export default authSlice.reducer; 