import React, { useState, useEffect } from 'react'; import { View, Text, ScrollView, TouchableOpacity, SafeAreaView, TextInput, RefreshControl, Alert } from 'react-native'; import { useNavigation } from '@react-navigation/native'; import { useDispatch, useSelector } from 'react-redux'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../../contexts/ThemeContext'; import { useTranslation } from '../../hooks/useTranslation'; import { RootState, AppDispatch } from '../../store'; import { fetchDoctors, searchDoctors, fetchDoctorsBySpecialization, fetchSpecializations, setSearchFilters } from '../../store/slices/doctorSlice'; import Card from '../../components/ui/Card'; import Badge from '../../components/ui/Badge'; import LoadingSpinner from '../../components/common/LoadingSpinner'; interface Doctor { id: number; name: string; specialization: string; experience: number; rating: number; reviews: number; available: boolean; image?: string; qualifications: string[]; languages: string[]; consultationFee: number; } const DoctorsListScreen: React.FC = () => { const navigation = useNavigation(); const dispatch = useDispatch<AppDispatch>(); const { t, isRTL } = useTranslation(); const { isDark } = useTheme(); const { doctors, specializations, isLoading, error } = useSelector((state: RootState) => state.doctor); const [filteredDoctors, setFilteredDoctors] = useState<Doctor[]>([]); const [searchQuery, setSearchQuery] = useState(''); const [selectedSpecialization, setSelectedSpecialization] = useState('All'); const [showAvailableOnly, setShowAvailableOnly] = useState(false); const [refreshing, setRefreshing] = useState(false); // Load doctors and specializations on component mount useEffect(() => { loadDoctorsData(); }, [dispatch]); const loadDoctorsData = () => { dispatch(fetchDoctors({ page: 1, pageSize: 20 })); dispatch(fetchSpecializations()); }; // Show error alert if there's an error useEffect(() => { if (error) { Alert.alert(t('common.error'), error); } }, [error, t]); // Filter doctors based on search and filters useEffect(() => { filterDoctors(); }, [searchQuery, selectedSpecialization, showAvailableOnly, doctors]); // Handle search with API call useEffect(() => { const delayedSearch = setTimeout(() => { if (searchQuery.trim()) { const filters = { ...(selectedSpecialization !== 'All' && { specialization: selectedSpecialization }), ...(showAvailableOnly && { availability_date: new Date().toISOString().split('T')[0] }) }; dispatch(searchDoctors({ query: searchQuery, filters })); } else { loadDoctorsData(); } }, 500); return () => clearTimeout(delayedSearch); }, [searchQuery, dispatch]); const filterDoctors = () => { let filtered = doctors; // Filter by specialization (client-side for immediate feedback) if (selectedSpecialization !== 'All') { filtered = filtered.filter(doctor => doctor.specialization === selectedSpecialization); } // Filter by availability (client-side for immediate feedback) if (showAvailableOnly) { filtered = filtered.filter(doctor => doctor.available); } setFilteredDoctors(filtered); }; const onRefresh = async () => { setRefreshing(true); loadDoctorsData(); setRefreshing(false); }; const handleDoctorPress = (doctor: Doctor) => { // Navigate to doctor detail or book appointment console.log('Selected doctor:', doctor.name); }; const renderSearchBar = () => ( <View className="px-6 mb-4"> <View className={`flex-row items-center border rounded-xl px-4 py-3 ${ isDark ? 'border-dark-border bg-dark-card' : 'border-border bg-background' } ${isRTL ? 'flex-row-reverse' : ''}`}> <Ionicons name="search" size={20} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginRight: 12 }} /> <TextInput value={searchQuery} onChangeText={setSearchQuery} placeholder="Search doctors or specializations..." placeholderTextColor={isDark ? '#8E8E93' : '#8E8E93'} className={`flex-1 text-base ${isDark ? 'text-dark-foreground' : 'text-foreground'}`} /> </View> </View> ); const renderFilters = () => ( <View className="px-6 mb-4"> {/* Specializations */} <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-3"> <View className="flex-row space-x-2"> <TouchableOpacity key="All" onPress={() => setSelectedSpecialization('All')} className={`px-4 py-2 rounded-full border ${ selectedSpecialization === 'All' ? 'bg-primary-600 border-primary-600' : isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border' }`} > <Text className={`text-sm font-medium ${ selectedSpecialization === 'All' ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> All </Text> </TouchableOpacity> {specializations.map((spec) => ( <TouchableOpacity key={spec.id} onPress={() => setSelectedSpecialization(spec.name)} className={`px-4 py-2 rounded-full border ${ selectedSpecialization === spec.name ? 'bg-primary-600 border-primary-600' : isDark ? 'bg-dark-card border-dark-border' : 'bg-background border-border' }`} > <Text className={`text-sm font-medium ${ selectedSpecialization === spec.name ? 'text-white' : isDark ? 'text-dark-foreground' : 'text-foreground' }`}> {spec.name} </Text> </TouchableOpacity> ))} </View> </ScrollView> {/* Available Only Toggle */} <TouchableOpacity onPress={() => setShowAvailableOnly(!showAvailableOnly)} className="flex-row items-center" > <View className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${ showAvailableOnly ? 'bg-primary-600 border-primary-600' : isDark ? 'border-dark-border' : 'border-border' }`}> {showAvailableOnly && ( <Ionicons name="checkmark" size={12} color="white" /> )} </View> <Text className={`text-sm ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Show available doctors only </Text> </TouchableOpacity> </View> ); const renderDoctorCard = (doctor: Doctor) => ( <TouchableOpacity key={doctor.id} onPress={() => handleDoctorPress(doctor)} > <Card className={`mb-4 ${!doctor.available ? 'opacity-70' : ''}`}> <View className="flex-row"> <View className={`w-16 h-16 rounded-full mr-4 items-center justify-center ${ isDark ? 'bg-dark-muted' : 'bg-muted' }`}> <Ionicons name="person" size={32} color="#007AFF" /> </View> <View className="flex-1"> <View className="flex-row justify-between items-start mb-2"> <View className="flex-1"> <Text className={`font-semibold text-lg ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> {doctor.name} </Text> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {doctor.specialization} </Text> </View> <Badge variant={doctor.available ? 'success' : 'error'} size="sm" > {doctor.available ? 'Available' : 'Busy'} </Badge> </View> <View className="flex-row items-center mb-2"> <Ionicons name="star" size={16} color="#FFD700" /> <Text className={`text-sm ml-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {doctor.rating} ({doctor.reviews} reviews) </Text> <Text className={`text-sm ml-3 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {doctor.experience} years exp. </Text> </View> <View className="flex-row items-center justify-between"> <Text className={`text-sm ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Languages: {doctor.languages.join(', ')} </Text> <Text className={`font-semibold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> ${doctor.consultationFee} </Text> </View> </View> </View> </Card> </TouchableOpacity> ); return ( <SafeAreaView className={`flex-1 ${isDark ? 'bg-dark-background' : 'bg-background'}`}> {/* Header */} <View className="px-6 pt-6 pb-4"> <Text className={`text-2xl font-bold ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> Find Doctors </Text> <Text className={`text-sm mt-1 ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> {filteredDoctors.length} doctors available </Text> </View> {renderSearchBar()} {renderFilters()} {/* Doctors List */} <ScrollView className="flex-1 px-6" refreshControl={ <RefreshControl refreshing={refreshing} onRefresh={onRefresh} /> } showsVerticalScrollIndicator={false} > {isLoading ? ( <LoadingSpinner message="Loading doctors..." /> ) : filteredDoctors.length > 0 ? ( <View className="pb-6"> {filteredDoctors.map(renderDoctorCard)} </View> ) : ( <View className={`p-6 rounded-xl ${isDark ? 'bg-dark-card' : 'bg-background'} shadow-sm`}> <View className="items-center"> <Ionicons name="people-outline" size={48} color={isDark ? '#8E8E93' : '#8E8E93'} style={{ marginBottom: 16 }} /> <Text className={`text-lg font-medium mb-2 ${isDark ? 'text-dark-foreground' : 'text-foreground'}`}> No doctors found </Text> <Text className={`text-center ${isDark ? 'text-dark-muted-foreground' : 'text-muted-foreground'}`}> Try adjusting your search criteria or filters. </Text> </View> </View> )} </ScrollView> </SafeAreaView> ); }; export default DoctorsListScreen; 