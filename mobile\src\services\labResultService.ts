import { apiService } from './api'; import { PaginatedResponse } from '../types/api'; export interface LabResult { id: number; test_name: string; test_type: string; result_value: string; reference_range: string; unit: string; status: 'normal' | 'abnormal' | 'critical' | 'pending'; test_date: string; doctor_name: string; lab_name: string; notes?: string; report_url?: string; created_at: string; updated_at: string; } export interface LabResultDetail extends LabResult { patient_id: string; doctor_id: number; lab_id: number; test_category: string; interpretation: string; recommendations: string[]; follow_up_required: boolean; follow_up_date?: string; } class LabResultService { async getMyLabResults(page = 1, pageSize = 20): Promise<PaginatedResponse<LabResult>> { return apiService.getPaginated<LabResult>(`/lab-results/my-results/?page=${page}&page_size=${pageSize}`); } async getLabResultById(id: number): Promise<LabResultDetail> { return apiService.get<LabResultDetail>(`/lab-results/results/${id}/`); } async downloadLabReport(id: number): Promise<Blob> { return apiService.get<Blob>(`/lab-results/results/${id}/download/`, { responseType: 'blob' }); } async getLabResultsByDateRange(startDate: string, endDate: string): Promise<LabResult[]> { return apiService.get<LabResult[]>(`/lab-results/my-results/date-range/?start_date=${startDate}&end_date=${endDate}`); } async getLabResultsByStatus(status: string): Promise<LabResult[]> { return apiService.get<LabResult[]>(`/lab-results/my-results/status/${status}/`); } async getLabResultsByTestType(testType: string): Promise<LabResult[]> { return apiService.get<LabResult[]>(`/lab-results/my-results/test-type/${testType}/`); } async requestLabResultCopy(id: number, email: string): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/lab-results/results/${id}/request-copy/`, { email }); } async shareLabResult(id: number, doctorEmail: string, message?: string): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/lab-results/results/${id}/share/`, { doctor_email: doctorEmail, message }); } } export const labResultService = new LabResultService(); 