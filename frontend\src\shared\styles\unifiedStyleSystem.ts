/** * Unified HMS Style System * Consolidates all styling utilities and eliminates duplicate code */ // Base theme-aware color classes using CSS variables export const BASE_COLORS = { // Primary colors primary: 'text-primary', primaryForeground: 'text-primary-foreground', secondary: 'text-secondary', secondaryForeground: 'text-secondary-foreground', // Background colors background: 'bg-background text-foreground', card: 'bg-card text-card-foreground', popover: 'bg-popover text-popover-foreground', // Semantic colors muted: 'text-muted-foreground', accent: 'bg-accent text-accent-foreground', destructive: 'bg-destructive text-destructive-foreground', // Border and input border: 'border-border', input: 'bg-input border-border', ring: 'ring-ring', } as const; // Glassmorphism effects using CSS variables export const GLASS_EFFECTS = { light: 'glass-light', medium: 'glass', heavy: 'glass-heavy', } as const; // Status color system - unified across all components export const STATUS_COLORS = { // Appointment/Task statuses active: 'status-success', inactive: 'text-muted-foreground', pending: 'status-warning', completed: 'status-success', cancelled: 'status-error', scheduled: 'status-info', confirmed: 'status-success', rejected: 'status-error', approved: 'status-success', draft: 'text-muted-foreground', published: 'status-info', archived: 'text-muted-foreground', // Priority levels urgent: 'status-error', high: 'status-error', medium: 'status-warning', low: 'status-success', critical: 'status-error', // Communication types appointment: 'status-info', prescription: 'status-success', lab_result: 'status-info', billing: 'status-warning', alert: 'status-error', reminder: 'status-warning', // General states success: 'status-success', warning: 'status-warning', error: 'status-error', info: 'status-info', } as const; // Role-based styling system export const ROLE_COLORS = { admin: 'bg-purple-100/80 text-purple-800', doctor: 'bg-blue-100/80 text-sky-700 dark:text-sky-400 border /50 dark:border-blue-700/50', nurse: 'status-success', patient: 'bg-card text-card-foreground border border-border', staff: 'bg-muted/80 text-foreground border border-border/50 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-700/50', receptionist: 'bg-teal-100/80 text-teal-800', } as const; // Priority badge system export const PRIORITY_COLORS = { low: 'status-success', medium: 'bg-yellow-100/80 text-amber-700 dark:text-amber-400 border /50 dark:border-yellow-700/50', high: 'bg-orange-100/80 text-orange-800', urgent: 'bg-red-100/80 text-rose-700 dark:text-rose-400 border /50 dark:border-red-700/50', critical: 'bg-red-200/80 text-red-900 border border-red-300/50 dark:bg-red-900/40 dark:text-red-300 dark:border-red-700/50', } as const; // Text size and weight system export const TEXT_STYLES = { heading: { h1: 'text-4xl font-bold text-foreground', h2: 'text-3xl font-semibold text-foreground', h3: 'text-2xl font-semibold text-foreground', h4: 'text-xl font-medium text-foreground', h5: 'text-lg font-medium text-foreground', h6: 'text-base font-medium text-foreground', }, body: { large: 'text-lg text-foreground', default: 'text-base text-foreground', small: 'text-sm text-foreground', xs: 'text-xs text-foreground', }, muted: { large: 'text-lg text-muted-foreground', default: 'text-base text-muted-foreground', small: 'text-sm text-muted-foreground', xs: 'text-xs text-muted-foreground', }, } as const; // Button variants using theme system export const BUTTON_VARIANTS = { default: 'bg-primary text-primary-foreground hover:bg-primary/90', destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90', outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground', secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80', ghost: 'hover:bg-accent hover:text-accent-foreground', link: 'text-primary underline-offset-4 hover:underline', } as const; // Card variants with glassmorphism export const CARD_VARIANTS = { default: 'glass rounded-lg p-6', elevated: 'glass-heavy rounded-lg p-6 shadow-elevated', flat: 'bg-card border border-border rounded-lg p-6', ghost: 'glass-light rounded-lg p-6', } as const; // Utility functions export const getStatusColor = (status: string): string => { return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'text-muted-foreground'; }; export const getRoleColor = (role: string): string => { return ROLE_COLORS[role as keyof typeof ROLE_COLORS] || ROLE_COLORS.patient; }; export const getPriorityColor = (priority: string): string => { return PRIORITY_COLORS[priority as keyof typeof PRIORITY_COLORS] || PRIORITY_COLORS.medium; }; export const getGlassEffect = (intensity: 'light' | 'medium' | 'heavy' = 'medium'): string => { return GLASS_EFFECTS[intensity]; }; export const getTextStyle = (category: 'heading' | 'body' | 'muted', size: string): string => { const styles = TEXT_STYLES[category]; return styles[size as keyof typeof styles] || styles.default; }; export const getButtonVariant = (variant: keyof typeof BUTTON_VARIANTS): string => { return BUTTON_VARIANTS[variant]; }; export const getCardVariant = (variant: keyof typeof CARD_VARIANTS): string => { return CARD_VARIANTS[variant]; }; // Badge component utility export const getBadgeClasses = (variant: 'default' | 'secondary' | 'destructive' | 'outline' = 'default'): string => { const baseClasses = 'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'; const variants = { default: 'bg-primary text-primary-foreground hover:bg-primary/80', secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80', destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/80', outline: 'text-foreground border border-border', }; return `${baseClasses} ${variants[variant]}`; }; // Input component utility export const getInputClasses = (variant: 'default' | 'error' = 'default'): string => { const baseClasses = 'flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'; const variants = { default: 'border-input bg-background', error: 'border-destructive bg-background', }; return `${baseClasses} ${variants[variant]}`; }; // Animation classes export const ANIMATIONS = { fadeIn: 'animate-in fade-in-0 duration-200', fadeOut: 'animate-out fade-out-0 duration-200', slideIn: 'animate-in slide-in-from-bottom-2 duration-200', slideOut: 'animate-out slide-out-to-bottom-2 duration-200', scaleIn: 'animate-in zoom-in-95 duration-200', scaleOut: 'animate-out zoom-out-95 duration-200', } as const; // Layout utilities export const LAYOUT = { container: 'container mx-auto px-4 sm:px-6 lg:px-8', section: 'py-8 sm:py-12 lg:py-16', grid: { cols1: 'grid grid-cols-1 gap-6', cols2: 'grid grid-cols-1 md:grid-cols-2 gap-6', cols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6', cols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6', }, flex: { center: 'flex items-center justify-center', between: 'flex items-center justify-between', start: 'flex items-center justify-start', end: 'flex items-center justify-end', col: 'flex flex-col', colCenter: 'flex flex-col items-center justify-center', }, } as const; // Export all utilities as a single object for easy importing export const UnifiedStyles = { BASE_COLORS, GLASS_EFFECTS, STATUS_COLORS, ROLE_COLORS, PRIORITY_COLORS, TEXT_STYLES, BUTTON_VARIANTS, CARD_VARIANTS, ANIMATIONS, LAYOUT, getStatusColor, getRoleColor, getPriorityColor, getGlassEffect, getTextStyle, getButtonVariant, getCardVariant, getBadgeClasses, getInputClasses, } as const; export default UnifiedStyles; 