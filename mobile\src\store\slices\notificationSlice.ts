import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { Notification, notificationService, CreateNotificationData } from '../../services/notificationService'; interface NotificationState { notifications: Notification[]; unreadCount: number; isLoading: boolean; error: string | null; totalCount: number; hasMore: boolean; preferences: { appointment_reminders: boolean; medication_reminders: boolean; lab_results: boolean; general_updates: boolean; emergency_alerts: boolean; email_notifications: boolean; push_notifications: boolean; } | null; } const initialState: NotificationState = { notifications: [], unreadCount: 0, isLoading: false, error: null, totalCount: 0, hasMore: true, preferences: null, }; // Async thunks export const fetchNotifications = createAsyncThunk( 'notification/fetchNotifications', async ({ page = 1, pageSize = 20 }: { page?: number; pageSize?: number }, { rejectWithValue }) => { try { const response = await notificationService.getMyNotifications(page, pageSize); return { ...response, page }; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch notifications'); } } ); export const fetchUnreadCount = createAsyncThunk( 'notification/fetchUnreadCount', async (_, { rejectWithValue }) => { try { const response = await notificationService.getUnreadCount(); return response.count; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch unread count'); } } ); export const markNotificationAsRead = createAsyncThunk( 'notification/markAsRead', async (id: number, { rejectWithValue }) => { try { const notification = await notificationService.markAsRead(id); return notification; } catch (error: any) { return rejectWithValue(error.message || 'Failed to mark notification as read'); } } ); export const markAllNotificationsAsRead = createAsyncThunk( 'notification/markAllAsRead', async (_, { rejectWithValue }) => { try { await notificationService.markAllAsRead(); return true; } catch (error: any) { return rejectWithValue(error.message || 'Failed to mark all notifications as read'); } } ); export const deleteNotification = createAsyncThunk( 'notification/deleteNotification', async (id: number, { rejectWithValue }) => { try { await notificationService.deleteNotification(id); return id; } catch (error: any) { return rejectWithValue(error.message || 'Failed to delete notification'); } } ); export const deleteAllReadNotifications = createAsyncThunk( 'notification/deleteAllRead', async (_, { rejectWithValue }) => { try { await notificationService.deleteAllRead(); return true; } catch (error: any) { return rejectWithValue(error.message || 'Failed to delete read notifications'); } } ); export const createNotification = createAsyncThunk( 'notification/createNotification', async (data: CreateNotificationData, { rejectWithValue }) => { try { const notification = await notificationService.createNotification(data); return notification; } catch (error: any) { return rejectWithValue(error.message || 'Failed to create notification'); } } ); export const fetchNotificationPreferences = createAsyncThunk( 'notification/fetchPreferences', async (_, { rejectWithValue }) => { try { const preferences = await notificationService.getNotificationPreferences(); return preferences; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch notification preferences'); } } ); export const updateNotificationPreferences = createAsyncThunk( 'notification/updatePreferences', async (preferences: any, { rejectWithValue }) => { try { const updatedPreferences = await notificationService.updateNotificationPreferences(preferences); return updatedPreferences; } catch (error: any) { return rejectWithValue(error.message || 'Failed to update notification preferences'); } } ); export const scheduleReminder = createAsyncThunk( 'notification/scheduleReminder', async (data: any, { rejectWithValue }) => { try { const reminder = await notificationService.scheduleReminder(data); return reminder; } catch (error: any) { return rejectWithValue(error.message || 'Failed to schedule reminder'); } } ); const notificationSlice = createSlice({ name: 'notification', initialState, reducers: { clearError: (state) => { state.error = null; }, resetNotifications: (state) => { state.notifications = []; state.totalCount = 0; state.hasMore = true; }, updateUnreadCount: (state, action) => { state.unreadCount = action.payload; }, }, extraReducers: (builder) => { // Fetch Notifications builder .addCase(fetchNotifications.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchNotifications.fulfilled, (state, action) => { state.isLoading = false; const { results, count, page } = action.payload; if (page === 1) { state.notifications = results; } else { state.notifications = [...state.notifications, ...results]; } state.totalCount = count; state.hasMore = state.notifications.length < count; state.error = null; }) .addCase(fetchNotifications.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }); // Fetch Unread Count builder .addCase(fetchUnreadCount.fulfilled, (state, action) => { state.unreadCount = action.payload; }); // Mark as Read builder .addCase(markNotificationAsRead.fulfilled, (state, action) => { const updatedNotification = action.payload; const index = state.notifications.findIndex(n => n.id === updatedNotification.id); if (index !== -1) { state.notifications[index] = updatedNotification; if (!updatedNotification.read) { state.unreadCount = Math.max(0, state.unreadCount - 1); } } }); // Mark All as Read builder .addCase(markAllNotificationsAsRead.fulfilled, (state) => { state.notifications = state.notifications.map(n => ({ ...n, read: true })); state.unreadCount = 0; }); // Delete Notification builder .addCase(deleteNotification.fulfilled, (state, action) => { const deletedId = action.payload; const deletedNotification = state.notifications.find(n => n.id === deletedId); state.notifications = state.notifications.filter(n => n.id !== deletedId); state.totalCount -= 1; if (deletedNotification && !deletedNotification.read) { state.unreadCount = Math.max(0, state.unreadCount - 1); } }); // Delete All Read builder .addCase(deleteAllReadNotifications.fulfilled, (state) => { const unreadNotifications = state.notifications.filter(n => !n.read); state.notifications = unreadNotifications; state.totalCount = unreadNotifications.length; }); // Create Notification builder .addCase(createNotification.fulfilled, (state, action) => { state.notifications = [action.payload, ...state.notifications]; state.totalCount += 1; if (!action.payload.read) { state.unreadCount += 1; } }); // Fetch Preferences builder .addCase(fetchNotificationPreferences.fulfilled, (state, action) => { state.preferences = action.payload; }); // Update Preferences builder .addCase(updateNotificationPreferences.fulfilled, (state, action) => { state.preferences = action.payload; }); // Schedule Reminder builder .addCase(scheduleReminder.fulfilled, (state, action) => { state.notifications = [action.payload, ...state.notifications]; state.totalCount += 1; }); }, }); export const { clearError, resetNotifications, updateUnreadCount } = notificationSlice.actions; export default notificationSlice.reducer; 