import React, { Component, ErrorInfo, ReactNode } from 'react'; import { View, Text, TouchableOpacity } from 'react-native'; import { Ionicons } from '@expo/vector-icons'; interface Props { children: ReactNode; fallback?: ReactNode; } interface State { hasError: boolean; error?: Error; } class ErrorBoundary extends Component<Props, State> { constructor(props: Props) { super(props); this.state = { hasError: false }; } static getDerivedStateFromError(error: Error): State { return { hasError: true, error }; } componentDidCatch(error: Error, errorInfo: ErrorInfo) { console.error('ErrorBoundary caught an error:', error, errorInfo); } handleRetry = () => { this.setState({ hasError: false, error: undefined }); }; render() { if (this.state.hasError) { if (this.props.fallback) { return this.props.fallback; } return ( <View className="flex-1 justify-center items-center px-6 bg-background"> <View className="items-center"> <View className="w-20 h-20 rounded-full bg-red-100 items-center justify-center mb-6"> <Ionicons name="alert-circle" size={40} color="#EF4444" /> </View> <Text className="text-xl font-bold text-foreground mb-2 text-center"> Something went wrong </Text> <Text className="text-muted-foreground text-center mb-6"> We're sorry, but something unexpected happened. Please try again. </Text> {__DEV__ && this.state.error && ( <View className="bg-red-50 border rounded-lg p-4 mb-6 w-full"> <Text className="text-rose-700 dark:text-rose-400 text-sm font-mono"> {this.state.error.message} </Text> </View> )} <TouchableOpacity onPress={this.handleRetry} className="bg-primary-600 py-3 px-6 rounded-lg" > <Text className="text-white font-semibold">Try Again</Text> </TouchableOpacity> </View> </View> ); } return this.props.children; } } export default ErrorBoundary; 