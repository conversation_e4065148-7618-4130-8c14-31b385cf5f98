import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import type { PayloadAction } from '@reduxjs/toolkit'; import { EmergencyContact, PersonalContact, CreatePersonalContactData, emergencyContactService } from '../../services/emergencyContactService'; interface EmergencyContactState { systemContacts: EmergencyContact[]; personalContacts: PersonalContact[]; currentContact: PersonalContact | null; isLoading: boolean; error: string | null; } const initialState: EmergencyContactState = { systemContacts: [], personalContacts: [], currentContact: null, isLoading: false, error: null, }; // Async thunks export const fetchSystemEmergencyContacts = createAsyncThunk( 'emergencyContact/fetchSystemContacts', async (_, { rejectWithValue }) => { try { const response = await emergencyContactService.getSystemEmergencyContacts(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch system emergency contacts'); } } ); export const fetchMyEmergencyContacts = createAsyncThunk( 'emergencyContact/fetchMyContacts', async (_, { rejectWithValue }) => { try { const response = await emergencyContactService.getMyEmergencyContacts(); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch personal emergency contacts'); } } ); export const fetchPersonalContactById = createAsyncThunk( 'emergencyContact/fetchPersonalContactById', async (id: number, { rejectWithValue }) => { try { const response = await emergencyContactService.getPersonalContactById(id); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to fetch contact details'); } } ); export const createPersonalContact = createAsyncThunk( 'emergencyContact/createPersonalContact', async (contactData: CreatePersonalContactData, { rejectWithValue }) => { try { const response = await emergencyContactService.createPersonalContact(contactData); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to create emergency contact'); } } ); export const updatePersonalContact = createAsyncThunk( 'emergencyContact/updatePersonalContact', async ({ id, contactData }: { id: number; contactData: Partial<CreatePersonalContactData> }, { rejectWithValue }) => { try { const response = await emergencyContactService.updatePersonalContact(id, contactData); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to update emergency contact'); } } ); export const deletePersonalContact = createAsyncThunk( 'emergencyContact/deletePersonalContact', async (id: number, { rejectWithValue }) => { try { await emergencyContactService.deletePersonalContact(id); return id; } catch (error: any) { return rejectWithValue(error.message || 'Failed to delete emergency contact'); } } ); export const setPrimaryContact = createAsyncThunk( 'emergencyContact/setPrimaryContact', async (id: number, { rejectWithValue }) => { try { const response = await emergencyContactService.setPrimaryContact(id); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to set primary contact'); } } ); export const sendEmergencyAlert = createAsyncThunk( 'emergencyContact/sendEmergencyAlert', async ({ contactIds, message }: { contactIds: number[]; message: string }, { rejectWithValue }) => { try { const response = await emergencyContactService.sendEmergencyAlert(contactIds, message); return response; } catch (error: any) { return rejectWithValue(error.message || 'Failed to send emergency alert'); } } ); const emergencyContactSlice = createSlice({ name: 'emergencyContact', initialState, reducers: { clearError: (state) => { state.error = null; }, clearCurrentContact: (state) => { state.currentContact = null; }, }, extraReducers: (builder) => { builder // Fetch system emergency contacts .addCase(fetchSystemEmergencyContacts.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchSystemEmergencyContacts.fulfilled, (state, action) => { state.isLoading = false; state.systemContacts = action.payload; }) .addCase(fetchSystemEmergencyContacts.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch my emergency contacts .addCase(fetchMyEmergencyContacts.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(fetchMyEmergencyContacts.fulfilled, (state, action) => { state.isLoading = false; state.personalContacts = action.payload; }) .addCase(fetchMyEmergencyContacts.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Fetch personal contact by ID .addCase(fetchPersonalContactById.fulfilled, (state, action) => { state.currentContact = action.payload; }) // Create personal contact .addCase(createPersonalContact.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(createPersonalContact.fulfilled, (state, action) => { state.isLoading = false; state.personalContacts.push(action.payload); }) .addCase(createPersonalContact.rejected, (state, action) => { state.isLoading = false; state.error = action.payload as string; }) // Update personal contact .addCase(updatePersonalContact.fulfilled, (state, action) => { const index = state.personalContacts.findIndex(contact => contact.id === action.payload.id); if (index !== -1) { state.personalContacts[index] = action.payload; } if (state.currentContact?.id === action.payload.id) { state.currentContact = action.payload; } }) // Delete personal contact .addCase(deletePersonalContact.fulfilled, (state, action) => { state.personalContacts = state.personalContacts.filter(contact => contact.id !== action.payload); if (state.currentContact?.id === action.payload) { state.currentContact = null; } }) // Set primary contact .addCase(setPrimaryContact.fulfilled, (state, action) => { // Update all contacts to set isPrimary = false state.personalContacts = state.personalContacts.map(contact => ({ ...contact, isPrimary: contact.id === action.payload.id })); }); }, }); export const { clearError, clearCurrentContact } = emergencyContactSlice.actions; export default emergencyContactSlice.reducer; 