import React from 'react'; import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'; import { createNativeStackNavigator } from '@react-navigation/native-stack'; import { Ionicons } from '@expo/vector-icons'; import { useTheme } from '../contexts/ThemeContext'; // Screens import DashboardScreen from '../screens/main/DashboardScreen'; import AppointmentsScreen from '../screens/main/AppointmentsScreen'; import ProfileScreen from '../screens/main/ProfileScreen'; import SettingsScreen from '../screens/main/SettingsScreen'; import AppointmentDetailScreen from '../screens/main/AppointmentDetailScreen'; import BookAppointmentScreen from '../screens/main/BookAppointmentScreen'; import MedicalRecordsScreen from '../screens/main/MedicalRecordsScreen'; import MedicalRecordDetailScreen from '../screens/main/MedicalRecordDetailScreen'; import AddMedicalRecordScreen from '../screens/main/AddMedicalRecordScreen'; import EditMedicalRecordScreen from '../screens/main/EditMedicalRecordScreen'; import DoctorsListScreen from '../screens/main/DoctorsListScreen'; import NotificationsScreen from '../screens/main/NotificationsScreen'; import EditProfileScreen from '../screens/main/EditProfileScreen'; import LabResultsScreen from '../screens/main/LabResultsScreen'; import PrescriptionsScreen from '../screens/main/PrescriptionsScreen'; import EmergencyScreen from '../screens/main/EmergencyScreen'; import AddEmergencyContactScreen from '../screens/main/AddEmergencyContactScreen'; import BillingScreen from '../screens/main/BillingScreen'; import HealthSummaryScreen from '../screens/main/HealthSummaryScreen'; export type MainTabParamList = { Dashboard: undefined; Appointments: undefined; Profile: undefined; Settings: undefined; }; export type MainStackParamList = { MainTabs: undefined; AppointmentDetail: { appointmentId: number }; BookAppointment: undefined; MedicalRecords: undefined; MedicalRecordDetail: { recordId: number }; AddMedicalRecord: undefined; EditMedicalRecord: { recordId: number }; DoctorsList: undefined; Notifications: undefined; EditProfile: undefined; Settings: undefined; LabResults: undefined; LabResultDetail: { resultId: number }; Prescriptions: undefined; PrescriptionDetail: { prescriptionId: number }; Emergency: undefined; AddEmergencyContact: undefined; Billing: undefined; BillDetail: { billId: number }; PaymentMethod: { billId: number }; HealthSummary: undefined; }; const Tab = createBottomTabNavigator(); const Stack = createNativeStackNavigator(); const MainTabs: React.FC = () => { const { isDark } = useTheme(); return ( <Tab.Navigator screenOptions={({ route }) => ({ tabBarIcon: ({ focused, color, size }) => { let iconName: keyof typeof Ionicons.glyphMap; if (route.name === 'Dashboard') { iconName = focused ? 'home' : 'home-outline'; } else if (route.name === 'Appointments') { iconName = focused ? 'calendar' : 'calendar-outline'; } else if (route.name === 'MedicalRecords') { iconName = focused ? 'document-text' : 'document-text-outline'; } else if (route.name === 'DoctorsList') { iconName = focused ? 'people' : 'people-outline'; } else if (route.name === 'HealthSummary') { iconName = focused ? 'heart' : 'heart-outline'; } else if (route.name === 'Profile') { iconName = focused ? 'person' : 'person-outline'; } else { iconName = 'help-outline'; } return <Ionicons name={iconName} size={size} color={color} />; }, tabBarActiveTintColor: '#007AFF', tabBarInactiveTintColor: isDark ? '#8E8E93' : '#8E8E93', tabBarStyle: { backgroundColor: isDark ? '#1C1C1E' : '#FFFFFF', borderTopColor: isDark ? '#38383A' : '#E5E5E7', borderTopWidth: 0.5, paddingBottom: 8, paddingTop: 8, height: 88, }, tabBarLabelStyle: { fontSize: 12, fontWeight: '500', }, headerShown: false, })} > <Tab.Screen name="Dashboard" component={DashboardScreen} options={{ tabBarLabel: 'Home' }} /> <Tab.Screen name="Appointments" component={AppointmentsScreen} options={{ tabBarLabel: 'Appointments' }} /> <Tab.Screen name="MedicalRecords" component={MedicalRecordsScreen} options={{ tabBarLabel: 'Records' }} /> <Tab.Screen name="DoctorsList" component={DoctorsListScreen} options={{ tabBarLabel: 'Doctors' }} /> <Tab.Screen name="HealthSummary" component={HealthSummaryScreen} options={{ tabBarLabel: 'Health' }} /> <Tab.Screen name="Profile" component={ProfileScreen} options={{ tabBarLabel: 'Profile' }} /> </Tab.Navigator> ); }; const MainNavigator: React.FC = () => { return ( <Stack.Navigator screenOptions={{ headerShown: false, gestureEnabled: true, animation: 'slide_from_right', }} > <Stack.Screen name="MainTabs" component={MainTabs} /> <Stack.Screen name="AppointmentDetail" component={AppointmentDetailScreen} options={{ headerShown: true, title: 'Appointment Details', }} /> <Stack.Screen name="BookAppointment" component={BookAppointmentScreen} options={{ headerShown: true, title: 'Book Appointment', }} /> <Stack.Screen name="MedicalRecordDetail" component={MedicalRecordDetailScreen} options={{ headerShown: true, title: 'Medical Record', }} /> <Stack.Screen name="AddMedicalRecord" component={AddMedicalRecordScreen} options={{ headerShown: true, title: 'Add Medical Record', }} /> <Stack.Screen name="EditMedicalRecord" component={EditMedicalRecordScreen} options={{ headerShown: true, title: 'Edit Medical Record', }} /> <Stack.Screen name="EditProfile" component={EditProfileScreen} options={{ headerShown: true, title: 'Edit Profile', }} /> <Stack.Screen name="Settings" component={SettingsScreen} options={{ headerShown: true, title: 'Settings', }} /> <Stack.Screen name="Notifications" component={NotificationsScreen} options={{ headerShown: true, title: 'Notifications', }} /> <Stack.Screen name="LabResults" component={LabResultsScreen} options={{ headerShown: true, title: 'Lab Results', }} /> <Stack.Screen name="Prescriptions" component={PrescriptionsScreen} options={{ headerShown: true, title: 'Prescriptions', }} /> <Stack.Screen name="Billing" component={BillingScreen} options={{ headerShown: true, title: 'Billing', }} /> <Stack.Screen name="Emergency" component={EmergencyScreen} options={{ headerShown: true, title: 'Emergency', }} /> <Stack.Screen name="AddEmergencyContact" component={AddEmergencyContactScreen} options={{ headerShown: false, }} /> </Stack.Navigator> ); }; export default MainNavigator; 