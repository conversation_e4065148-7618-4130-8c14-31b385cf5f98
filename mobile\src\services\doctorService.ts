import { apiService } from './api'; import { PaginatedResponse } from '../types/api'; export interface Doctor { id: number; name: string; specialization: string; experience: number; rating: number; reviews: number; available: boolean; qualifications: string[]; languages: string[]; consultationFee: number; profile_image?: string; bio?: string; hospital_affiliations: string[]; working_hours: { [key: string]: { start: string; end: string; available: boolean; }; }; created_at: string; updated_at: string; } export interface DoctorDetail extends Doctor { education: Array<{ degree: string; institution: string; year: number; }>; certifications: Array<{ name: string; issuer: string; year: number; }>; research_papers: Array<{ title: string; journal: string; year: number; }>; awards: Array<{ title: string; year: number; }>; patient_reviews: Array<{ id: number; rating: number; comment: string; date: string; patient_name: string; }>; } export interface DoctorAvailability { doctor_id: number; date: string; available_slots: Array<{ start_time: string; end_time: string; is_available: boolean; }>; } export interface DoctorSearchFilters { specialization?: string; location?: string; availability_date?: string; min_rating?: number; max_fee?: number; languages?: string[]; experience_years?: number; } class DoctorService { async getDoctors(page = 1, pageSize = 20, filters?: DoctorSearchFilters): Promise<PaginatedResponse<Doctor>> { const params = new URLSearchParams(); params.append('page', page.toString()); params.append('page_size', pageSize.toString()); if (filters) { Object.entries(filters).forEach(([key, value]) => { if (value !== undefined && value !== null) { if (Array.isArray(value)) { value.forEach(v => params.append(key, v.toString())); } else { params.append(key, value.toString()); } } }); } return apiService.getPaginated<Doctor>(`/doctors/doctors/?${params.toString()}`); } async getDoctorById(id: number): Promise<DoctorDetail> { return apiService.get<DoctorDetail>(`/doctors/doctors/${id}/`); } async searchDoctors(query: string, filters?: DoctorSearchFilters): Promise<Doctor[]> { const params = new URLSearchParams(); params.append('q', query); if (filters) { Object.entries(filters).forEach(([key, value]) => { if (value !== undefined && value !== null) { if (Array.isArray(value)) { value.forEach(v => params.append(key, v.toString())); } else { params.append(key, value.toString()); } } }); } return apiService.get<Doctor[]>(`/doctors/search/?${params.toString()}`); } async getDoctorsBySpecialization(specialization: string): Promise<Doctor[]> { return apiService.get<Doctor[]>(`/doctors/specialization/${specialization}/`); } async getDoctorAvailability(doctorId: number, date: string): Promise<DoctorAvailability> { return apiService.get<DoctorAvailability>(`/doctors/doctors/${doctorId}/availability/?date=${date}`); } async getDoctorAvailabilityRange(doctorId: number, startDate: string, endDate: string): Promise<DoctorAvailability[]> { return apiService.get<DoctorAvailability[]>(`/doctors/doctors/${doctorId}/availability-range/?start_date=${startDate}&end_date=${endDate}`); } async getSpecializations(): Promise<Array<{ id: number; name: string; description: string }>> { return apiService.get<Array<{ id: number; name: string; description: string }>>(`/doctors/specializations/`); } async getFeaturedDoctors(): Promise<Doctor[]> { return apiService.get<Doctor[]>(`/doctors/featured/`); } async getTopRatedDoctors(limit = 10): Promise<Doctor[]> { return apiService.get<Doctor[]>(`/doctors/top-rated/?limit=${limit}`); } async getNearbyDoctors(latitude: number, longitude: number, radius = 10): Promise<Doctor[]> { return apiService.get<Doctor[]>(`/doctors/nearby/?lat=${latitude}&lng=${longitude}&radius=${radius}`); } async rateDoctorAppointment(appointmentId: number, rating: number, comment?: string): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/doctors/rate-appointment/`, { appointment_id: appointmentId, rating, comment }); } async reportDoctor(doctorId: number, reason: string, description: string): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/doctors/doctors/${doctorId}/report/`, { reason, description }); } async addDoctorToFavorites(doctorId: number): Promise<{ message: string }> { return apiService.post<{ message: string }>(`/doctors/doctors/${doctorId}/favorite/`); } async removeDoctorFromFavorites(doctorId: number): Promise<{ message: string }> { return apiService.delete<{ message: string }>(`/doctors/doctors/${doctorId}/favorite/`); } async getFavoriteDoctors(): Promise<Doctor[]> { return apiService.get<Doctor[]>(`/doctors/my-favorites/`); } } export const doctorService = new DoctorService(); 