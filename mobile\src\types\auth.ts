export interface User { id: number; username: string; email: string; first_name: string; last_name: string; full_name: string; role: 'admin' | 'doctor' | 'nurse' | 'patient' | 'receptionist'; phone_number?: string; date_of_birth?: string; address?: string; emergency_contact?: string; profile_picture?: string; is_active: boolean; is_staff: boolean; date_joined: string; last_login?: string; created_at: string; updated_at: string; // Role-based properties is_admin: boolean; is_doctor: boolean; is_nurse: boolean; is_patient: boolean; is_receptionist: boolean; } export interface LoginCredentials { username: string; password: string; } export interface RegisterData { username: string; email: string; password: string; password_confirm: string; first_name: string; last_name: string; role: 'admin' | 'doctor' | 'nurse' | 'patient' | 'receptionist'; phone_number?: string; date_of_birth?: string; address?: string; emergency_contact?: string; } export interface AuthResponse { message: string; user: User; tokens: { access: string; refresh: string; }; } export interface TokenRefreshResponse { access: string; } export interface AuthState { user: User | null; token: string | null; refreshToken: string | null; isAuthenticated: boolean; isLoading: boolean; error: string | null; } 