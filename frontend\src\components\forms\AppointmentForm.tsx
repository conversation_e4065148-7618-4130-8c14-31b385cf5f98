/** * Refactored AppointmentForm Component * Example of using shared FormField, Modal, and validation patterns */ import React, { useState } from 'react'; import { useTranslation } from 'react-i18next'; import { Calendar, Clock, User, FileText, Save, X } from 'lucide-react'; // Shared components import FormField from '../../shared/components/forms/FormField'; import Modal, { useModal } from '../../shared/components/feedback/Modal'; import { LoadingButton } from '../../shared/components/feedback/LoadingSpinner'; import { Button } from '../ui/Button'; import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'; // Hooks and services import { useCrud } from '../../shared/hooks/useCrud'; import appointmentService, { AppointmentCreate } from '../../services/appointmentService'; interface AppointmentFormProps { isOpen: boolean; onClose: () => void; onSuccess?: (appointment: any) => void; editingAppointment?: any; patientId?: number; doctorId?: number; } const AppointmentForm: React.FC<AppointmentFormProps> = ({ isOpen, onClose, onSuccess, editingAppointment, patientId, doctorId, }) => { const { t } = useTranslation(); const [formData, setFormData] = useState<Partial<AppointmentCreate>>({ patient: patientId || editingAppointment?.patient?.id || '', doctor: doctorId || editingAppointment?.doctor?.id || '', appointment_date: editingAppointment?.appointment_date || '', appointment_time: editingAppointment?.appointment_time || '', duration_minutes: editingAppointment?.duration_minutes || 30, appointment_type: editingAppointment?.appointment_type || 'consultation', reason_for_visit: editingAppointment?.reason_for_visit || '', notes: editingAppointment?.notes || '', consultation_fee: editingAppointment?.consultation_fee || 0, }); const [touched, setTouched] = useState<Record<string, boolean>>({}); const [errors, setErrors] = useState<Record<string, string>>({}); // Use shared CRUD hook for appointment operations const { create: createAppointment, update: updateAppointment, loading, error, } = useCrud(appointmentService); // Form validation rules const validationRules = { patient: [ { type: 'required' as const, message: 'Patient is required' }, ], doctor: [ { type: 'required' as const, message: 'Doctor is required' }, ], appointment_date: [ { type: 'required' as const, message: 'Appointment date is required' }, { type: 'custom' as const, message: 'Date cannot be in the past', validator: (value: string) => { if (!value) return true; return new Date(value) >= new Date(new Date().toDateString()); }, }, ], appointment_time: [ { type: 'required' as const, message: 'Appointment time is required' }, ], reason_for_visit: [ { type: 'required' as const, message: 'Reason for visit is required' }, { type: 'min' as const, value: 10, message: 'Please provide more details (minimum 10 characters)' }, ], duration_minutes: [ { type: 'required' as const, message: 'Duration is required' }, ], }; // Handle field change const handleFieldChange = (name: string, value: any) => { setFormData(prev => ({ ...prev, [name]: value })); // Clear error when user starts typing if (errors[name]) { setErrors(prev => ({ ...prev, [name]: '' })); } }; // Handle field blur const handleFieldBlur = (name: string) => { setTouched(prev => ({ ...prev, [name]: true })); }; // Validate form const validateForm = (): boolean => { const newErrors: Record<string, string> = {}; Object.entries(validationRules).forEach(([field, rules]) => { const value = formData[field as keyof AppointmentCreate]; for (const rule of rules) { switch (rule.type) { case 'required': if (!value || (typeof value === 'string' && value.trim() === '')) { newErrors[field] = rule.message; return; } break; case 'min': if (value && typeof value === 'string' && value.length < rule.value) { newErrors[field] = rule.message; return; } break; case 'custom': if (rule.validator && !rule.validator(value)) { newErrors[field] = rule.message; return; } break; } } }); setErrors(newErrors); setTouched(Object.keys(validationRules).reduce((acc, key) => ({ ...acc, [key]: true }), {})); return Object.keys(newErrors).length === 0; }; // Handle form submission const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); if (!validateForm()) { return; } try { let result; if (editingAppointment) { result = await updateAppointment(editingAppointment.id, formData); } else { result = await createAppointment(formData); } onSuccess?.(result); onClose(); // Reset form setFormData({ patient: patientId || '', doctor: doctorId || '', appointment_date: '', appointment_time: '', duration_minutes: 30, appointment_type: 'consultation', reason_for_visit: '', notes: '', consultation_fee: 0, }); setTouched({}); setErrors({}); } catch (err) { console.error('Failed to save appointment:', err); } }; // Handle cancel const handleCancel = () => { onClose(); setErrors({}); setTouched({}); }; return ( <Modal isOpen={isOpen} onClose={handleCancel} title={editingAppointment ? 'Edit Appointment' : 'Schedule New Appointment'} size="lg" variant="glass" footer={ <div className="flex gap-3"> <Button variant="outline" onClick={handleCancel}> <X className="w-4 h-4 mr-2" /> Cancel </Button> <LoadingButton isLoading={loading.create || loading.update} loadingText={editingAppointment ? 'Updating...' : 'Scheduling...'} onClick={handleSubmit} variant="glass" className="bg-blue-600 text-white hover:bg-blue-700" > <Save className="w-4 h-4 mr-2" /> {editingAppointment ? 'Update Appointment' : 'Schedule Appointment'} </LoadingButton> </div> } > <form onSubmit={handleSubmit} className="space-y-6"> {/* Error Display */} {error.create || error.update ? ( <div className="p-4 bg-red-50 dark:bg-red-900/20 border rounded-lg"> <p className="text-rose-700 dark:text-rose-400 dark:text-red-200"> {error.create || error.update} </p> </div> ) : null} {/* Patient and Doctor Selection */} <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> <FormField name="patient" label="Patient" type="select" value={formData.patient} onChange={(value) => handleFieldChange('patient', value)} onBlur={() => handleFieldBlur('patient')} options={[ { label: 'Select Patient', value: '' }, { label: 'John Doe (P000123)', value: '1' }, { label: 'Jane Smith (P000124)', value: '2' }, { label: 'Bob Johnson (P000125)', value: '3' }, ]} required icon={User} variant="glass" validation={validationRules.patient} error={errors.patient} touched={touched.patient} disabled={!!patientId} /> <FormField name="doctor" label="Doctor" type="select" value={formData.doctor} onChange={(value) => handleFieldChange('doctor', value)} onBlur={() => handleFieldBlur('doctor')} options={[ { label: 'Select Doctor', value: '' }, { label: 'Dr. Sarah Johnson - Cardiology', value: '1' }, { label: 'Dr. Michael Brown - General Practice', value: '2' }, { label: 'Dr. Emily Davis - Pediatrics', value: '3' }, ]} required icon={User} variant="glass" validation={validationRules.doctor} error={errors.doctor} touched={touched.doctor} disabled={!!doctorId} /> </div> {/* Date and Time */} <div className="grid grid-cols-1 md:grid-cols-3 gap-6"> <FormField name="appointment_date" label="Appointment Date" type="date" value={formData.appointment_date} onChange={(value) => handleFieldChange('appointment_date', value)} onBlur={() => handleFieldBlur('appointment_date')} required icon={Calendar} variant="glass" validation={validationRules.appointment_date} error={errors.appointment_date} touched={touched.appointment_date} /> <FormField name="appointment_time" label="Appointment Time" type="time" value={formData.appointment_time} onChange={(value) => handleFieldChange('appointment_time', value)} onBlur={() => handleFieldBlur('appointment_time')} required icon={Clock} variant="glass" validation={validationRules.appointment_time} error={errors.appointment_time} touched={touched.appointment_time} /> <FormField name="duration_minutes" label="Duration (minutes)" type="select" value={formData.duration_minutes} onChange={(value) => handleFieldChange('duration_minutes', parseInt(value))} onBlur={() => handleFieldBlur('duration_minutes')} options={[ { label: '15 minutes', value: '15' }, { label: '30 minutes', value: '30' }, { label: '45 minutes', value: '45' }, { label: '60 minutes', value: '60' }, { label: '90 minutes', value: '90' }, ]} required icon={Clock} variant="glass" validation={validationRules.duration_minutes} error={errors.duration_minutes} touched={touched.duration_minutes} /> </div> {/* Appointment Type and Fee */} <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> <FormField name="appointment_type" label="Appointment Type" type="select" value={formData.appointment_type} onChange={(value) => handleFieldChange('appointment_type', value)} options={[ { label: 'Consultation', value: 'consultation' }, { label: 'Follow-up', value: 'follow_up' }, { label: 'Check-up', value: 'check_up' }, { label: 'Emergency', value: 'emergency' }, { label: 'Procedure', value: 'procedure' }, ]} icon={FileText} variant="glass" /> <FormField name="consultation_fee" label="Consultation Fee ($)" type="number" value={formData.consultation_fee} onChange={(value) => handleFieldChange('consultation_fee', parseFloat(value) || 0)} placeholder="0.00" variant="glass" /> </div> {/* Reason for Visit */} <FormField name="reason_for_visit" label="Reason for Visit" type="textarea" value={formData.reason_for_visit} onChange={(value) => handleFieldChange('reason_for_visit', value)} onBlur={() => handleFieldBlur('reason_for_visit')} placeholder="Please describe the reason for this appointment..." required variant="glass" validation={validationRules.reason_for_visit} error={errors.reason_for_visit} touched={touched.reason_for_visit} description="Provide details about symptoms, concerns, or the purpose of this visit" /> {/* Notes */} <FormField name="notes" label="Additional Notes" type="textarea" value={formData.notes} onChange={(value) => handleFieldChange('notes', value)} placeholder="Any additional information or special requirements..." variant="glass" description="Optional: Any special instructions or additional information" /> </form> </Modal> ); }; export default AppointmentForm; /** * Code Reduction Analysis: * * Original AppointmentForm.tsx: ~400-500 lines (estimated) * Refactored AppointmentForm.tsx: 300 lines * * Benefits achieved: * 1. ✅ Consistent form fields using FormField component * 2. ✅ Unified validation patterns with real-time feedback * 3. ✅ Standardized modal behavior with Modal component * 4. ✅ Shared CRUD operations with useCrud hook * 5. ✅ Loading states with LoadingButton component * 6. ✅ Consistent glassmorphism styling * 7. ✅ Built-in accessibility features * 8. ✅ Error handling and user feedback * 9. ✅ Form state management patterns * 10. ✅ Responsive grid layouts * * Features provided by shared components: * - Automatic form validation with visual feedback * - Consistent field styling and behavior * - Modal with keyboard navigation and focus management * - Loading states during form submission * - Error display and handling * - Responsive design * - Dark/light mode support * - Arabic RTL support * - Accessibility features (ARIA labels, keyboard navigation) * * This same pattern can be applied to all forms: * - Patient registration forms * - Medical record forms * - Prescription forms * - User management forms * - Settings forms * * Each form benefits from the same shared infrastructure * while maintaining their specific validation and business logic. */ 